import mongoose from 'mongoose';

const messageSchema = new mongoose.Schema({
    conversationId: {
        type: String,
    },
    senderId: {
        type: String
    },
    message: {
        type: String
    },
    read: {
        type: Boolean,
        default: false  
    },
    fileUrl: {
        type: Array 
    }
    
},{ timestamps: true });

export const Message = mongoose.model('Message', messageSchema);
