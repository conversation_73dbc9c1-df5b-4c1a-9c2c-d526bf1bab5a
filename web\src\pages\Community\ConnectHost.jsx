import { ButtonFilled } from "common/buttons/buttonFilled";
import { ButtonWithoutBackground } from "common/buttons/buttonWithoutBackgourd";
import { TextEditor } from "common/textEditor";
import React, { useRef, useState } from "react";
import { useMutation } from "react-query";
import { toast } from "react-toastify";
import { localStorageData } from "services/auth/localStorageData";
import ErrorService from "services/formatError/ErrorService";
import userServices from "services/httpService/userAuth/userServices";

const ConnectHost = ({ setStartConversation }) => {
  const editorRef = useRef(null);
  const [title, settitle] = useState("");
  const [editorContent, setEditorContent] = useState("");
  const [subject, setSubject] = useState("");

  //handle navigation back from the conversation
  const handleBack = () => {
    setStartConversation(false);
  };

  //set the title based on the selected value
  const handlePClick = (value) => {
    settitle(value);
  };

  // Mutation hook to post community data
  const { mutate: communityData, isLoading } = useMutation(
    (token) => userServices.communityhost("/post/communityPost", token),
    {
      onError: (error) => {
        toast.error(ErrorService.uniformError(error));
      },
      onSuccess: (data) => {
        if (data.data?.status) {
          setStartConversation(false);
        }
        toast.success("your data post successfully");
      },
    }
  );

  // Function to handle posting data
  const log = () => {
    if (editorRef.current) {
      const textEditorContent = editorRef.current.getContent();
      setEditorContent(textEditorContent);

      // Call the mutation function to post the community data
      communityData({
        subject,
        userId: localStorageData("_id"),
        description: textEditorContent,
        category: title,
      });
    }
  };
  return (
    <>
      <div className="lg:w-full flex justify-center mb-10">
        <div className="w-[80%] mt-8">
          <p className="text-4xl font-medium leading-relaxed font-avenir">
            Start a conversation
          </p>

          <p className="textCommonPrp text-2xl mt-[35px]">
            1: What would you like to discuss?
          </p>
          <input
            className="w-full h-10 sm:w-[511.52px] sm:h-[50px] rounded-[10px] mt-[20px] border-2 border-[#AFB5C1] font-lato text-base font-medium pl-5"
            placeholder="Enter Subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
          />
          <div className="h-[272px] w-full rounded-[10px] border-2 border-[#AFB5C1] mt-[24px]">
            <TextEditor editorRef={editorRef} />
          </div>
          <p className="mt-[11px] font-normal font-lato text-[16px] leading-6 text-[#4B4B4B]">
            Hint: @ links to members
          </p>
          <p className="textCommonPrp text-2xl mt-[35px]">
            2: What would you like to discuss?
          </p>
          <div className="flex gap-[15px] mt-5 flex-wrap md:justify-start justify-center">
            {[1, 24, 6, 4, 3].map((item, index) => {
              return (
                <>
                  <div
                    key={index}
                    onClick={() =>
                      handlePClick(` Ask about your listing ${index}`)
                    }
                    className="h-[149.617px] w-[215.93px] border-2 bg-[#f9fcf9] border-[#C1E1C2] rounded-[15px] pl-5"
                  >
                    <div className="w-[53px] h-[53px] mt-4 rounded-full text-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="54"
                        height="55"
                        viewBox="0 0 54 55"
                        fill="none"
                      >
                        <circle
                          opacity="0.1"
                          cx="26.8638"
                          cy="27.4233"
                          r="26.8608"
                          fill="#58C0D0"
                        />
                        <path
                          d="M20.5503 47.5393H33.3622C34.6487 47.5393 35.6916 46.4964 35.6916 45.2099V37.6002L40.8211 36.1967C41.1877 36.0942 41.5109 35.8747 41.7413 35.5716C41.9717 35.2686 42.0968 34.8985 42.0976 34.5178V24.4983L42.4121 24.4103C43.8788 23.9992 44.7345 22.4769 44.3234 21.0102C44.2572 20.7738 44.1596 20.5473 44.0334 20.3367L42.0149 16.959L42.0096 16.9625C41.9358 16.8346 41.8158 16.7398 41.6742 16.6975L27.5858 12.6846C27.1735 12.5687 26.7373 12.5687 26.325 12.6846L12.2377 16.6976C12.0964 16.74 11.9766 16.8348 11.9029 16.9626L11.8976 16.9591L9.87973 20.3368C9.09519 21.6424 9.51762 23.3368 10.8233 24.1214C11.0349 24.2485 11.2627 24.3467 11.5004 24.4133L11.8149 24.5012V34.5178C11.8158 34.8992 11.9416 35.2698 12.1729 35.5731C12.4042 35.8764 12.7285 36.0956 13.0961 36.1973L18.2208 37.6002V45.2099C18.2209 46.4964 19.2638 47.5393 20.5503 47.5393ZM34.527 45.2099C34.527 45.8532 34.0055 46.3746 33.3622 46.3746H20.5503C19.9071 46.3746 19.3856 45.8532 19.3856 45.2099V30.6509C19.3856 30.0077 19.9071 29.4862 20.5503 29.4862H33.3622C34.0055 29.4862 34.527 30.0077 34.527 30.6509V45.2099ZM40.9329 34.5178C40.9321 34.6436 40.8906 34.7657 40.8146 34.866C40.7386 34.9662 40.6322 35.0392 40.5113 35.0739L35.6916 36.3923V30.6509C35.6916 29.3644 34.6487 28.3215 33.3622 28.3215H27.5387V23.4372L29.0959 26.0334C29.3414 26.4419 29.6885 26.7799 30.1033 27.0146C30.5181 27.2493 30.9866 27.3727 31.4632 27.3728C31.7134 27.3731 31.9625 27.3388 32.2034 27.271L40.9329 24.8274V34.5178H40.9329ZM43.1459 22.3464C43.056 22.5739 42.9145 22.7774 42.7326 22.941C42.5507 23.1045 42.3334 23.2235 42.0976 23.2887L31.8889 26.1487C31.5527 26.2424 31.1951 26.2232 30.8709 26.0942C30.5467 25.9651 30.2738 25.7332 30.094 25.4341L27.8479 21.689L41.2422 17.9386L43.0323 20.9348C43.1592 21.1447 43.2355 21.3813 43.2552 21.6258C43.275 21.8703 43.2375 22.116 43.1459 22.3435V22.3464ZM26.6424 13.8062C26.8471 13.7485 27.0637 13.7485 27.2684 13.8062L39.3722 17.2532L26.9563 20.7292L14.5404 17.2532L26.6424 13.8062ZM10.7667 22.3436C10.6754 22.1157 10.6384 21.8696 10.6586 21.6249C10.6788 21.3802 10.7557 21.1436 10.8832 20.9337L12.6687 17.9386L26.063 21.689L23.8168 25.4341C23.6373 25.7334 23.3644 25.9655 23.0401 26.0946C22.7158 26.2237 22.3581 26.2427 22.022 26.1487L11.815 23.2887C11.579 23.2231 11.3614 23.1036 11.1795 22.9396C10.9976 22.7756 10.8563 22.5716 10.7667 22.3436ZM13.4036 35.0768C13.2818 35.0424 13.1745 34.9692 13.098 34.8683C13.0215 34.7675 12.98 34.6444 12.9797 34.5178V24.8273L21.7093 27.2732C22.9053 27.6067 24.1768 27.1008 24.8168 26.0369L26.374 23.4372V28.3215H20.5503C19.2639 28.3215 18.2209 29.3644 18.2209 30.6509V36.3923L13.4036 35.0768Z"
                          fill="#58C0D0"
                        />
                        <path
                          d="M23.6327 31.4038L22.2973 32.7392L22.1267 32.5686C22.0167 32.4633 21.8699 32.4053 21.7176 32.407C21.5654 32.4086 21.4198 32.4699 21.3122 32.5775C21.2045 32.6852 21.1433 32.8307 21.1416 32.983C21.14 33.1352 21.198 33.282 21.3033 33.392L21.8856 33.9744C21.9948 34.0835 22.1429 34.1449 22.2973 34.1449C22.4517 34.1449 22.5998 34.0835 22.709 33.9744L24.4561 32.2273C24.5614 32.1173 24.6194 31.9705 24.6178 31.8182C24.6161 31.666 24.5549 31.5204 24.4472 31.4128C24.3395 31.3051 24.194 31.2439 24.0418 31.2422C23.8895 31.2406 23.7427 31.2986 23.6327 31.4038ZM32.1974 32.398H26.3738C26.2194 32.398 26.0713 32.4593 25.9621 32.5685C25.8528 32.6777 25.7915 32.8259 25.7915 32.9803C25.7915 33.1348 25.8528 33.2829 25.9621 33.3921C26.0713 33.5013 26.2194 33.5627 26.3738 33.5627H32.1974C32.5191 33.5627 32.7798 33.302 32.7798 32.9803C32.7798 32.6587 32.5191 32.398 32.1974 32.398ZM23.6327 34.898L22.2973 36.2334L22.1267 36.0628C22.0167 35.9575 21.8699 35.8995 21.7176 35.9011C21.5654 35.9028 21.4198 35.964 21.3122 36.0717C21.2045 36.1793 21.1433 36.3249 21.1416 36.4771C21.14 36.6294 21.198 36.7762 21.3033 36.8862L21.8856 37.4685C21.9948 37.5777 22.1429 37.639 22.2973 37.639C22.4517 37.639 22.5998 37.5777 22.709 37.4685L24.4561 35.7215C24.5614 35.6115 24.6194 35.4646 24.6178 35.3124C24.6161 35.1601 24.5549 35.0146 24.4472 34.9069C24.3395 34.7993 24.194 34.7381 24.0418 34.7364C23.8895 34.7347 23.7427 34.7927 23.6327 34.898ZM32.1974 35.8921H26.3738C26.2194 35.8921 26.0713 35.9535 25.9621 36.0627C25.8528 36.1719 25.7915 36.32 25.7915 36.4745C25.7915 36.6289 25.8528 36.7771 25.9621 36.8863C26.0713 36.9955 26.2194 37.0568 26.3738 37.0568H32.1974C32.3519 37.0568 32.5 36.9955 32.6092 36.8863C32.7184 36.7771 32.7798 36.6289 32.7798 36.4745C32.7798 36.32 32.7184 36.1719 32.6092 36.0627C32.5 35.9535 32.3519 35.8921 32.1974 35.8921ZM23.6327 38.3922L22.2973 39.7275L22.1267 39.5569C22.0167 39.4517 21.8699 39.3936 21.7176 39.3953C21.5654 39.397 21.4198 39.4582 21.3122 39.5659C21.2045 39.6735 21.1433 39.8191 21.1416 39.9713C21.14 40.1235 21.198 40.2704 21.3033 40.3804L21.8856 40.9627C21.9948 41.0719 22.1429 41.1332 22.2973 41.1332C22.4517 41.1332 22.5998 41.0719 22.709 40.9627L24.4561 39.2156C24.5614 39.1056 24.6194 38.9588 24.6178 38.8066C24.6161 38.6543 24.5549 38.5088 24.4472 38.4011C24.3395 38.2934 24.194 38.2322 24.0418 38.2306C23.8895 38.2289 23.7427 38.2869 23.6327 38.3922ZM32.1974 39.3863H26.3738C26.2194 39.3863 26.0713 39.4477 25.9621 39.5569C25.8528 39.6661 25.7915 39.8142 25.7915 39.9687C25.7915 40.1231 25.8528 40.2712 25.9621 40.3804C26.0713 40.4896 26.2194 40.551 26.3738 40.551H32.1974C32.5191 40.551 32.7798 40.2903 32.7798 39.9687C32.7798 39.647 32.5191 39.3863 32.1974 39.3863ZM23.6327 41.8864L22.2973 43.2217L22.1267 43.0511C22.0167 42.9458 21.8699 42.8878 21.7176 42.8895C21.5654 42.8911 21.4198 42.9524 21.3122 43.06C21.2045 43.1677 21.1433 43.3132 21.1416 43.4655C21.14 43.6177 21.198 43.7646 21.3033 43.8745L21.8856 44.4569C21.9948 44.566 22.1429 44.6274 22.2973 44.6274C22.4517 44.6274 22.5998 44.566 22.709 44.4569L24.4561 42.7098C24.5614 42.5998 24.6194 42.453 24.6178 42.3007C24.6161 42.1485 24.5549 42.0029 24.4472 41.8953C24.3395 41.7876 24.194 41.7264 24.0418 41.7247C23.8895 41.7231 23.7427 41.7811 23.6327 41.8864ZM32.1974 42.8805H26.3738C26.2194 42.8805 26.0713 42.9418 25.9621 43.051C25.8528 43.1603 25.7915 43.3084 25.7915 43.4628C25.7915 43.6173 25.8528 43.7654 25.9621 43.8746C26.0713 43.9838 26.2194 44.0452 26.3738 44.0452H32.1974C32.5191 44.0452 32.7798 43.7845 32.7798 43.4628C32.7798 43.1412 32.5191 42.8805 32.1974 42.8805Z"
                          fill="#58C0D0"
                        />
                      </svg>
                    </div>
                    <p className="textCommonPrp text-black font-avenir text-base mt-8">
                      Ask about your listing
                    </p>
                  </div>
                </>
              );
            })}
          </div>
          <p className="mt-5 font-normal font-lato text-[16px] leading-6 text-[#4B4B4B]">
            Please follow the Guidelines
          </p>
          <div className="mt-8 flex items-center gap-6">
            <div className="w-[100px] h-[48.228px] ">
              <ButtonFilled text="Post" onClick={log} />
            </div>
            <div className="">
              <ButtonWithoutBackground onClick={handleBack} text="Cancle" />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ConnectHost;
