import winston from "winston";

/*
 * Log Level
 * error: 0, warn: 1, info: 2, http: 3, verbose: 4, debug: 5, silly: 6
 */
export const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp({
      format: "YYYY-MM-DD HH:mm:ss",
    }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      level: 'debug',
      handleExceptions: true,
    })
  ],
  exitOnError: false
});

// Create a stream object with a 'write' function that will be used by `morgan`
export const stream = {
  write: (message) => {
    logger.info(message.trim());
  },
};
