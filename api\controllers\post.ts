import { ObjectId } from 'mongoose/node_modules/mongodb';
import mongoose from 'mongoose';
import { Request, Response } from 'express';

import { CommunityPost, Blog,Innovator } from '../model';
import { MulterS3Request } from "../middleware/upload";
import { sendEmail } from '../services/mailJetEmail';

const uploadCommunityPost = async (req: Request, res: Response): Promise<void> => {
  try {

    const {
      subject,
      userId,
      description,
    } = req.body;
    if (!userId || !subject || !description) {
      res.status(400).json({ status: false, message: "please fill all the fields" });
      return;
    }

    req.body.userId = new ObjectId(req.body.userId as string);
    const newCommunityPost = new CommunityPost(req.body);
    const communityPost = await newCommunityPost.save();

    res.status(200).json({ status: true, data: communityPost });
  } catch (err) {
    res.status(400).json({ message: "something Went Wrong" });
  }
}

// get communitypost controller 
const getCommunityPost = async (req: Request, res: Response): Promise<void> => {
  try {
    const communityPosts = await CommunityPost.find().populate('userId', 'fname lname pic username email address').populate({
      path: 'likes.user',
      model: 'Users',
      select: 'fname lname pic address username email',
    }).populate({
      path: 'replies.user',
      model: 'Users',
      select: 'fname lname pic address username email',
    });
    res.status(200).json({ status: true, data: communityPosts });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Something went wrong' });
  }
};

const getCommunityPostById = async (req: Request, res: Response): Promise<void> => {
  try {
    const postId = req.params.postId;

    const communityPost = await CommunityPost.findById(postId).populate('userId', 'fname lname pic username email address').populate({
      path: 'likes.user',
      model: 'Users',
      select: 'fname lname pic address username email',
    }).populate({
      path: 'replies.user',
      model: 'Users',
      select: 'fname lname pic address username email',
    })
      .populate('userId', 'fname lname pic username email address');

    if (!communityPost) {
      res.status(404).json({ status: false, message: 'Community post not found' });
      return;
    }

    res.status(200).json({ status: true, data: communityPost });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Something went wrong' });
  }
};

// like post & unlike
const CommunityLikePost = async (req: Request, res: Response): Promise<void> => {
  try {
    const { communityId, userId } = req.body;

    const community = await CommunityPost.findById(communityId);

    if (!community) {
      res.status(404).json({ error: 'Community not found' });
      return;
    }

    const existingLike = community.likes.some((like) => like.user.toString() === userId);
    if (existingLike) {
      community.likes.pull({ user: userId });
      await community.save();
    } else {
      community.likes.push({ user: userId });
      await community.save();
    }
    const newData = await CommunityPost.findById(communityId)
    res.status(200).json({ status: true, data: newData });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

//  replies post 
const CommunityReplyPost = async (req: Request, res: Response): Promise<void> => {
  try {
    const { communityId, userId, description } = req.body;
    const community = await CommunityPost.findById(communityId);
    if (!community) {
      res.status(404).json({ error: 'Community not found' });
      return;
    }
    community.replies.push({ user: userId, description });
    await community.save();

    res.status(201).json(community.replies);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// blogs portion 

// Create a blog
const createBlogs = async (req: MulterS3Request, res: Response): Promise<void> => {
  try {
    const {title,description} = req.body;
    req.body.pic = req.file.location;
    if(!title || !description ){
      res.status(400).json({status:false, error: 'please fill all the fields' });
      return;
    }
    const blogData = await Blog.create(req.body);
    res.status(201).json({status:true, data:blogData});
  } catch (error) {
    console.error(error);
    res.status(500).json({status:false, error: 'Internal Server Error' });
  }
};

// Get all blog
const getAllBlogs = async (req: Request, res: Response): Promise<void> => {
  try {
    const blogsData = await Blog.find()
    res.status(200).json({status:true,data:blogsData});
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// Get a specific blog by ID
const getBlogById = async (req: Request, res: Response): Promise<void> => {
  
  try {
    const blogData = await Blog.findById(req.params._id).populate('comments.userId');
    if (!blogData) {
      res.status(404).json({ status: false, error: 'Blog not found' });
      return;
    }
    const comments = blogData.comments;
    const mainComments = comments.filter(c => !c.parentCommentId);
    const replies = comments.filter(c => c.parentCommentId);
    const threadedComments = mainComments.map(main => {
      const childReplies = replies.filter(reply =>
        reply.parentCommentId?.toString() === main._id.toString()
      );
      return {
        ...main.toObject(),
        replies: childReplies.map(r => r.toObject())
      };
    });
    const blogWithThreadedComments = {
      ...blogData.toObject(),
      comments: threadedComments
    };
    res.status(200).json({ status: true, data: blogWithThreadedComments });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// Update a blog by ID
const updateBlog = async (req: MulterS3Request, res: Response): Promise<void> => {
  try {
    if(req.file){
      req.body.pic = req.file.location
    }
    const blogData = await Blog.findByIdAndUpdate(req.params._id, req.body, { new: true });
    if (!blogData) {
      res.status(404).json({ error: 'Blog not found' });
      return;
    }
    res.status(200).json({status:true,data:blogData});
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// Delete a blog by ID
const deleteBlog  =async (req: Request, res: Response): Promise<void> => {
  try {
    const deletedBlog = await Blog.findByIdAndDelete(req.params._id);
    if (!deletedBlog) {
      res.status(404).json({ error: 'blog not found' });
      return;
    }
    res.status(200).json({status: true , message:"successfully deleted"});
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const blogComment = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      name,
      email,
      website,
      userId,
      blogId,
      message,
      parentCommentId, 
    } = req.body; 

    if (!userId) {
      res.status(400).json({ status: false, message: "Please login first" });
      return;
    }

    if(!parentCommentId){
      if (!name || !message) {
        res.status(400).json({ status: false, message: "Please fill all the fields" });
        return;
      }
    }

    const findBlog = await Blog.findById(blogId);
    if (!findBlog) {
      res.status(404).json({ status: false, message: "Blog not found" });
      return;
    }

    const newComment = {
      name,
      email,
      website,
      userId,
      message,
      parentCommentId: parentCommentId || null,
    };

    findBlog.comments.push(newComment);
    await findBlog.save();

    res.status(200).json({
      status: true,
      message: parentCommentId ? "Reply submitted successfully" : "Comment submitted successfully",
    });

  } catch (err) {
    console.error("Error posting comment:", err);
    res.status(500).json({ status: false, message: "Something went wrong" });
  }
};

const blogCommentupdate = async (req: Request, res: Response): Promise<void> => {
  const { blogId, commentId } = req.params; 
  const { message } = req.body;

  try {
    if (!mongoose.Types.ObjectId.isValid(blogId) || !mongoose.Types.ObjectId.isValid(commentId)) {
      res.status(400).json({ success: false, message: "Invalid Blog or Comment ID" });
      return;
    }

    const blog = await Blog.findOne({ _id: blogId, "comments._id": commentId });
    if (!blog) {
      res.status(404).json({ success: false, message: "Blog or comment not found" });
      return;
    }

    if (!req.body.message) {
      res.status(400).json({ success: false, message: "No new message provided" });
      return;
    }

    const comment = blog.comments.id(commentId);
    if (!comment) {
      res.status(404).json({ success: false, message: "Comment not found" });
      return;
    }

    if (!comment.editHistory) {
      comment.set('editHistory', [])
    }
    comment.editHistory.push({ previousMessage: comment.message, editedAt: new Date() });
    comment.message = message || comment.message;
    comment.status = 1;
    await blog.save();
    res.status(200).json({
      success: true,
      message: "Comment updated successfully",
    });

  } catch (error) {
    console.error("Error updating blog comment:", error);
    res.status(500).json({ success: false, message: "Internal Server Error" });
  }
};

const deleteComment = async (req: Request, res: Response): Promise<void> => {
  const { blogId, commentId } = req.params;

  try {
    if (!mongoose.Types.ObjectId.isValid(blogId) || !mongoose.Types.ObjectId.isValid(commentId)) {
      res.status(400).json({ success: false, message: "Invalid Blog or Comment ID" });
      return;
    }

    const blog = await Blog.findOne({ _id: blogId, "comments._id": commentId });
    if (!blog) {
      res.status(404).json({ success: false, message: "Blog or comment not found" });
      return;
    }

    blog.comments.pull({ _id: commentId });
    await blog.save();

    res.status(200).json({
      success: true,
      message: "Comment deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting blog comment:", error);
    res.status(500).json({ success: false, message: "Internal Server Error" });
  }
};


 const postInnovatorData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { fname, lname, email, state1, state2, state3, state4, state5 } = req.body;


     const existingUser = await Innovator.findOne({ email });
    if (existingUser) {
       res.status(400).json({status: false,message: "This email has already been submitted."});
       return;
    }
    const newInnovator = new Innovator({
      fname,
      lname,
      email,
      state1,
      state2,
      state3,
      state4,
      state5
    });

    const savedInnovator = await newInnovator.save();

  
     await sendEmail(
      email,
      "Thank you for your interest in BnByond",
      { fname, lname },
      'thanks_Email_Template'
    );
    res.status(201).json({ status: true, data: savedInnovator });
  } catch (error: any) {
    if (error.code === 11000) {
      res.status(409).json({ status: false, message: "Email already exists." });
    } else {
      console.error("Error posting innovator data:", error);
      res.status(500).json({ status: false, message: "Something went wrong." });
    }
  }
};




const getInnovatorData = async (_req: Request, res: Response): Promise<void> => {
  try {
    const data = await Innovator.find().sort({ createdAt: -1 });
    res.status(200).json({ status: true, data });
  } catch (error) {
    console.error("Error fetching innovator data:", error);
    res.status(500).json({ status: false, message: "Failed to fetch data" });
  }
};
export const Controller = {
  uploadCommunityPost,
  getCommunityPost,
  getCommunityPostById,
  CommunityLikePost,
  CommunityReplyPost,
  createBlogs,
  updateBlog,
  getAllBlogs,
  getBlogById,
  deleteBlog,
  blogComment,
  blogCommentupdate,
  deleteComment,
  postInnovatorData,
  getInnovatorData
};
