import React from "react";
import { Editor } from "@tinymce/tinymce-react";
import { config } from "config.js";

export const TextEditor=({log='', editorRef}) =>{

  return (
    <>
      <Editor
        apiKey={config.textEditorTinyKey}
        onInit={(evt, editor) => (editorRef.current = editor)}
        initialValue={log}
        init={{
            height: "100%", 
          menubar: false,
          plugins: [
            "advlist",
            "autolink",
            "lists",
            "link",
            "image",
            "charmap",
            "preview",
            "anchor",
            "searchreplace",
            "visualblocks",
            "code",
            "fullscreen",
            "insertdatetime",
            "media",
            "table",
            "code",
            "help",
            "wordcount",
          ],
          toolbar:
            "undo redo | blocks | " +
            "bold italic forecolor | alignleft aligncenter " +
            "alignright alignjustify | bullist numlist outdent indent | " +
            "removeformat | help",
          content_style:
            "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
        }}
      />
    </>
  );
}