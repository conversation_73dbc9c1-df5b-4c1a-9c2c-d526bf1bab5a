{"name": "bnbyond-web", "version": "1.0.0", "description": "A highly scalable, offline-first foundation with the best DX and a focus on performance and best practices", "private": true, "dependencies": {"@date-io/moment": "^2.16.1", "@dropzone-ui/react": "^6.7.10", "@emotion/react": "^11.9.3", "@emotion/styled": "^11.9.3", "@fortawesome/fontawesome-free": "6.1.2", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@mui/material": "^5.9.2", "@mui/x-date-pickers": "^5.0.4", "@react-oauth/google": "^0.12.1", "@stripe/react-stripe-js": "^2.7.0", "@stripe/stripe-js": "^3.3.0", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "13.3.0", "@testing-library/user-event": "14.3.0", "@tinymce/tinymce-react": "^4.3.2", "apexcharts": "^4.5.0", "axios": "0.27.2", "bcrypt": "^5.0.1", "chart.js": "^4.4.1", "countries-list": "^3.1.0", "crypto": "^1.0.1", "dayjs": "^1.11.7", "emailjs": "^4.0.3", "emailjs-com": "^3.2.0", "flat-carousel": "^0.0.1", "formik": "^2.4.5", "gapi-script": "^1.2.0", "google-map-react": "^2.2.0", "history": "^5.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.44", "mui-image-slider": "^1.0.7", "otp-input-react": "^0.3.0", "react": "18.2.0", "react-apexcharts": "^1.7.0", "react-calendar": "^4.8.0", "react-country-flag": "^3.1.0", "react-country-region-selector": "^3.6.1", "react-credit-cards-2": "^1.0.2", "react-dom": "18.2.0", "react-drag-drop-files": "^2.3.10", "react-dropzone": "^14.2.2", "react-flag-kit": "^1.1.1", "react-ga4": "^2.1.0", "react-icons": "^4.12.0", "react-loading-skeleton": "^3.5.0", "react-localization": "^1.0.17", "react-modal": "^3.16.1", "react-moment": "^1.1.2", "react-multi-carousel": "^2.8.2", "react-otp-input": "^3.1.0", "react-outside-click-handler": "^1.3.0", "react-paginate": "^8.2.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.3.7", "react-query": "4.0.0", "react-rating": "^2.0.5", "react-rating-stars-component": "^2.2.0", "react-redux": "8.0.2", "react-router-dom": "^6.2.1", "react-scripts": "5.0.1", "react-share": "^5.0.3", "react-slick": "^0.29.0", "react-slider": "^2.0.6", "react-slideshow-image": "^4.1.1", "react-stripe-checkout": "^2.6.3", "react-toastify": "9.0.7", "redux": "^4.1.2", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.4.1", "slick-carousel": "^1.8.1", "socket.io-client": "^4.7.4", "sweetalert2": "^11.10.1", "sweetalert2-react-content": "^5.0.7", "use-places-autocomplete": "^4.0.0", "web-vitals": "3.0.0-rc.0", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && npm run optimize-build", "build:analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js", "optimize-build": "npm run compress-assets && npm run generate-sw-precache", "compress-assets": "gzip -k build/static/js/*.js && gzip -k build/static/css/*.css", "generate-sw-precache": "echo 'Service worker already included'", "optimize:images": "node scripts/optimize-images.js", "optimize:deps": "node scripts/optimize-dependencies.js", "optimize:all": "npm run optimize:deps && npm run optimize:images", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "lighthouse": "lhci autorun", "performance:check": "node scripts/performance-check.js", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"postcss": "8.4.14", "tailwindcss": "3.1.6"}, "proxy": "http://localhost:5000"}