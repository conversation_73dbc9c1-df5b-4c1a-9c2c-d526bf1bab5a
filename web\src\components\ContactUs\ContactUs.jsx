import React, { useRef, useState } from 'react';
import img1 from "assets/img/abtdesign.png";
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import Footerbn from 'pages/Footer/Footerbn';
import Container from "@mui/material/Container";
import icon from "assets/img/icon.png";
import { toast } from 'react-toastify';
import emailjs from 'emailjs-com';
import BnbNav from 'components/NavBar/BnbNav';
import Navbar from 'components/Navbars/AuthNavbar';
import NewsLetter from 'components/NewsLetter/NewsLetter';

const ContactUs = () => {
  const formRef = useRef();
  const [showExpectationMessage, setShowExpectationMessage] = useState(false);

  const sendEmail = (values, actions) => {
    emailjs.sendForm('service_giri3ed', 'template_c5jkdxh', formRef.current, '6nmQhJABeuPA-BNo9')
      .then((result) => {
        toast.success("Message sent successfully");
        setShowExpectationMessage(true);
        actions.resetForm();
      }, (error) => {
        toast.error("Failed to send message");
      })
      .finally(() => {
        actions.setSubmitting(false);
      });
  };

  return (
    <>
      <BnbNav />
      <Navbar />
      <div className='flex flex-col items-center justify-center space-y-2 md:p-16 p-3 '>
        <h1 className='md:text-4xl text-2xl font-medium'>Let's Make Your Travel Dreams a Reality!</h1>
        <p className='md:text-xl text-lg font-normal text-[#4B4B4B] md:w-[526px] w-full text-center'>Your Journey Begins with a Click: Reach Out and Explore the World Together.</p>
      </div>

      <div className='flex flex-col items-center bg-[#F8F8F8] md:p-10 p-4 md:space-y-4 space-y-2 '>
        <div className='w-full flex md:flex-row flex-col md:gap-5 gap-3 items-center justify-center'></div>
        <div>
          <img src={img1} alt="" />
        </div>
      </div>

      <div className='px-3 pt-20 flex items-center justify-center xl:justify-start xl:pl-36 '>
        <span className='text-2xl font-extrabold'>Send Us A Message</span>
      </div>

      <Formik
        initialValues={{
          from_name: '',
          user_email: '',
          user_phone: '',
          user_city: '',
          message: ''
        }}
        validate={(values) => {
          const errors = {};
          if (!values.from_name) {
            errors.from_name = 'Required';
          }
          if (!values.user_email) {
            errors.user_email = 'Required';
          } else if (!/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/.test(values.user_email)) {
            errors.user_email = 'Invalid email address';
          }

          return errors;
        }}
        onSubmit={(values, actions) => {
          sendEmail(values, actions);
        }}
      >
        {({ isSubmitting }) => (
          <Form ref={formRef} noValidate className='flex flex-col gap-4 items-center justify-center pt-6 px-3'>
            <div className='w-full max-w-[1140px] flex flex-col gap-3'>
              <div className='flex flex-col xl:flex-row gap-3 w-full'>
                <div className='flex flex-col flex-1'>
                  <label htmlFor="from_name">Name*</label>
                  <Field type="text" id="from_name" name="from_name" placeholder='Your Name' className='p-2 w-full h-[52px] border border-[#C1E1C2] rounded-lg' />
                  <ErrorMessage name="from_name" component="div" className="text-red-500" />
                </div>
                <div className='flex flex-col flex-1'>
                  <label htmlFor="user_email">Email*</label>
                  <Field type="email" id="user_email" name="user_email" placeholder='Email Address' className='p-2 w-full h-[52px] border border-[#C1E1C2] rounded-lg' />
                  <ErrorMessage name="user_email" component="div" className="text-red-500" />
                </div>
              </div>

              <div className='flex flex-col xl:flex-row gap-3 w-full'>
                <div className='flex flex-col flex-1'>
                  <label htmlFor="user_phone">Phone number</label>
                  <Field type="tel" id="user_phone" name="user_phone" placeholder='+62' className='p-2 w-full h-[52px] border border-[#C1E1C2] rounded-lg' />
                  <ErrorMessage name="user_phone" component="div" className="text-red-500" />
                </div>
                <div className='flex flex-col flex-1'>
                  <label htmlFor="user_city">City</label>
                  <Field type="text" id="user_city" name="user_city" placeholder='City' className='p-2 w-full h-[52px] border border-[#C1E1C2] rounded-lg' />
                  <ErrorMessage name="user_city" component="div" className="text-red-500" />
                </div>
              </div>

              <div className='flex flex-col w-full'>
                <label htmlFor="message">Description</label>
                <Field as="textarea" id="message" name="message" placeholder="Enter description" rows="6" className="p-2 rounded-lg border border-[#C1E1C2] w-full" />
                <ErrorMessage name="message" component="div" className="text-red-500" />
                <div className='pt-3 pb-2 flex justify-center'>
                  <button type="submit" disabled={isSubmitting} className='max-w-fit md:py-5 py-3 md:px-6 px-4 bg-[#2459BF] rounded-full text-white w-auto whitespace-nowrap'>
                    {isSubmitting ? 'Submitting...' : 'SEND MESSAGE'}
                  </button>
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
      {showExpectationMessage && (
        <div className="text-center mt-4 p-4 bg-green-100 text-green-700 border border-green-400 rounded-lg">
          <p>Thank you for reaching out! Our team will respond to your message within 24 hours.</p>
        </div>
      )}


      <div className='flex justify-center items-center'>
        <div className="flex justify-center w-full">
          <NewsLetter />
        </div>
      </div>
      <Footerbn />
    </>
  );
}

export default ContactUs;
