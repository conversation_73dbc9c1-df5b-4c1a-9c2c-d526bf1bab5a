import React, { useEffect, useState } from "react";
import Breadcrumbs from "components/BreadCrumbs/BreadCrumbs";
import { XCircleIcon } from "@heroicons/react/24/outline";
import { useMutation } from "react-query";
import { localStorageData } from "services/auth/localStorageData";
import userServices from "services/httpService/userAuth/userServices";
import ErrorService from "services/formatError/ErrorService";
import { toast } from "react-toastify";
import { storeLocalData } from "services/auth/localStorageData";

const userId = localStorageData("_id");
const email = localStorageData("email");

export const ManageSubscription = () => {
    const [subscriptionDetails, setSubscriptionDetails] = useState([]);
    const [subscriptionLoading, setSubscriptionLoading] = useState(false)
    const [showSupportForm, setShowSupportForm] = useState(false);
    const [supportMessage, setSupportMessage] = useState("");
    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [subscriptionToCancel, setSubscriptionToCancel] = useState(null);

    const { mutate: cancelSubscriptionMutate, isLoading: isCanceling } = useMutation(
        (subscriptionId) =>
            userServices.stripeauthorize(`stripe/cancelsubscription/${userId}`, {
                subscriptionId,
            }),
        {
            onError: (error) => {
                toast.error(ErrorService.uniformError(error));
            },
            onSuccess: () => {
                toast.success("Subscription successfully canceled.");
                setSubscriptionDetails((prev) =>
                    prev.map((sub) =>
                        sub.subscription_id === subscriptionToCancel
                            ? { ...sub, auto_renewal: false }
                            : sub
                    )
                );
                setShowCancelDialog(false);
            },
        }
    );


    const getSubscriptionDetail = async () => {
        setSubscriptionLoading(true)
        try {
            if (!email) {
                console.error("No user ID found");
                return;
            }

            const response = await userServices.getSubscriptionDetails(email);
            if (response && response.data) {
                setSubscriptionDetails(response.data.stripeSubscription);
                setSubscriptionLoading(false)
            }
        } catch (error) {
            console.error("Error fetching payment details:", error);
        }
    };

    const handleCancelSubscription = () => {
        if (!subscriptionToCancel) {
            toast.error("Subscription ID not found. Please refresh and try again.");
            return;
        }
        cancelSubscriptionMutate(subscriptionToCancel);
    };

    const fetchUserData = async () => {
        try {
            const userId = localStorageData("_id");
            const apiUrl = `userAuth/userdatabyId/${userId}`;
            let res = await userServices.userDatagetById(apiUrl);
            storeLocalData(res?.data.data);
        } catch (error) {
            console.error("Error fetching userdatabyid data", error);
        }
    };

    useEffect(() => {
        fetchUserData();
        getSubscriptionDetail();
    }, [cancelSubscriptionMutate]);
    return (
        <div className="md:my-10 md:mx-20 flex flex-col gap-6">
            <Breadcrumbs />
            <div className="rounded-2xl border border-[#C1E1C2] h-auto w-full p-6 bg-white shadow-lg">
                <div className="p-5 border-b border-gray-200">
                    <p className="text-2xl font-semibold text-gray-800">Manage Subscription</p>
                </div>

                <div className="flex flex-col gap-4 p-4 max-w-[90%]">
                    {subscriptionLoading ? (
                        <p className="mt-5 text-center text-base text-gray-600">Loading Subscriptions...</p>
                    ) : subscriptionDetails.length === 0 ? (
                        <p className="mt-5 text-center text-base text-gray-600">No Subscription Found</p>
                    ) : (
                        subscriptionDetails.map((subscription, index) => (
                            <div
                                key={index}
                                className={`p-6 rounded-xl shadow-md transition-all duration-300 ${subscription.status === "active"
                                    ? "bg-green-50 border border-green-400 shadow-lg"
                                    : "bg-gray-50 border border-gray-300"
                                    }`}
                            >
                                <div className="flex items-center justify-between mb-3">
                                    <h2 className="text-lg font-bold text-gray-800">Subscription Details</h2>
                                    <span
                                        className={`px-3 py-1 text-sm font-semibold rounded-full ${subscription.status === "active"
                                            ? "bg-green-100 text-green-700"
                                            : "bg-red-100 text-red-700"
                                            }`}
                                    >
                                        {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                                    </span>
                                </div>

                                <div className="space-y-3 text-sm text-gray-700">
                                    <p><strong>Plan Name:</strong> {subscription.plan_name}</p>
                                    <p><strong>Start Date:</strong> {new Date(subscription.start_date).toLocaleDateString()}</p>
                                    <p><strong>Next Billing Date:</strong> {new Date(subscription.current_period_end).toLocaleDateString()}</p>
                                    <p>
                                        <strong>Auto Renewal:</strong>
                                        <span
                                            className={`ml-2 px-2 py-1 rounded-full text-xs font-semibold ${subscription.auto_renewal
                                                ? "bg-green-100 text-green-700"
                                                : "bg-red-100 text-red-700"
                                                }`}
                                        >
                                            {subscription.auto_renewal ? "Enabled" : "Disabled"}
                                        </span>
                                    </p>
                                </div>

                                {/* Show Danger Zone only if auto-renewal is enabled */}
                                {subscription.auto_renewal && (
                                    <div className="p-5 mt-6 bg-red-50 border border-red-300 rounded-xl">
                                        <h2 className="text-lg font-semibold text-red-700">Danger Zone</h2>
                                        <p className="text-sm text-gray-700 mt-2">
                                            Canceling your subscription will remove access to premium features. This action cannot be undone.
                                        </p>
                                        <button
                                            className="mt-4 w-full md:w-auto bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition-all"
                                            onClick={() => {
                                                setShowCancelDialog(true);
                                                setSubscriptionToCancel(subscription.subscription_id);
                                            }}
                                            disabled={isCanceling}
                                        >
                                            {isCanceling ? "Canceling..." : "Unsubscribe"}
                                        </button>
                                    </div>
                                )}
                            </div>
                        ))
                    )}
                </div>


                {/* Customer Support Section */}
                {/* <div className="p-4">
                    <div className="p-6 my-6 bg-gray-50 border border-gray-300 rounded-lg">
                        <div className="flex justify-between">
                            <div>
                                <h2 className="text-lg font-semibold text-gray-700">Need Help?</h2>
                                <p className="text-base text-gray-700">If you have any issues, feel free to reach out to our support team.</p>
                            </div>
                            {!showSupportForm && (
                                <button
                                    className="mt-4 bg-gray-700 hover:bg-gray-800 text-white font-semibold py-2 px-4 rounded-lg shadow-md w-full md:w-auto"
                                    onClick={() => setShowSupportForm(true)}
                                >
                                    Contact Support
                                </button>
                            )}
                        </div>
                    </div>
                    {showSupportForm && (
                        <div className="mt-4 relative">
                            <button
                                className="absolute top-2 right-4 text-gray-500 hover:text-gray-700"
                                onClick={() => setShowSupportForm(false)}
                            >
                                <XCircleIcon size={20} title="Close" className="w-6 h-6" />
                            </button>
                            <textarea
                                className="w-full p-2 border border-gray-300 rounded-md resize-none text-base"
                                rows="4"
                                placeholder="Describe your issue..."
                                value={supportMessage}
                                onChange={(e) => setSupportMessage(e.target.value)}
                            />
                            <button
                                className="mt-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md w-full md:w-auto"
                                onClick={() => setSupportMessage("")}
                            >
                                Submit
                            </button>
                        </div>
                    )}
                </div> */}
            </div>



            {/* Cancel Confirmation Dialog */}
            {showCancelDialog && (
                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
                <div className="bg-white p-6 rounded-lg shadow-lg">
                    <p className="text-lg font-semibold">Are you sure you want to cancel your subscription?</p>
                    <div className="flex gap-4 mt-4">
                        <button
                            className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center justify-center"
                            onClick={handleCancelSubscription}
                            disabled={isCanceling}

                        >
                            {isCanceling ? "Cancelling..." : "Yes, Cancel"}
                        </button>
                        <button
                            className="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-lg"
                            onClick={() => setShowCancelDialog(false)}
                            disabled={isCanceling}
                        >
                            No, Keep It
                        </button>
                    </div>
                </div>
            </div>
            )}
        </div>
    );
};
