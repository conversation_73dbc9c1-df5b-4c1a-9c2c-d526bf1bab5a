import * as Sentry from "@sentry/node";

// Helper function to check if Sentry should be enabled
export const isSentryEnabled = () => process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN;

// The request handler must be the first middleware on the app
if (isSentryEnabled()) {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    release: process.env.VERSION || 'unknown',
    tracesSampleRate: 1.0, // Capture 100% of the transactions
  });
}
