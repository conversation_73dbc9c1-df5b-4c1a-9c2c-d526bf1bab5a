import Container from "@mui/material/Container";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import { useMutation } from "react-query";
import ErrorService from "services/formatError/ErrorService";
import userService from "services/httpService/userAuth/userServices";
import CircularProgress from "@mui/material/CircularProgress";
import { localStorageData } from "services/auth/localStorageData";
import * as Yup from "yup";
import { useFormik } from "formik";
import { useState } from "react";
import PopupModal from "components/PopupModal/PopupModal";
import { modalText } from "common/commonText/ModalText";
import { Logout } from "services/auth/localStorageData";
import { storeLocalData } from "services/auth/localStorageData";

function Profile() {
  let navigate = useNavigate();
  const [updatePass, setUpdatePass] = useState([]);
  const [openAlert, setOpenAlert] = useState(false);

  // Formik hook to handle form state and validation
  const formik = useFormik({
    initialValues: {
      userId: localStorageData("_id"),
      pics: "",
      fname: localStorageData("fname"),
      phoneNumber: localStorageData("phoneNumber"),
      address: localStorageData("address"),
      pic: localStorageData('pic')
    },
    validationSchema: Yup.object().shape({}),
    onSubmit: async (values) => {
      const formData = new FormData();
      formData.append("pics", values.pic);
      formData.append("userId", values.userId);
      formData.append("fname", values.fname);
      formData.append("lname", values.fname);
      formData.append("phoneNumber", values.phoneNumber);
      formData.append("address", values.address);
      mutate(formData);
    },
  });

  // Mutation hook to update user info
  const { mutate, isLoading } = useMutation(
    (token) => userService.commonPostService("/userAuth/updateuserinfo", token),
    {
      onError: (error) => {
        toast.error(ErrorService.uniformError(error));
      },
      onSuccess: (data) => {
        if (data?.data?.data.isEnable == "no") {
          toast.success("your account deactivated successfully");
          Logout();
          navigate('/');
        } else {
          storeLocalData(data?.data.data);
          toast.success("updated successfully!");
        }
      },
    }
  );

  // Mutation hook to update password
  const { mutate: passMutate, isLoading: loading } = useMutation(
    (token) =>
      userService.commonPostService(
        "/userAuth/updatepasswordFromProfile",
        token
      ),
    {
      onError: (error) => {
        toast.error(ErrorService.uniformError(error));
      },
      onSuccess: (data) => {
        setUpdatePass({ new_password: "", old_password: "" });
        toast.success("password updated successfully!");
      },
    }
  );

  // Handles file input change and previews selected image
  const onChangeHandler = async (e) => {
    var reader = new FileReader();
    reader.onload = function () {
      var output = document.getElementById("serviceimg");
      output.src = reader.result;
    };
    if (e.target.files[0]) {
      const file = e.target.files[0];
      reader.readAsDataURL(file);
      formik.setFieldValue("pic", file);
    }
  };

  // Updates password state on input change
  const handleChangePass = (event) => {
    setUpdatePass((prevUpdatePass) => ({
      ...prevUpdatePass,
      [event.target.name]: event.target.value,
    }));
  };

  // Triggers password update mutation
  const handlePassUpdate = () => {
    passMutate({
      oldPass: updatePass.old_password,
      newPass: updatePass.new_password,
      userId: localStorageData("_id"),
    });
  };

  const handleDeativateAccount = () => {
    setOpenAlert(true);
  };
  const handleAcceptModal = () => {
    setOpenAlert(false);
  };
  const handleCancleModal = () => {
    setOpenAlert(false);
  };

  return (
    <div className="mt-20">
      {openAlert && (
        <PopupModal
          handleCancle={handleCancleModal}
          handleAccept={handleAcceptModal}
          openAlert={openAlert}
          setOpenAlert={setOpenAlert}
          description={modalText.deactivationAccount}
          title={modalText.deactivationTitle}
          acceptbtnText={modalText.deactivate}
        />
      )}
      <Container maxWidth="md">
        <h1 className="mb-10 text-3xl font-bold text-center">Profile</h1>

        <form onSubmit={formik.handleSubmit}>
          <div className="card-styl">
            <div className="grid center-styl">
              <img
                className="object-cover w-24 h-24 mx-auto rounded-full"
                src={
                  formik.values.pic
                }
                alt="profile img"
                id="serviceimg"
              />

              <label
                className="flex mt-4 btn-styl center-styl"
                htmlFor="contained-button-file"
              >
                <input
                  type="file"
                  onChange={onChangeHandler}
                  accept="image/*"
                  id="contained-button-file"
                />
                Upload
              </label>

              {formik.touched.pics && formik.errors.pics ? (
                <div className="text-sm text-red-500">{formik.errors.pics}</div>
              ) : null}

              <h2 className="my-2 text-2xl font-bold text-center center-styl">

                {localStorageData("fname")}
              </h2>
              <h3 className="text-lg font-semibold text-center ">
                {localStorageData("email")}
              </h3>
            </div>
          </div>
          <div className="card-styl ">
            <h3 className="text-xl font-bold">Personal info</h3>

            <div className="my-2">
              <label className="block my-2 text-sm font-bold">
                Phone Number
              </label>
              <input
                type="number"
                placeholder="Phone Number"
                id="phoneNumber"
                name="phoneNumber"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.phoneNumber}
                className="p-2 input-hidden focus:bg-slate-100 placeholder:text-black"
              />
              {formik.touched.phoneNumber && formik.errors.phoneNumber ? (
                <div className="text-sm text-red-500">
                  {formik.errors.phoneNumber}
                </div>
              ) : null}
            </div>

            <div className="my-2">
              <label className="block my-2 text-sm font-bold">Address</label>
              <input
                type="text"
                placeholder="Address"
                id="address"
                name="address"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.address}
                className="w-2/4 p-2 input-hidden focus:bg-slate-100 placeholder:text-black"
              />
              {formik.touched.address && formik.errors.address ? (
                <div className="text-sm text-red-500">
                  {formik.errors.address}
                </div>
              ) : null}
            </div>
          </div>

          <div className="card-styl ">
            <h3 className="text-xl font-bold">Update Password</h3>

            <label className="block my-2 text-sm font-bold">Old Password</label>
            <input
              type="text"
              placeholder="Old Password"
              name="old_password"
              onChange={handleChangePass}
              value={updatePass.old_password}
              className="w-2/4 p-2 input-hidden focus:bg-slate-100 placeholder:text-black"
            />
            <label className="block my-2 text-sm font-bold">Cha Password</label>
            <input
              type="text"
              placeholder="New Password"
              name="new_password"
              onChange={handleChangePass}
              value={updatePass.new_password}
              className="w-2/4 p-2 input-hidden focus:bg-slate-100 placeholder:text-black"
            />

            <div
              onClick={() => handlePassUpdate()}
              className="px-6 py-3 my-2 mb-1 mr-1 text-sm font-bold text-white transition-all duration-150 ease-linear rounded shadow cursor-pointer bg-color-primary w-44 hover:shadow-lg"
            >
              Change Password
            </div>
          </div>

          <div className="card-styl ">
            <h3 className="text-xl font-bold">Verification</h3>

            <button
              onClick={() => navigate("/verification")}
              className="px-6 py-3 my-2 mb-1 mr-1 text-sm font-bold text-white transition-all duration-150 ease-linear rounded shadow bg-color-primary w-44 hover:shadow-lg"
            >
              Start Verification
            </button>
          </div>
          <div className="card-styl ">
            <h3 className="text-xl font-bold">Deactivate Account</h3>

            <div
              onClick={() => handleDeativateAccount()}
              className="px-6 py-3 my-2 mb-1 mr-1 text-sm font-bold text-white transition-all duration-150 ease-linear rounded shadow cursor-pointer bg-color-red w-44 hover:shadow-lg"
            >
              Deactivate Account
            </div>
          </div>
          <div className="mt-6 text-center">
            {isLoading ? (
              <CircularProgress />
            ) : (
              <>
                <button className="text-sm font-bold text-white btn-styl bg-color-primary" type="submit">
                  Update
                </button>
              </>
            )}
          </div>
        </form>
      </Container>
    </div>
  );
}

export default Profile;
