import http from "services/httpService/http-common.js";

class userService {
  signUp(data) {
    return http.post("/register", data);
  }
  login(data) {
    return http.post("/login", data);
  }

  applyForForgetPass(data) {
    return http.post("/applyforgetpass", data);
  }

  updatePass(data) {
    return http.post("/updatepassword", data);
  }
  userGetbyId(data) {
    return http.post("/userget", data);
  }

  commonPostService(route, data) {
    return http.post(route, data);
  }
  communityhost(route, data) {
    return http.post(route, data);
  }
  updatePropertyById(route, data) {
    return http.put(route, data);
  }
  updatePropertyStatus(route, data) {
    return http.put(route, data);
  }
  addReviewToProperty(route, data) {
    return http.put(route, data);
  }
  Blogpost(route, data) {
    return http.post(route, data);
  }
  getBlogPost(route, data) {
    return http.get(route, data);
  }
  updateBlogPost(route, data) {
    return http.put(route, data);
  }
  deleteBlogPost(route, data) {
    return http.put(route, data);
  }
  communityReplyPost(route, data) {
    return http.post(route, data);
  }
  communitylikePost(route, data) {
    return http.post(route, data);
  }
  communitydeletePost(route, data) {
    return http.delete(route, data);
  }
  communityGethost(route, data) {
    return http.get(route, data);
  }
  updatePostService(route, data) {
    return http.put(route, data);
  }
  refundReservation(route, data) {
    return http.put(route, data);
  }
  updateReservation(route, data) {
    return http.put(route, data);
  }
  updateImage(route, data) {
    return http.post(route, data);
  }
  availabilityToogle(route, data) {
    return http.put(route, data);
  }
  deletePostService(route, data) {
    return http.delete(route, data);
  }
  otpService(route, data) {
    return http.put(route, data);
  }
  commonGetService(route, data) {
    return http.get(route, data);
  }
  commonPutService(route, data) {
    return http.put(route, data);
  }
  commonDeleteService(route, data) {
    return http.delete(route, data);
  }
  Reservation(route, data) {
    return http.post(route, data);
  }
  getReservationByuserId(route, data) {
    return http.get(route, data);
  }
  getReservationById(route, data) {
    return http.get(route, data);
  }
  Wishlist(route, data) {
    return http.put(route, data);
  }
  //chat routes
  getChatUsers(route, data) {
    return http.get(route, data);
  }
  createConversation(route, data) {
    return http.post(route, data);
  }
  getChatConversations(route, data) {
    return http.get(route, data);
  }
  getConversation(route, data) {
    return http.get(route, data);
  }
  fetchMessages(route, data) {
    return http.get(route, data);
  }
  sendMessageApi(route, data) {
    return http.post(route, data);
  }
  sendFileApi(route, data) {
    return http.post(route, data);
  }

  googleVerification(route, data) {
    return http.post(route, data);
  }
  userDatagetById(route, data) {
    return http.get(route, data);
  }
  userById(route, data) {
    return http.get(route, data);
  }
  //checking deatil of comming reservation
  commingreservation(route, data) {
    return http.get(route, data);
  }
  userUpdateById(route, data) {
    return http.put(route, data);
  }
  deleteUser(route, data) {
    return http.delete(route, data);
  }

  adminLogin(route, data) {
    return http.post(route, data);
  }

  getReferralCode(route, data) {
    return http.post(route, data);
  }
  stripeConnect(route, data) {
    return http.post(route, data);
  }
  stripeRefund(route, data) {
    return http.post(route, data);
  }
  stripeauthorize(route, data) {
    return http.post(route, data);
  }
  getStripeTransactions(route) {
    return http.get(route);
  }

  getPaymentDetailsById(id) {
    return http.get(`/stripe/trackpayment/${id}`);
  }

  recoveryEmail(route, data) {
    return http.put(route, data);
  }

  createEmployee(data) {
    return http.post("/employes/addEmploye", data);
  }

  getAllEmployees() {
    return http.get("employes/getEmploye");
  }

  editEmployee(employeeId, data) {
    return http.put(`/employes/employeRole/${employeeId}`, data);
  }

  getUserDeviceInfo(route) {
    return http.get(route);
  }

  getRefferalNumberCount() {
    return http.get("admin/refferalNumberCount");
  }

  // New method to handle newsletter subscription
  subscribeNewsletter(email) {
    return http.post("/userAuth/subscriber", { email });
  }
  getSubscriptionDetails(email) {
    return http.post("/stripe/subscriptiondetail", { user_email: email });
  }

  sendTransactionDetails(data) {
    return http.post("/email/send-transaction-details", {
      email: data.email,
      transactionData: {
        reservationCode: data.reservationCode,
        propertyTitle: data.propertyTitle,
        propertyAddress: data.propertyAddress,
        checkInDate: data.checkInDate,
        checkOutDate: data.checkOutDate,
        guestCount: data.guestCount,
        roomCount: data.roomCount,
        pointsUsed: data.pointsUsed,
        serviceFee: data.serviceFee,
        hostName: data.hostName,
        totalAmount: data.totalAmount,
        propertyImage: data.propertyImage
      }
    });
  }

  contactHost(data) {
    return http.post("/email/contact-host", {
      guestEmail: data.guestEmail,
      guestName: data.guestName,
      hostEmail: data.hostEmail,
      hostName: data.hostName,
      propertyTitle: data.propertyTitle,
      checkInDate: data.checkInDate,
      checkOutDate: data.checkOutDate,
      guestMessage: data.guestMessage
    });
  }

  syncCalendar(data) {
    return http.post("/property/calendar/v1/sync", data);
  }
  getReferralUser(route, data) {
    return http.post(route, data);
  }


// innovator post routes
  createInnovatorPost(route, data) {
    return http.post(route, data);
  }
  getInnovatorPosts(route, data) {
    return http.get(route, data);
  }


}

export default new userService();
