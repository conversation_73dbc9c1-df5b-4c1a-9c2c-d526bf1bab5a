import userServices from 'services/httpService/userAuth/userServices';

export const login = (data) => (dispatch) => {
  dispatch({
    type: 'User_login_pending',
    payload: 'pending',
  });

  return userServices.login(data).then(
    (response) => {
      if (response.data.token) {
        localStorage.setItem('localdealtoken', JSON.stringify(response.data));

        dispatch({
          type: 'User_login_Success',
          payload: 'done',
        });
      } else {
        dispatch({
          type: 'User_Error',
          payload: 'Token Not Valid',
        });
        // eslint-disable-next-line
        throw 'Some problem ';
      }

      return Promise.resolve();
    },
    (error) => {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      dispatch({
        type: 'User_Error',
        payload: message,
      });


      return Promise.reject();
    }
  );
};

export const applyForForgetPass = (data) => {
  userServices.applyForForgetPass(data).then(
    (response) => {
      return response.data;
    },
    (error) => {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      return message;
    }
  );
};

