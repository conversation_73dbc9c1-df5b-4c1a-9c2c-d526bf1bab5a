import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
  fname: { type: String, default: null },
  lname: { type: String, default: null },
  email: { type: String, unique: true, required: true },
  recoveryEmail: { type: String, unique: true, sparse: true, },
  referredBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Users' },
  referralCode: { type: String, unique: true },
  username: { type: String },
  pic: { type: String, default: "https://bnbpictures.s3.amazonaws.com/emptyProfilepic.jpeg" },
  stripe_access_token: { type: String, default: null },
  stripe_account_id: { type: String, default: null },
  stripe_refresh_token: { type: String, default: null },
  stripeCustomerId: { type: String, default: null }, // 🆕 Stores Stripe Customer ID
  subscriptionId: { type: String, default: null }, // 🆕 Stores Stripe Subscription ID
  subscription: { type: Boolean, default: false }, // 🆕 Active Subscription?
  subscriptionStatus: { type: String, enum: ["active", "canceled", "pending_cancellation", "unpaid", "trialing"], default: "active" }, // 🆕 Status of Subscription
  nextBillingDate: { type: Date, default: null }, // 🆕 Next Billing Date
  subscriptionCancelAt: { type: Date, default: null },
  paymentMethodId: { type: String, default: null }, // 🆕 Stores Stripe Payment Method ID
  fbId: { type: String },
  googleId: { type: String },
  registeredBy: { type: String },
  bday: { type: Date, default: null },
  gender: { type: String, default: null },

  address: { type: String, default: null },
  phoneNumber: { type: String, default: null },
  country: { type: String, default: null },
  town: { type: String, default: null },
  cardInfo: [{
    paymentMethodId: String,
    cardName: String,
    cardNumber: String,
    expiryDate: String,
    cardType: String,
    cvv: String,
    country: String,
    last4: String,
    funding: String,
    primary: { type: Boolean, default: false }
  }],

  reservationCount: { type: Number, default: 0 }, // 🔁 Tracks how many reservations user has made
  membershipReminderCount: { type: Number, default: 0 }, // 📬 How many times we've reminded them
  lastReminderSentAt: { type: Date, default: null }, // 🕒 When the last reminder was sent
  autoCharged: { type: Boolean, default: false }, // 💳 Has the user been auto-charged?
  autoChargeDate: { type: Date, default: null }, // 📅 When the auto charge was done
  firstReservationDate: { type: Date, default: null }, // 📅 When the user made their first reservation

  paymentRetryCount: { type: Number, default: 0 },        // count of failed payment attempts
  lastPaymentError: { type: String, default: '' },        // last payment error message
  lastPaymentAttemptAt: { type: Date },                    // when last payment attempt happened
  paymentFailed: { type: Boolean, default: false },


  subRole: { type: String, enum: ["Elite", "Regular"], default: "Regular" }, // 🆕 Role Based on Subscription
  points: { type: Number, default: 0 },
  isEnable: { type: String, default: "yes" },
  referrelUsers: [{ type: mongoose.Schema.Types.ObjectId, ref: "Users" }],
  isInfluencer: { type: String, default: "Pending" },
  roles: { type: Array, default: ["Guest", "Host"] },
  otp: { type: String },
  isVerifphone: { type: Boolean, default: false },
  pass: { type: String },
  token: { type: String },
  following: { type: Number, default: 0 },
  follower: { type: Number, default: 0 },
  pendingPoints: { type: Number, default: 0 },
  verify: { type: String, default: "no" },
  counterId: { type: Number },
  date: { type: Date, default: new Date() },
  isAdmin: { type: Boolean, default: false },
  about: { type: String },
  interest: { type: Array },
  pastTrips: { type: Array },
  loginAs: { type: String },
  governmentID: {
    frontendPic: { type: String },
    backendPic: { type: String },
  },
  failedLoginAttempts: { type: Number, default: 0 },
  lockUntil: { type: Date },
  lastLogin: { type: Date, default: null }
});

// Automatically update the lastLogin field on save
userSchema.pre("save", function (next) {
  if (this.isModified("lastLogin") || this.isNew) {
    this.lastLogin = new Date();
  }
  next();
});

export const User = mongoose.model("Users", userSchema);
