import React, { useState } from 'react';
import { useMutation } from 'react-query';
import Modal from "components/Modal/Modal";
import userServices from "services/httpService/userAuth/userServices";

const ContactHostModal = ({ isOpen, onClose, hostData }) => {
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');


  const contactHostMutation = useMutation(
    (formData) => {
      return userServices.contactHost(formData);
    },
    {
      onSuccess: () => {
        setSuccess('Message sent successfully!');
        setMessage('');
        setTimeout(() => {
          onClose();
          setSuccess('');
        }, 2000);
      },
      onError: (error) => {
        console.error("Error sending message:", error);
        setError(error.response?.data?.message || 'Failed to send message');
      },
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!hostData || !hostData.user) {
      console.error("hostData or hostData.user is undefined", hostData);
      setError("Host information is missing.");
      return;
    }

    const formData = {
      guestEmail: localStorage.getItem('userEmail'),
      guestName: localStorage.getItem('userName'),
      hostEmail: hostData.user.email,
      hostName: `${hostData.user.fname} ${hostData.user.lname}`,
      propertyTitle: hostData.propertyTitle,
      checkInDate: hostData.checkInDate,
      checkOutDate: hostData.checkOutDate,
      guestMessage: message
    };

    contactHostMutation.mutate(formData);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="md:p-6 p-5 h-full flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-gray-800">Contact Host</h2>
          <button 
            onClick={() => {
              onClose();
            }}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md">
            {success}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex flex-col flex-grow">
          <div className="flex-grow">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email to {hostData?.user?.fname || "Host"}
            </label>
            <textarea
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
              }}
              className="w-full md:h-[300px] h-[200px] p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="Write your message here..."
              required
            />
          </div>

          {/* Buttons */}
          <div className="flex gap-4 justify-end mt-auto">
            <button
              type="button"
              onClick={() => {
                onClose();
              }}
              className="px-6 py-2 text-gray-700 bg-gray-100 rounded-full hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={contactHostMutation.isLoading}
              className="px-6 py-2 text-white bg-[#2459BF] rounded-full hover:bg-blue-700 flex items-center justify-center min-w-[120px]"
            >
              {contactHostMutation.isLoading ? 'Sending...' : 'Send Message'}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ContactHostModal;
