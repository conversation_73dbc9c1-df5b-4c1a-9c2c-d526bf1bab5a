import React, { useState, useRef, useEffect, useCallback } from 'react';
import LazyImage from 'components/LazyImage/LazyImage';

/**
 * Optimized image component with WebP support, responsive images, and CDN optimization
 */
const OptimizedImage = ({
  src,
  alt,
  className = '',
  width,
  height,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  priority = false,
  quality = 80,
  placeholder = 'blur',
  blurDataURL,
  onLoad,
  onError,
  ...props
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef(null);

  // Generate optimized image URLs
  const generateImageUrls = useCallback((originalSrc) => {
    if (!originalSrc) return { webp: null, fallback: null, srcSet: null };

    // Check if it's already a CDN URL or external URL
    const isExternal = originalSrc.startsWith('http');
    const isAsset = originalSrc.startsWith('/assets/');
    
    if (isExternal && !originalSrc.includes('your-cdn-domain.com')) {
      // External image - return as is
      return {
        webp: null,
        fallback: originalSrc,
        srcSet: null
      };
    }

    // For local assets, generate optimized versions
    const basePath = isAsset ? originalSrc : `/assets/img/${originalSrc}`;
    const extension = basePath.split('.').pop();
    const baseName = basePath.replace(`.${extension}`, '');

    // Generate WebP version
    const webpSrc = `${baseName}.webp`;
    
    // Generate responsive srcSet
    const srcSet = [
      `${baseName}_400w.${extension} 400w`,
      `${baseName}_800w.${extension} 800w`,
      `${baseName}_1200w.${extension} 1200w`,
      `${basePath} 1600w`
    ].join(', ');

    const webpSrcSet = [
      `${baseName}_400w.webp 400w`,
      `${baseName}_800w.webp 800w`,
      `${baseName}_1200w.webp 1200w`,
      `${webpSrc} 1600w`
    ].join(', ');

    return {
      webp: webpSrc,
      webpSrcSet,
      fallback: basePath,
      srcSet
    };
  }, []);

  // Check WebP support
  const [supportsWebP, setSupportsWebP] = useState(null);
  
  useEffect(() => {
    const checkWebPSupport = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const dataURL = canvas.toDataURL('image/webp');
      setSupportsWebP(dataURL.indexOf('data:image/webp') === 0);
    };

    if (supportsWebP === null) {
      checkWebPSupport();
    }
  }, [supportsWebP]);

  // Generate blur placeholder
  const generateBlurPlaceholder = useCallback((src) => {
    if (blurDataURL) return blurDataURL;
    
    // Generate a simple blur placeholder
    const canvas = document.createElement('canvas');
    canvas.width = 10;
    canvas.height = 10;
    const ctx = canvas.getContext('2d');
    
    // Create a simple gradient as placeholder
    const gradient = ctx.createLinearGradient(0, 0, 10, 10);
    gradient.addColorStop(0, '#f3f4f6');
    gradient.addColorStop(1, '#e5e7eb');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 10, 10);
    
    return canvas.toDataURL();
  }, [blurDataURL]);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setImageError(true);
    onError?.();
  }, [onError]);

  // Preload image if priority is set
  useEffect(() => {
    if (priority && src) {
      const { webp, fallback } = generateImageUrls(src);
      const preloadSrc = (supportsWebP && webp) ? webp : fallback;
      
      if (preloadSrc) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = preloadSrc;
        document.head.appendChild(link);
        
        return () => {
          document.head.removeChild(link);
        };
      }
    }
  }, [priority, src, supportsWebP, generateImageUrls]);

  if (!src) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <span className="text-gray-400 text-sm">No image</span>
      </div>
    );
  }

  if (imageError) {
    return (
      <div 
        className={`bg-gray-100 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <span className="text-gray-500 text-sm">Failed to load image</span>
      </div>
    );
  }

  const { webp, webpSrcSet, fallback, srcSet } = generateImageUrls(src);

  // Use LazyImage for non-priority images
  if (!priority) {
    return (
      <LazyImage
        src={fallback}
        webpSrc={webp}
        alt={alt}
        className={className}
        sizes={sizes}
        onLoad={handleLoad}
        onError={handleError}
        placeholder={
          placeholder === 'blur' ? (
            <div 
              className={`bg-gray-200 animate-pulse ${className}`}
              style={{
                backgroundImage: `url(${generateBlurPlaceholder(src)})`,
                backgroundSize: 'cover',
                filter: 'blur(5px)',
                width,
                height
              }}
            />
          ) : null
        }
        {...props}
      />
    );
  }

  // Priority images load immediately
  return (
    <div className={`relative overflow-hidden ${className}`} ref={imgRef}>
      {!isLoaded && placeholder === 'blur' && (
        <div
          className="absolute inset-0 bg-gray-200"
          style={{
            backgroundImage: `url(${generateBlurPlaceholder(src)})`,
            backgroundSize: 'cover',
            filter: 'blur(5px)'
          }}
        />
      )}
      
      <picture>
        {webp && supportsWebP && (
          <source
            srcSet={webpSrcSet || webp}
            sizes={sizes}
            type="image/webp"
          />
        )}
        <img
          src={fallback}
          srcSet={srcSet}
          sizes={sizes}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={handleLoad}
          onError={handleError}
          style={{
            objectFit: 'cover',
            width: '100%',
            height: '100%'
          }}
          {...props}
        />
      </picture>
    </div>
  );
};

/**
 * Hook for image optimization utilities
 */
export const useImageOptimization = () => {
  const [webpSupport, setWebpSupport] = useState(null);
  
  useEffect(() => {
    const checkWebPSupport = async () => {
      try {
        const webpData = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        const img = new Image();
        img.onload = () => setWebpSupport(true);
        img.onerror = () => setWebpSupport(false);
        img.src = webpData;
      } catch {
        setWebpSupport(false);
      }
    };
    
    checkWebPSupport();
  }, []);

  const optimizeImageUrl = useCallback((url, options = {}) => {
    const { width, height, quality = 80, format } = options;
    
    if (!url) return url;
    
    // If it's a CDN URL, add optimization parameters
    if (url.includes('your-cdn-domain.com')) {
      const params = new URLSearchParams();
      if (width) params.set('w', width);
      if (height) params.set('h', height);
      if (quality) params.set('q', quality);
      if (format) params.set('f', format);
      
      return `${url}?${params.toString()}`;
    }
    
    return url;
  }, []);

  const preloadImage = useCallback((src) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = resolve;
      img.onerror = reject;
      img.src = src;
    });
  }, []);

  return {
    webpSupport,
    optimizeImageUrl,
    preloadImage
  };
};

export default OptimizedImage;
