import axios from 'axios';
import config from '../config';

/**
 * Optimized API service with caching, retry logic, and performance optimizations
 */
class OptimizedApiService {
  constructor() {
    this.cache = new Map();
    this.pendingRequests = new Map();
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      retryCondition: (error) => {
        return error.response?.status >= 500 || error.code === 'NETWORK_ERROR';
      }
    };

    // Create axios instance with optimized defaults
    this.axiosInstance = axios.create({
      baseURL: config.endPoint,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    // Request interceptor for caching and deduplication
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Add timestamp for cache busting when needed
        if (config.bustCache) {
          config.params = { ...config.params, _t: Date.now() };
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for caching
    this.axiosInstance.interceptors.response.use(
      (response) => {
        // Cache successful responses
        if (response.config.cache && response.status === 200) {
          const cacheKey = this.getCacheKey(response.config);
          this.setCache(cacheKey, response.data, response.config.cacheTTL);
        }
        return response;
      },
      (error) => {
        // Handle errors and retry logic
        return this.handleError(error);
      }
    );
  }

  /**
   * Generate cache key from request config
   */
  getCacheKey(config) {
    const { method, url, params, data } = config;
    return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`;
  }

  /**
   * Set cache with TTL
   */
  setCache(key, data, ttl = 5 * 60 * 1000) { // Default 5 minutes
    const expiry = Date.now() + ttl;
    this.cache.set(key, { data, expiry });
    
    // Clean up expired entries periodically
    if (this.cache.size > 100) {
      this.cleanExpiredCache();
    }
  }

  /**
   * Get cached data
   */
  getCache(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    if (Date.now() > cached.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * Clean expired cache entries
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now > value.expiry) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Handle request errors with retry logic
   */
  async handleError(error) {
    const config = error.config;
    
    if (!config || !this.retryConfig.retryCondition(error)) {
      return Promise.reject(error);
    }

    config.retryCount = config.retryCount || 0;
    
    if (config.retryCount >= this.retryConfig.maxRetries) {
      return Promise.reject(error);
    }

    config.retryCount++;
    
    // Exponential backoff
    const delay = this.retryConfig.retryDelay * Math.pow(2, config.retryCount - 1);
    
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return this.axiosInstance(config);
  }

  /**
   * Optimized GET request with caching and deduplication
   */
  async get(url, options = {}) {
    const {
      cache = true,
      cacheTTL = 5 * 60 * 1000,
      deduplicate = true,
      ...axiosConfig
    } = options;

    const config = {
      method: 'GET',
      url,
      cache,
      cacheTTL,
      ...axiosConfig
    };

    const cacheKey = this.getCacheKey(config);

    // Check cache first
    if (cache) {
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        return { data: cachedData };
      }
    }

    // Deduplicate concurrent requests
    if (deduplicate && this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey);
    }

    const requestPromise = this.axiosInstance(config);
    
    if (deduplicate) {
      this.pendingRequests.set(cacheKey, requestPromise);
      
      // Clean up pending request after completion
      requestPromise.finally(() => {
        this.pendingRequests.delete(cacheKey);
      });
    }

    return requestPromise;
  }

  /**
   * Optimized POST request
   */
  async post(url, data, options = {}) {
    const config = {
      method: 'POST',
      url,
      data,
      ...options
    };

    return this.axiosInstance(config);
  }

  /**
   * Batch multiple requests
   */
  async batch(requests) {
    const promises = requests.map(request => {
      const { method = 'GET', url, data, ...options } = request;
      
      switch (method.toUpperCase()) {
        case 'GET':
          return this.get(url, options);
        case 'POST':
          return this.post(url, data, options);
        default:
          return this.axiosInstance({ method, url, data, ...options });
      }
    });

    return Promise.allSettled(promises);
  }

  /**
   * Prefetch data for future use
   */
  async prefetch(url, options = {}) {
    try {
      await this.get(url, { ...options, cache: true });
    } catch (error) {
      console.warn('Prefetch failed:', url, error);
    }
  }

  /**
   * Paginated data loading with caching
   */
  async getPaginated(url, options = {}) {
    const {
      page = 1,
      limit = 10,
      cache = true,
      ...otherOptions
    } = options;

    const paginatedUrl = `${url}?page=${page}&limit=${limit}`;
    
    return this.get(paginatedUrl, {
      cache,
      cacheTTL: 2 * 60 * 1000, // Shorter cache for paginated data
      ...otherOptions
    });
  }

  /**
   * Infinite scroll data loading
   */
  async getInfiniteScroll(url, options = {}) {
    const {
      cursor,
      limit = 10,
      cache = true,
      ...otherOptions
    } = options;

    const params = { limit };
    if (cursor) {
      params.cursor = cursor;
    }

    return this.get(url, {
      params,
      cache,
      cacheTTL: 1 * 60 * 1000, // Short cache for infinite scroll
      ...otherOptions
    });
  }

  /**
   * Clear cache for specific pattern or all
   */
  clearCache(pattern = null) {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, value] of this.cache.entries()) {
      if (now > value.expiry) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      total: this.cache.size,
      valid: validEntries,
      expired: expiredEntries,
      hitRate: this.hitRate || 0
    };
  }

  /**
   * Optimized property loading for landing page
   */
  async getProperties(options = {}) {
    const {
      page = 1,
      limit = 8,
      status = 'Active',
      cache = true,
      ...otherOptions
    } = options;

    return this.getPaginated('/property/getAllPropertyAvailable', {
      page,
      limit,
      cache,
      cacheTTL: 10 * 60 * 1000, // 10 minutes cache for properties
      params: { status },
      ...otherOptions
    });
  }

  /**
   * Preload critical data
   */
  async preloadCriticalData() {
    const criticalEndpoints = [
      '/property/getAllPropertyAvailable?page=1&limit=8&status=Active',
      '/blogs?limit=5',
      '/user/profile'
    ];

    const preloadPromises = criticalEndpoints.map(endpoint => 
      this.prefetch(endpoint).catch(() => {}) // Ignore errors for preloading
    );

    await Promise.allSettled(preloadPromises);
  }
}

// Create singleton instance
const optimizedApiService = new OptimizedApiService();

export default optimizedApiService;
