import mongoose from "mongoose";

const subscriptionSchema = new mongoose.Schema({
  email: { type: String, required: true },
  ipAddress: { type: String, required: true },
  userAgent: { type: String, required: true },
  browser: { type: String },
  os: { type: String },
  timeStamp: {
    type: Date,
    default: Date.now,
  },
});

const Subscription = mongoose.model('Newshelter', subscriptionSchema);

export { Subscription };
