import React, { useEffect } from "react";
import Container from "@mui/material/Container";
import { useLocation, useNavigate } from "react-router-dom";
import { useState } from "react";
import { localStorageData } from "services/auth/localStorageData";
import userServices from "services/httpService/userAuth/userServices";
import { useMutation } from "react-query";
import { toast } from "react-toastify";
import ErrorService from "services/formatError/ErrorService";

function ConfirmReservation() {
  const navigate = useNavigate();
  const [renderedfav, setRenderedfav] = useState(false);
  const location = useLocation();
  const { state } = location;

  // Format date for display
  function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  }

  // Format time for display
  function formatTime(dateString) {
    const date = new Date(dateString);
    const hour = date.getHours();
    const minute = date.getMinutes();
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const formattedHour = hour % 12 || 12;
    const formattedMinute = `${minute < 10 ? '0' : ''}${minute}`;
    return `${formattedHour}:${formattedMinute}${ampm}`;
  }

  // Handle favorite property
  const handleConfirmList = () => {
    mutate({ propertyId: state.property_id, userId: localStorageData("_id") });
  };

  const { mutate } = useMutation(
    (token) => userServices.Wishlist("/property/updatePropertyforFavourite", token),
    {
      onError: (error) => {
        toast.error(ErrorService.uniformError(error));
      },
      onSuccess: () => {
        setRenderedfav(!renderedfav);
        toast.success(renderedfav ? "Removed from favorites" : "Added to favorites");
      },
    }
  );

  // Send confirmation email
  const { mutate: sendEmailMutation, isLoading: isSendingEmail } = useMutation(
    (transactionData) => userServices.sendTransactionDetails(transactionData),
    {
      onSuccess: () => {
        toast.success("Booking confirmation sent to your email!");
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || "Failed to send booking confirmation");
      },
    }
  );

  const handleSendEmail = () => {
    if (!state?.propertyDetail) {
      toast.error("Reservation details not found");
      return;
    }

    const transactionData = {
      email: localStorageData("email"),
      reservationCode: state.property_id,
      propertyTitle: state.propertyDetail.title,
      propertyAddress: state.propertyDetail.address,
      checkInDate: `${formatDate(state.offerState.checkIn)}\nCheck-in time is ${formatTime(state.offerState.checkIn)}`,
      checkOutDate: `${formatDate(state.offerState.checkOut)}\nCheck-out time is ${formatTime(state.offerState.checkOut)}`,
      guestCount: state.offerState.guest,
      roomCount: state.offerState.room,
      pointsUsed: state.offerState.points || 0,
      serviceFee: state.offerState.serviceFee || 0,
      hostName: state.propertyDetail.user?.fname || "Unknown Host",
      totalAmount: `${state.offerState.points || 0} points + $${state.offerState.serviceFee || 0}`,
      propertyImage: state.propertyDetail.images?.[0] || '',
    };

    sendEmailMutation(transactionData);
  };

  if (!state?.propertyDetail) {
    return (
      <Container maxWidth="xl">
        <div className="mt-8 text-center">
          <h2 className="text-2xl font-semibold text-gray-700">Reservation Details Not Found</h2>
          <p className="mt-2 text-gray-600">Please try making a reservation again.</p>
          <button
            onClick={() => navigate("/")}
            className="mt-4 px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition"
          >
            Return Home
          </button>
        </div>
      </Container>
    );
  }
  function formatCurrency(amount) {
    return Number(amount).toFixed(2);
  }
  return (
    <Container maxWidth="xl">
      <div className="mt-8">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-green-600 mb-2">Reservation Confirmed!</h1>
            <p className="text-gray-600">Your stay at {state.propertyDetail.title} has been confirmed.</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Property Details */}
            <div className="space-y-6">
              <div className="relative">
                <img
                  src={state.propertyDetail.pics[0]}
                  alt={state.propertyDetail.title}
                  className="w-full h-64 object-cover rounded-lg"
                />
                <button
                  onClick={handleConfirmList}
                  className={`absolute top-4 right-4 text-2xl ${
                    renderedfav ? "text-red-500" : "text-gray-400"
                  }`}
                >
                  <i className={`fa-heart ${renderedfav ? "fas" : "far"}`}></i>
                </button>
              </div>

              <div>
                <h2 className="text-2xl font-semibold mb-2">{state.propertyDetail.title}</h2>
                <p className="text-gray-600">{state.propertyDetail.address}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold mb-1">Check-in</h3>
                  <p className="text-gray-600">{formatDate(state.offerState.checkIn)}</p>
                  <p className="text-sm text-gray-500">{formatTime(state.offerState.checkIn)}</p>
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Check-out</h3>
                  <p className="text-gray-600">{formatDate(state.offerState.checkOut)}</p>
                  <p className="text-sm text-gray-500">{formatTime(state.offerState.checkOut)}</p>
                </div>
              </div>
            </div>

            {/* Reservation Summary */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Reservation Summary</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Reservation Code</span>
                  <span className="font-medium">{state.property_id}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Guests</span>
                  <span>{state.offerState.guest}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Rooms</span>
                  <span>{state.offerState.room}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Points Used</span>
                  <span>{state.offerState.points} points</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Service Fee</span>
                  <span>${formatCurrency(state.offerState.serviceFee)}</span>
                </div>
                
                <div className="border-t pt-4 mt-4">
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>{state.offerState.points} points + ${formatCurrency(state.offerState.serviceFee)}</span>
                  </div>
                </div>
              </div>

              <div className="mt-6 space-y-4">
                <button
                  onClick={handleSendEmail}
                  disabled={isSendingEmail}
                  className="w-full py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition disabled:bg-blue-300"
                >
                  {isSendingEmail ? "Sending..." : "Send Confirmation Email"}
                </button>
                
                <button
                  onClick={() => navigate("/")}
                  className="w-full py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition"
                >
                  Return to Home
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
}

export default ConfirmReservation;
