#!/usr/bin/env node

/**
 * Dependency optimization script for BnbYond
 * Identifies and suggests replacements for heavy dependencies
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 Starting dependency optimization...\n');

// Heavy dependencies and their lighter alternatives
const dependencyReplacements = {
  'moment': {
    replacement: 'date-fns',
    reason: 'date-fns is tree-shakable and much smaller',
    size: 'moment: ~67KB → date-fns: ~13KB (tree-shaken)',
    action: 'replace'
  },
  'lodash': {
    replacement: 'lodash-es',
    reason: 'lodash-es supports tree-shaking',
    size: 'lodash: ~70KB → lodash-es: ~10KB (tree-shaken)',
    action: 'replace'
  },
  'react-moment': {
    replacement: 'date-fns + custom hook',
    reason: 'Eliminates moment.js dependency',
    size: 'react-moment + moment: ~70KB → date-fns: ~13KB',
    action: 'replace'
  }
};

// Duplicate dependencies to consolidate
const duplicateDependencies = {
  'react-rating': {
    replacement: 'Custom rating component',
    reason: 'Simple rating can be implemented with minimal code',
    action: 'implement'
  },
  'sweetalert2': {
    replacement: 'react-hot-toast or custom modal',
    reason: 'Lighter toast notifications',
    action: 'replace'
  }
};

/**
 * Analyze package.json for heavy dependencies
 */
function analyzeDependencies() {
  const packageJsonPath = path.join(__dirname, '../package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const allDeps = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies
  };

  console.log('🔍 Analyzing dependencies...\n');

  const foundHeavyDeps = [];
  const foundDuplicates = [];

  // Check for heavy dependencies
  Object.keys(dependencyReplacements).forEach(dep => {
    if (allDeps[dep]) {
      foundHeavyDeps.push({
        name: dep,
        version: allDeps[dep],
        ...dependencyReplacements[dep]
      });
    }
  });

  // Check for duplicates
  Object.keys(duplicateDependencies).forEach(dep => {
    if (allDeps[dep]) {
      foundDuplicates.push({
        name: dep,
        version: allDeps[dep],
        ...duplicateDependencies[dep]
      });
    }
  });

  return { foundHeavyDeps, foundDuplicates, allDeps };
}

/**
 * Create date-fns replacement for moment
 */
function createDateFnsReplacement() {
  const utilsPath = path.join(__dirname, '../src/utils/dateUtils.js');
  
  const dateUtilsContent = `/**
 * Date utilities using date-fns (lightweight moment.js replacement)
 */
import { format, parseISO, isValid, differenceInDays, addDays, subDays } from 'date-fns';

/**
 * Format date string
 */
export const formatDate = (date, formatString = 'yyyy-MM-dd') => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return isValid(dateObj) ? format(dateObj, formatString) : '';
  } catch (error) {
    console.error('Date formatting error:', error);
    return '';
  }
};

/**
 * Format date for display
 */
export const formatDisplayDate = (date) => {
  return formatDate(date, 'MMM dd, yyyy');
};

/**
 * Format date and time
 */
export const formatDateTime = (date) => {
  return formatDate(date, 'MMM dd, yyyy HH:mm');
};

/**
 * Get relative time (e.g., "2 days ago")
 */
export const getRelativeTime = (date) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const now = new Date();
    const days = differenceInDays(now, dateObj);
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return \`\${days} days ago\`;
    if (days < 30) return \`\${Math.floor(days / 7)} weeks ago\`;
    if (days < 365) return \`\${Math.floor(days / 30)} months ago\`;
    return \`\${Math.floor(days / 365)} years ago\`;
  } catch (error) {
    console.error('Relative time error:', error);
    return '';
  }
};

/**
 * Add days to date
 */
export const addDaysToDate = (date, days) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return addDays(dateObj, days);
  } catch (error) {
    console.error('Add days error:', error);
    return date;
  }
};

/**
 * Subtract days from date
 */
export const subtractDaysFromDate = (date, days) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return subDays(dateObj, days);
  } catch (error) {
    console.error('Subtract days error:', error);
    return date;
  }
};

/**
 * Check if date is valid
 */
export const isValidDate = (date) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return isValid(dateObj);
  } catch (error) {
    return false;
  }
};

/**
 * Get days between two dates
 */
export const getDaysBetween = (startDate, endDate) => {
  try {
    const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
    return differenceInDays(end, start);
  } catch (error) {
    console.error('Days between error:', error);
    return 0;
  }
};

export default {
  formatDate,
  formatDisplayDate,
  formatDateTime,
  getRelativeTime,
  addDaysToDate,
  subtractDaysFromDate,
  isValidDate,
  getDaysBetween
};`;

  fs.writeFileSync(utilsPath, dateUtilsContent);
  console.log('✅ Created date-fns utility at src/utils/dateUtils.js');
}

/**
 * Create custom rating component
 */
function createCustomRating() {
  const componentPath = path.join(__dirname, '../src/components/CustomRating/CustomRating.jsx');
  const componentDir = path.dirname(componentPath);
  
  if (!fs.existsSync(componentDir)) {
    fs.mkdirSync(componentDir, { recursive: true });
  }

  const ratingContent = `import React, { useState } from 'react';

/**
 * Lightweight custom rating component
 */
const CustomRating = ({ 
  value = 0, 
  onChange, 
  max = 5, 
  size = 20, 
  color = '#ffc107',
  emptyColor = '#e4e5e9',
  readonly = false,
  className = ''
}) => {
  const [hoverValue, setHoverValue] = useState(0);

  const handleClick = (rating) => {
    if (!readonly && onChange) {
      onChange(rating);
    }
  };

  const handleMouseEnter = (rating) => {
    if (!readonly) {
      setHoverValue(rating);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverValue(0);
    }
  };

  const renderStar = (index) => {
    const rating = index + 1;
    const filled = (hoverValue || value) >= rating;

    return (
      <span
        key={index}
        className={\`inline-block cursor-\${readonly ? 'default' : 'pointer'} \${className}\`}
        style={{
          fontSize: size,
          color: filled ? color : emptyColor,
          transition: 'color 0.2s ease'
        }}
        onClick={() => handleClick(rating)}
        onMouseEnter={() => handleMouseEnter(rating)}
        onMouseLeave={handleMouseLeave}
      >
        ★
      </span>
    );
  };

  return (
    <div className="inline-flex items-center">
      {Array.from({ length: max }, (_, index) => renderStar(index))}
    </div>
  );
};

export default CustomRating;`;

  fs.writeFileSync(componentPath, ratingContent);
  console.log('✅ Created custom rating component at src/components/CustomRating/CustomRating.jsx');
}

/**
 * Generate optimization report
 */
function generateOptimizationReport(analysis) {
  console.log('📊 Dependency Optimization Report');
  console.log('='.repeat(50));

  if (analysis.foundHeavyDeps.length > 0) {
    console.log('\n🔴 Heavy Dependencies Found:');
    analysis.foundHeavyDeps.forEach(dep => {
      console.log(`  • ${dep.name} (${dep.version})`);
      console.log(`    → Replace with: ${dep.replacement}`);
      console.log(`    → Reason: ${dep.reason}`);
      console.log(`    → Size impact: ${dep.size}`);
      console.log('');
    });
  }

  if (analysis.foundDuplicates.length > 0) {
    console.log('🟡 Duplicate/Unnecessary Dependencies:');
    analysis.foundDuplicates.forEach(dep => {
      console.log(`  • ${dep.name} (${dep.version})`);
      console.log(`    → Replace with: ${dep.replacement}`);
      console.log(`    → Reason: ${dep.reason}`);
      console.log('');
    });
  }

  // Calculate potential savings
  const potentialSavings = analysis.foundHeavyDeps.length * 50 + analysis.foundDuplicates.length * 20; // Rough estimate in KB
  
  console.log('💡 Optimization Recommendations:');
  console.log(`  • Potential bundle size reduction: ~${potentialSavings}KB`);
  console.log('  • Implement tree-shaking for better optimization');
  console.log('  • Use dynamic imports for heavy components');
  console.log('  • Consider CDN for large libraries');

  return potentialSavings;
}

/**
 * Main optimization function
 */
async function optimizeDependencies() {
  try {
    const analysis = analyzeDependencies();
    
    // Create replacement utilities
    if (analysis.foundHeavyDeps.some(dep => dep.name === 'moment')) {
      createDateFnsReplacement();
    }
    
    if (analysis.foundDuplicates.some(dep => dep.name === 'react-rating')) {
      createCustomRating();
    }

    // Generate report
    const savings = generateOptimizationReport(analysis);

    console.log('\n✅ Dependency optimization completed!');
    console.log('\n📝 Next Steps:');
    console.log('1. Install lighter alternatives: npm install date-fns');
    console.log('2. Replace moment imports with dateUtils');
    console.log('3. Replace react-rating with CustomRating component');
    console.log('4. Remove unused dependencies: npm uninstall moment react-rating');
    console.log('5. Run bundle analyzer to verify improvements');

    return savings;

  } catch (error) {
    console.error('❌ Dependency optimization failed:', error);
    process.exit(1);
  }
}

// Run optimization
optimizeDependencies();
