name: Backend deployment pipeline

on:
  push:
    branches: 
      - main
    paths:
      - 'api/**'
      - 'deploy/backend/**'
      - '.github/workflows/backend-*.yml'
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'main'

jobs:
  deploy:
    uses: ./.github/workflows/backend-deploy.yml
    secrets:
      aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      s3_aws_access_key_id: ${{ secrets.S3_AWS_ACCESS_KEY_ID }}
      s3_aws_secret_access_key: ${{ secrets.S3_AWS_SECRET_ACCESS_KEY }}
      mongo_uri: ${{ secrets.MONGO_URI }}
      token_key: ${{ secrets.TOKEN_KEY }}
      twilio_account_id: ${{ secrets.TWILIO_ACCOUNT_ID }}
      twilio_auth_token: ${{ secrets.TWILIO_AUTH_TOKEN }}
      twilio_test_number: ${{ secrets.TWILIO_TEST_NUMBER }}
      mailjet_key1: ${{ secrets.MAILJET_KEY1 }}
      mailjet_key2: ${{ secrets.MAILJET_KEY2 }}
      stripe_secret_key: ${{ secrets.STRIPE_SECRET_KEY }}
      stripe_public_key: ${{ secrets.STRIPE_PUBLIC_KEY }}
      secret_encryption_key: ${{ secrets.SECRET_ENCRYPTION_KEY }}
      subscription_withaffiliated: ${{ secrets.SUBSCRIPTION_WITHAFFILIATED }}
      subscription_withoutaffiliated: ${{ secrets.SUBSCRIPTION_WITHOUTAFFILIATED }}
      stripe_webhook_secret: ${{ secrets.STRIPE_WEBHOOK_SECRET }}
      sentry_dsn: ${{ secrets.SENTRY_DSN }}
    with:
      environment: production
      ecr_repository: prod-bnbyond-api
      ecs_cluster: bnbyond-prod
      ecs_service: bnbyond-api-services
