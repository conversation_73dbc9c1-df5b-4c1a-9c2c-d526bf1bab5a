/**
 * Custom hook for exporting HTML tables to Excel
 * @returns {Function} export function that takes tableId, filename, and sheet name
 */
export const useExportTableToExcel = () => {
  const exportToExcel = (tableId, filename = 'export', sheetName = 'Sheet1') => {
    // Get the table element
    const table = document.getElementById(tableId);
    if (!table) {
      console.error(`Table with ID "${tableId}" not found`);
      return;
    }
    
    // Create a workbook
    let csv = '';
    const rows = table.querySelectorAll('tr');
    
    // Loop through rows
    for (let i = 0; i < rows.length; i++) {
      const cells = rows[i].querySelectorAll('td, th');
      const rowData = [];
      
      // Loop through cells
      for (let j = 0; j < cells.length; j++) {
        // Clean and escape cell content
        let cellData = cells[j].innerText;
        cellData = cellData.replace(/"/g, '""'); // Escape double quotes
        cellData = `"${cellData}"`; // Wrap in quotes
        rowData.push(cellData);
      }
      
      // Add row to CSV
      csv += rowData.join(',') + '\n';
    }
    
    // Create a download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    // Set up the link properties
    link.href = URL.createObjectURL(blob);
    link.setAttribute('download', `${filename}.csv`);
    link.style.display = 'none';
    
    // Add to document, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return { exportToExcel };
};

export default useExportTableToExcel;