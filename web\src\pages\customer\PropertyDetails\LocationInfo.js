
import React, { useEffect, useState } from 'react';
import axios from 'axios';

const LocationInfo = ({ latitude, longitude }) => {
  const [wikiData, setWikiData] = useState(null);
  const [expanded, setExpanded] = useState(false);

  const shouldTruncate = wikiData && wikiData.length > 1000;
  const displayedText = shouldTruncate && !expanded ? wikiData.slice(0, 600) + "..." : wikiData;
  useEffect(() => {
    const apiUrl = `https://en.wikipedia.org/w/api.php?action=query&format=json&list=geosearch&gscoord=${latitude}%7C${longitude}&gsradius=1000&gslimit=1&origin=*`;

    axios.get(apiUrl)
      .then(response => {
        const pageId = response.data.query.geosearch[0].pageid;
        const extractUrl = `https://en.wikipedia.org/w/api.php?action=query&format=json&prop=extracts&pageids=${pageId}&exintro&explaintext&origin=*`;
        return axios.get(extractUrl);
      })
      .then((response) => {
        const page = response.data.query.pages;
        const firstPageId = Object.keys(page)[0];
        setWikiData(page[firstPageId].extract);
      })
      .catch((error) => {
        console.error("Error fetching Wikipedia data:", error);
      });
  }, [latitude, longitude]);


  return (
    <div>
      {wikiData ? (
        <>
          <div>
            <h2>Wikipedia Information</h2>
            <p>{displayedText}</p>
          </div>
          {shouldTruncate && (
            <div className="mt-5">
              <button
                className="text-[#000000] text-base font-normal underline"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? "Read Less" : "Read More"}
              </button>
            </div>
          )}
        </>
      ) : (
        <div>
          <h2>Wikipedia Information</h2>
          <div className="flex justify-center items-center h-52">
            <span className="text-[#4B4B4B] text-lg">No Wikipedia Information Found</span>
          </div>
        </div>
      )}
    </div>

  );
};

export default LocationInfo;
