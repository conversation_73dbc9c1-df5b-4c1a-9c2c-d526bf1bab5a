import { Router } from "express";
import checkRoleMiddleware from "../middleware/routeMiddleware";
import { Controller as AdminController } from "../controllers/admin";

const router = Router();

// Authentication routes - no middleware required
router.post("/adminlogin", AdminController.adminLogin);

// Protected admin routes - require role-based access
const adminAccessControl = checkRoleMiddleware(['subadmin', 'Blogger', 'Financial']);

// User management routes
router.get("/fetchAllUser", adminAccessControl, AdminController.fetchAllUser);

// Analytics routes
router.get("/refferalNumberCount", adminAccessControl, AdminController.refferalNumberCount);

export default router;
