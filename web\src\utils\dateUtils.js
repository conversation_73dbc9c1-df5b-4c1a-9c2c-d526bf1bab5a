/**
 * Date utilities using date-fns (lightweight moment.js replacement)
 */
import { format, parseISO, isValid, differenceInDays, addDays, subDays } from 'date-fns';

/**
 * Format date string
 */
export const formatDate = (date, formatString = 'yyyy-MM-dd') => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return isValid(dateObj) ? format(dateObj, formatString) : '';
  } catch (error) {
    console.error('Date formatting error:', error);
    return '';
  }
};

/**
 * Format date for display
 */
export const formatDisplayDate = (date) => {
  return formatDate(date, 'MMM dd, yyyy');
};

/**
 * Format date and time
 */
export const formatDateTime = (date) => {
  return formatDate(date, 'MMM dd, yyyy HH:mm');
};

/**
 * Get relative time (e.g., "2 days ago")
 */
export const getRelativeTime = (date) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const now = new Date();
    const days = differenceInDays(now, dateObj);
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    if (days < 30) return `${Math.floor(days / 7)} weeks ago`;
    if (days < 365) return `${Math.floor(days / 30)} months ago`;
    return `${Math.floor(days / 365)} years ago`;
  } catch (error) {
    console.error('Relative time error:', error);
    return '';
  }
};

/**
 * Add days to date
 */
export const addDaysToDate = (date, days) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return addDays(dateObj, days);
  } catch (error) {
    console.error('Add days error:', error);
    return date;
  }
};

/**
 * Subtract days from date
 */
export const subtractDaysFromDate = (date, days) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return subDays(dateObj, days);
  } catch (error) {
    console.error('Subtract days error:', error);
    return date;
  }
};

/**
 * Check if date is valid
 */
export const isValidDate = (date) => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return isValid(dateObj);
  } catch (error) {
    return false;
  }
};

/**
 * Get days between two dates
 */
export const getDaysBetween = (startDate, endDate) => {
  try {
    const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
    return differenceInDays(end, start);
  } catch (error) {
    console.error('Days between error:', error);
    return 0;
  }
};

export default {
  formatDate,
  formatDisplayDate,
  formatDateTime,
  getRelativeTime,
  addDaysToDate,
  subtractDaysFromDate,
  isValidDate,
  getDaysBetween
};