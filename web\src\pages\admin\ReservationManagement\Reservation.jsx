import React, { useEffect, useState } from 'react';
import ReservationManagement from 'pages/admin/ReservationManagement/ReservationManagement'
import { localStorageData } from 'services/auth/localStorageData'
import userServices from 'services/httpService/userAuth/userServices';
import AdminReservationDetail from 'pages/admin/ReservationDetails/AdminReservationDetail'

const Reservation = () => {

  const [reservation, setReservation] = useState([]);
  const [ReservationDetail, setReservationDetail] = useState([]);
  const [steps, setSteps] = useState('ReservationManagement');

  const getReservation = async () => {
    let res = await userServices.commonGetService(
      `/reservation/reservations/${localStorageData("_id")}?role=admin`
    );
    setReservation(res.data);
  };

  useEffect(() => {
    getReservation();
  }, []);

  const handleSetStepsAndDetail = (_id) => {
    const detail = reservation.find(reservation => reservation._id === _id);
    setSteps('ReservationDetail');
    setReservationDetail(detail);
  };

  return (
    <>
      {steps === "ReservationManagement" &&
        <ReservationManagement setStepsAndDetail={handleSetStepsAndDetail} reservation={reservation} />
      }
      {steps === "ReservationDetail" &&
        <AdminReservationDetail ReservationDetail={ReservationDetail} setSteps={setSteps} onBack={() => setSteps("ReservationManagement")} />
      }
    </>
  )
}

export default Reservation