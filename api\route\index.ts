import { Router } from "express";
import EmailRouter from './email';
import UserAuthRouter from './userAuth';
import EmployeeRouter from './employe';
import ReservationRouter from './reservation';
import ChatRouter from './chat';
import AdminRouter from './admin';
import PostRouter from './post';
import StripeRouter from './stripe';
import PropertyRouter from './property';

const router = Router();

router.use("/email", EmailRouter);
router.use("/post", PostRouter);
router.use("/employes", EmployeeRouter);
router.use("/reservation", ReservationRouter);
router.use("/chat", ChatRouter);
router.use("/stripe", StripeRouter);
router.use('/property', PropertyRouter);
router.use("/admin", AdminRouter);
router.use("/userAuth", UserAuthRouter);

export default router;
