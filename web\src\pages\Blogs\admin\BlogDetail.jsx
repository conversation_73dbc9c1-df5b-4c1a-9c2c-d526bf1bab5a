import { CircularProgress, Container, Tooltip } from "@mui/material";
import { HiOutlineDotsVertical } from "react-icons/hi";
import {
  FiEdit,
  FiTrash2,
  FiMessageSquare,
  FiThumbsUp,
  FiThumbsDown,
  FiShare2,
  FiChevronDown
} from "react-icons/fi";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { io } from "socket.io-client";
import userServices from "services/httpService/userAuth/userServices";
import Footerbn from "pages/Footer/Footerbn";
import ErrorService from "services/formatError/ErrorService";
import { toast } from "react-toastify";
import { useMutation } from "react-query";
import { localStorageData } from "services/auth/localStorageData";
import BnbNav from "components/NavBar/BnbNav";
import Navbar from "components/Navbars/AuthNavbar";

function BlogDetail() {
  const param = useParams();
  const [data, setData] = useState();
  const [userComment, setUserComment] = useState([]);
  const [render, setRender] = useState(false);
  const [commentDropdownOpen, setCommentDropdownOpen] = useState(null);
  const [replyDropdownOpen, setReplyDropdownOpen] = useState(null);
  const [editingCommentId, setEditingCommentId] = useState(null);
  const [editCommentText, setEditCommentText] = useState("");
  const [isUpdateLoading, setIsUpdateLoading] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [fullName, setFullName] = useState("");
  const [replyingToComment, setReplyingToComment] = useState(null);
  const [replyText, setReplyText] = useState("");
  const [isReplyLoading, setIsReplyLoading] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [dislikeCount, setDislikeCount] = useState(0);
  const [userLiked, setUserLiked] = useState(false);
  const [userDisliked, setUserDisliked] = useState(false);
  const [socket, setSocket] = useState(null);
  const [copyTooltip, setCopyTooltip] = useState("Share");
  const [expandedReplies, setExpandedReplies] = useState({});

  useEffect(() => {
    const newSocket = io("https://api.bnbyond.com/");
    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, []);

  useEffect(() => {
    if (!socket) return;

    if (currentUserId) {
      socket.emit("addUser", currentUserId);
    }

    socket.on("updateLikes", (data) => {
      if (data.postId === param.id) {
        setLikeCount(data.likeCount);
      }
    });

    socket.on("updateDislikes", (data) => {
      if (data.postId === param.id) {
        setDislikeCount(data.dislikeCount);
      }
    });

    socket.on("error", (error) => {
      toast.error(error.message);
    });

    return () => {
      socket.off("updateLikes");
      socket.off("updateDislikes");
      socket.off("error");
    };
  }, [socket, param.id, currentUserId]);

  useEffect(() => {
    try {
      const storedData = localStorage.getItem("Bnbyond");
      const parsedData = storedData ? JSON.parse(storedData) : null;

      if (parsedData) {
        const userId = parsedData._id || "";
        const firstName = parsedData.fname || "";
        const lastName = parsedData.lname || "";

        setCurrentUserId(userId);
        setFullName(`${firstName} ${lastName}`.trim());
      }
    } catch (error) {
      console.error("Error parsing Bnbyond from localStorage:", error);
    }
  }, []);

  useEffect(() => {
    if (data) {
      setLikeCount(data.likes?.length || 0);
      setDislikeCount(data.dislikes?.length || 0);
      setUserLiked(data.likes?.includes(currentUserId) || false);
      setUserDisliked(data.dislikes?.includes(currentUserId) || false);
    }
  }, [data, currentUserId]);

  const handleLike = () => {
    if (!currentUserId) {
      toast.error("Please login to like this post");
      return;
    }

    if (socket) {
      if (userLiked) {
        socket.emit("likePost", param.id, currentUserId);
        setLikeCount((prev) => Math.max(0, prev - 1));
        setUserLiked(false);
      } else {
        socket.emit("likePost", param.id, currentUserId);
        setLikeCount((prev) => prev + 1);
        setUserLiked(true);

        if (userDisliked) {
          socket.emit("dislikePost", param.id, currentUserId);
          setDislikeCount((prev) => Math.max(0, prev - 1));
          setUserDisliked(false);
        }
      }
    }
  };

  const handleDislike = () => {
    if (!currentUserId) {
      toast.error("Please login to dislike this post");
      return;
    }

    if (socket) {
      if (userDisliked) {
        socket.emit("dislikePost", param.id, currentUserId);
        setDislikeCount((prev) => Math.max(0, prev - 1));
        setUserDisliked(false);
      } else {
        socket.emit("dislikePost", param.id, currentUserId);
        setDislikeCount((prev) => prev + 1);
        setUserDisliked(true);

        if (userLiked) {
          socket.emit("likePost", param.id, currentUserId);
          setLikeCount((prev) => Math.max(0, prev - 1));
          setUserLiked(false);
        }
      }
    }
  };

  const toggleRepliesExpansion = (commentId) => {
    setExpandedReplies(prev => ({
      ...prev,
      [commentId]: !prev[commentId]
    }));
  };

  const toggleCommentDropdown = (index) => {
    setCommentDropdownOpen(commentDropdownOpen === index ? null : index);
    setReplyDropdownOpen(null);
  };

  const toggleReplyDropdown = (commentIndex, replyIndex) => {
    const dropdownId = `${commentIndex}-${replyIndex}`;
    setReplyDropdownOpen(replyDropdownOpen === dropdownId ? null : dropdownId);
    setCommentDropdownOpen(null);
  };

  const fetchBlogPost = async () => {
    let res = await userServices.getBlogPost(`post/blog/${param.id}`);
    setData(res.data.data);
    console.log("Blog Post Data:", res.data.data);
    setUserComment(res.data.data.comments);
  };

  const formatDateTime = (timeStamp) => {
    const date = new Date(timeStamp);
    const dateOptions = { month: "long", day: "numeric", year: "numeric" };
    const timeOptions = { hour: "2-digit", minute: "2-digit" };

    return {
      date: date.toLocaleDateString("en-US", dateOptions),
      time: date.toLocaleTimeString("en-US", timeOptions),
    };
  };

  const handleReplyClick = (commentId) => {
    setReplyingToComment(commentId);
    setReplyText("");
  };

  const handleCancelReply = () => {
    setReplyingToComment(null);
    setReplyText("");
  };

  const submitReply = async (parentCommentId) => {
    if (replyText.trim() === "") {
      toast.error("Reply cannot be empty");
      return;
    }

    try {
      setIsReplyLoading(true);
      const userId = localStorageData("_id");

      if (!userId) {
        toast.error("Please login to reply to a comment");
        setIsReplyLoading(false);
        return;
      }

      const replyData = {
        message: replyText,
        parentCommentId: parentCommentId,
        blogId: param.id,
        userId: userId,
      };

      await userServices.commonPostService("post/blogComment", replyData);
      setReplyingToComment(null);
      setReplyText("");
      toast.success("Reply posted successfully");
      fetchBlogPost();
    } catch (error) {
      toast.error("Failed to post reply");
    } finally {
      setIsReplyLoading(false);
    }
  };

  const handleEditClick = (commentId, commentMessage) => {
    setEditingCommentId(commentId);
    setEditCommentText(commentMessage);
    setCommentDropdownOpen(null);
    setReplyDropdownOpen(null);
  };

  const handleCancelEdit = () => {
    setEditingCommentId(null);
    setEditCommentText("");
  };

  const handleSaveEdit = async (commentId) => {
    if (editCommentText.trim() === "") {
      toast.error("Comment cannot be empty");
      return;
    }

    try {
      setIsUpdateLoading(true);

      const response = await userServices.updateBlogPost(
        `post/blogCommentupdate/${param.id}/commentid/${commentId}`,
        { message: editCommentText }
      );

      await fetchBlogPost();

      setEditingCommentId(null);
      toast.success("Comment updated successfully");
    } catch (error) {
      toast.error("Failed to update comment");
      fetchBlogPost();
    } finally {
      setIsUpdateLoading(false);
    }
  };

  const handleDeleteComment = async (commentId) => {
    if (!window.confirm("Are you sure you want to delete this comment? This action cannot be undone.")) {
      return;
    }

    try {
      await userServices.deleteBlogPost(
        `post/blogCommentdelete/${param.id}/commentid/${commentId}`
      );
      await fetchBlogPost();
      toast.success("Comment deleted successfully");
    } catch (error) {
      toast.error("Failed to delete comment");
      fetchBlogPost();
    }
  };

  useEffect(() => {
    fetchBlogPost();
  }, [render]);

  const handleShareClick = () => {
    const currentUrl = window.location.href;

    navigator.clipboard
      .writeText(currentUrl)
      .then(() => {
        setCopyTooltip("Copied!");
        setTimeout(() => {
          setCopyTooltip("Share");
        }, 2000);
      })
      .catch((err) => {
        console.error("Failed to copy URL: ", err);
        toast.error("Failed to copy URL");
      });
  };

  const renderReplies = (comment, commentIndex) => {
    if (!comment.replies || comment.replies.length === 0) return null;

    const repliesToShow = expandedReplies[comment._id] ? comment.replies : [];

    return (
      <>
        {comment.replies.length > 1 && (
          <div className="ml-12 mt-2 flex items-center text-[#2459BF] hover:text-blue-800 cursor-pointer"
            onClick={() => toggleRepliesExpansion(comment._id)}>
            <FiChevronDown
              className={`mr-1 transition-transform ${expandedReplies[comment._id] ? 'transform rotate-180' : ''}`}
              size={18}
            />
            <span>{expandedReplies[comment._id] ? 'Hide' : 'Show'} {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}</span>
          </div>
        )}

        {(comment.replies.length === 1 || expandedReplies[comment._id]) && comment.replies.map((reply, replyIndex) => {
          const { date, time } = formatDateTime(reply.timeStamp);
          const dropdownId = `${commentIndex}-${replyIndex}`;

          return (
            <div
              key={`reply-${replyIndex}`}
              className="ml-12 mt-4 p-4 bg-gray-100 rounded-lg border border-gray-200 flex justify-between"
            >
              <div>
                <div className="flex items-center space-x-4">
                  <img
                    className="h-10 w-10 rounded-full object-cover"
                    src={reply.userId?.pic || "/default-avatar.png"}
                    alt={`${reply.name || "User"}'s profile`}
                  />
                  <div>
                    <p className="font-semibold">{reply.name || fullName || "User"}</p>
                    <p className="text-gray-500 text-xs">
                      {date} at {time}
                    </p>
                  </div>
                </div>
                <div className="mt-2 pl-14">
                  {editingCommentId === reply._id ? (
                    <div className="w-full">
                      <textarea
                        className="border border-gray-300 p-2.5 text-sm w-full block outline-none rounded"
                        rows="3"
                        value={editCommentText}
                        onChange={(e) => setEditCommentText(e.target.value)}
                      ></textarea>
                      <div className="flex gap-2 mt-2">
                        <button
                          className="bg-[#2459BF] rounded text-sm text-white px-4 py-1"
                          onClick={() => handleSaveEdit(reply._id)}
                          disabled={isUpdateLoading}
                        >
                          {isUpdateLoading ? "Saving..." : "Save"}
                        </button>
                        <button
                          className="bg-gray-300 rounded text-gray-800 text-sm px-4 py-1"
                          onClick={handleCancelEdit}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-800 text-md font-lato">{reply.message}</p>
                  )}
                </div>
              </div>

              <div className="relative">
                <button
                  onClick={() => toggleReplyDropdown(commentIndex, replyIndex)}
                >
                  <HiOutlineDotsVertical size={22} />
                </button>

                {replyDropdownOpen === dropdownId && (
                  <div className="bg-white border border-gray-300 rounded-lg shadow-md w-40 absolute mt-1 right-0 z-10">
                    <button
                      className="flex text-left text-sm w-full gap-2 hover:bg-gray-200 items-center px-4 py-2"
                      onClick={() => handleEditClick(reply._id, reply.message)}
                    >
                      <FiEdit size={16} /> Edit
                    </button>
                    <button
                      className="flex text-left text-sm w-full gap-2 hover:bg-gray-200 items-center px-4 py-2 text-red-600"
                      onClick={() => handleDeleteComment(reply._id)}
                    >
                      <FiTrash2 size={16} /> Delete
                    </button>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </>
    );
  };

  return (
    <>
      {data ? (
        <>
          <Navbar />
          <Container maxWidth="lg" className="py-12">
            <div className="w-full">
              <div className="w-full">
                <img
                  src={data?.pic}
                  alt="Blog Post"
                  className="h-[500px] w-full object-cover"
                />
                <span className="flex justify-start gap-3 items-center mt-3">
                  {data?.timeStamp && (
                    <>
                      <p>
                        {formatDateTime(data.timeStamp).date} at{" "}
                        {formatDateTime(data.timeStamp).time}
                      </p>
                      <hr className="border border-gray-500 h-5" />
                    </>
                  )}
                  <div className="flex gap-4 items-center">
                    <Tooltip title="Like" arrow>
                      <button
                      disabled
                        onClick={handleLike}
                        className={`flex items-center gap-1 ${userLiked ? "text-blue-600" : "text-gray-600"
                          } hover:text-blue-700 transition-colors`}
                      >
                        <FiThumbsUp
                          size={18}
                          className={userLiked ? "fill-current" : ""}
                        />
                        <span>{likeCount > 0 ? likeCount : ""}</span>
                      </button>
                    </Tooltip>

                    <Tooltip title="Dislike" arrow>
                      <button
                      disabled
                        onClick={handleDislike}
                        className={`flex items-center gap-1 ${userDisliked ? "text-red-600" : "text-gray-600"
                          } hover:text-red-700 transition-colors`}
                      >
                        <FiThumbsDown
                          size={18}
                          className={userDisliked ? "fill-current" : ""}
                        />
                        <span>{dislikeCount > 0 ? dislikeCount : ""}</span>
                      </button>
                    </Tooltip>

                    <Tooltip title={copyTooltip} arrow>
                      <button
                        onClick={handleShareClick}
                        className="text-gray-600 hover:text-green-600 transition-colors"
                      >
                        <FiShare2 size={18} />
                      </button>
                    </Tooltip>
                  </div>
                  <p> ({data?.comments.length}) Comments</p>
                </span>
              </div>

              <div className="pt-8">
                <h2 className="text-4xl capitalize font-semibold">
                  {data?.title}
                </h2>
              </div>
              <div className="flex flex-col w-full md:flex-row">
                <div className="flex flex-col md:w-full">
                  <div className="pt-2">
                    <p
                      className="text-[#646464] text-lg font-lato"
                      dangerouslySetInnerHTML={{
                        __html: data?.description,
                      }}
                    ></p>
                  </div>
                  <h1 className="text-2xl font-bold mt-5">Blog Comments</h1>
                  {userComment.length === 0 ? (
                    <div className="bg-gray-100 p-6 rounded-lg shadow-lg mt-8">
                      <p className="text-gray-700 text-lg">
                        No comments have been posted yet.
                      </p>
                    </div>
                  ) : (
                    userComment.map((comment, index) => {
                      console.log("Comment:", comment);
                      const { date, time } = formatDateTime(comment.timeStamp);
                      const userPic = comment?.userId?.pic || "https://via.placeholder.com/150";
                      return (
                        <div key={index} className="mt-8">
                          <div className="flex bg-gray-50 border border-gray-300 justify-between p-6 rounded-lg shadow-lg">
                            <div className="w-full">
                              <div className="flex items-center space-x-4">
                                <img
                                  className="h-12 rounded-full w-12 object-cover"
                                  src={userPic}
                                  alt={`${comment.name}'s profile`}
                                />
                                <div>
                                  <p className="font-semibold">
                                    {comment.name}
                                  </p>
                                  <p className="text-gray-500 text-xs">
                                    {date} at {time}
                                  </p>
                                </div>
                              </div>
                              <div className="w-full mt-4">
                                {editingCommentId === comment._id ? (
                                  <div className="w-full">
                                    <textarea
                                      className="border border-gray-300 p-2.5 text-sm w-full block outline-none"
                                      rows="3"
                                      value={editCommentText}
                                      onChange={(e) =>
                                        setEditCommentText(e.target.value)
                                      }
                                    ></textarea>
                                    <div className="flex gap-2 mt-2">
                                      <button
                                        className="bg-[#2459BF] rounded text-sm text-white px-4 py-1"
                                        onClick={() =>
                                          handleSaveEdit(comment._id)
                                        }
                                        disabled={isUpdateLoading}
                                      >
                                        {isUpdateLoading ? "Saving..." : "Save"}
                                      </button>
                                      <button
                                        className="bg-gray-300 rounded text-gray-800 text-sm px-4 py-1"
                                        onClick={handleCancelEdit}
                                      >
                                        Cancel
                                      </button>
                                    </div>
                                  </div>
                                ) : (
                                  <p className="text-gray-800 text-md font-lato">
                                    {comment.message}
                                  </p>
                                )}
                              </div>
                            </div>

                            <div className="relative">
                              <button
                                onClick={() => toggleCommentDropdown(index)}
                              >
                                <HiOutlineDotsVertical size={22} />
                              </button>

                              {commentDropdownOpen === index && (
                                <div className="bg-white border border-gray-300 rounded-lg shadow-md w-40 absolute mt-1 right-0 z-10">
                                  <button
                                    className="flex text-left text-sm w-full gap-2 hover:bg-gray-200 items-center px-4 py-2"
                                    onClick={() =>
                                      handleEditClick(
                                        comment._id,
                                        comment.message
                                      )
                                    }
                                  >
                                    <FiEdit size={16} /> Edit
                                  </button>
                                  <button
                                    className="flex text-left text-sm w-full gap-2 hover:bg-gray-200 items-center px-4 py-2 text-red-600"
                                    onClick={() =>
                                      handleDeleteComment(comment._id)
                                    }
                                  >
                                    <FiTrash2 size={16} /> Delete
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex justify-end items-center mt-2">
                            <button
                              className="text-[#2459BF] hover:underline hover:text-blue-800 flex items-center"
                              onClick={() => handleReplyClick(comment._id)}
                            >
                              <FiMessageSquare size={16} className="mr-1" />{" "}
                              Reply
                            </button>
                          </div>

                          {replyingToComment === comment._id && (
                            <div className="mt-3 ml-12">
                              <div className="mb-2">
                                <h1 className="text-[20px]">
                                  Reply to {comment.name}
                                </h1>
                              </div>
                              <textarea
                                rows="3"
                                className="border border-gray-300 p-2.5 text-sm w-full block outline-none rounded"
                                placeholder="Write your reply here..."
                                value={replyText}
                                onChange={(e) => setReplyText(e.target.value)}
                              ></textarea>
                              <div className="flex gap-2 mt-2">
                                <button
                                  className="bg-[#2459BF] rounded text-sm text-white px-4 py-1"
                                  onClick={() => submitReply(comment._id)}
                                  disabled={isReplyLoading}
                                >
                                  {isReplyLoading ? "Posting..." : "Post Reply"}
                                </button>
                                <button
                                  className="bg-gray-300 rounded text-gray-800 text-sm px-4 py-1"
                                  onClick={handleCancelReply}
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          )}

                          {renderReplies(comment, index)}
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </Container>
          <Footerbn />
        </>
      ) : (
        <div className="flex justify-center items-center h-screen">
          <CircularProgress />
        </div>
      )}
    </>
  );
}

export default BlogDetail;