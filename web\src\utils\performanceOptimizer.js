/**
 * Performance optimization utilities and initialization
 */

import { initOptimizedCSS } from './cssLoader';
import { registerSW, swConfig, preloadCriticalResources } from './serviceWorkerRegistration';
import { analyzeBundleSize } from './bundleOptimizer';
import optimizedApiService from '../services/optimizedApiService';

/**
 * Initialize all performance optimizations
 */
export const initializePerformanceOptimizations = () => {
  console.log('🚀 Initializing BnbYond Performance Optimizations...');

  // 1. Initialize CSS optimizations
  initOptimizedCSS();
  console.log('✅ CSS optimizations initialized');

  // 2. Register service worker in production
  if (process.env.NODE_ENV === 'production') {
    registerSW(swConfig);
    console.log('✅ Service Worker registered');

    // Preload critical resources
    preloadCriticalResources([
      '/assets/img/image1.png',
      '/assets/img/image2.png',
      '/assets/img/image3.png',
      '/api/property/getAllPropertyAvailable?page=1&limit=8&status=Active'
    ]);
    console.log('✅ Critical resources preloaded');

    // Preload critical API data
    optimizedApiService.preloadCriticalData();
    console.log('✅ Critical API data preloaded');
  }

  // 3. Initialize bundle analysis in development
  if (process.env.NODE_ENV === 'development') {
    analyzeBundleSize();
    console.log('✅ Bundle analysis initialized');
  }

  // 4. Setup performance monitoring
  setupPerformanceMonitoring();
  console.log('✅ Performance monitoring setup complete');

  // 5. Optimize third-party scripts
  optimizeThirdPartyScripts();
  console.log('✅ Third-party scripts optimized');

  console.log('🎉 All performance optimizations initialized successfully!');
};

/**
 * Setup performance monitoring and reporting
 */
const setupPerformanceMonitoring = () => {
  // Monitor long tasks
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) {
            console.warn('Long task detected:', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            });
          }
        });
      });
      observer.observe({ entryTypes: ['longtask'] });
    } catch (e) {
      console.warn('Long task monitoring not supported');
    }
  }

  // Monitor memory usage
  if (performance.memory) {
    const checkMemoryUsage = () => {
      const memory = performance.memory;
      const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
      
      if (usagePercent > 80) {
        console.warn('High memory usage detected:', {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB',
          percentage: Math.round(usagePercent) + '%'
        });
      }
    };

    // Check memory usage every 30 seconds
    setInterval(checkMemoryUsage, 30000);
  }

  // Monitor network conditions
  if ('connection' in navigator) {
    const connection = navigator.connection;
    
    const logNetworkInfo = () => {
      console.log('Network conditions:', {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      });
    };

    connection.addEventListener('change', logNetworkInfo);
    logNetworkInfo(); // Log initial state
  }
};

/**
 * Optimize third-party scripts loading
 */
const optimizeThirdPartyScripts = () => {
  // Defer non-critical scripts
  const deferScript = (src, callback) => {
    const script = document.createElement('script');
    script.src = src;
    script.async = true;
    script.defer = true;
    if (callback) script.onload = callback;
    document.head.appendChild(script);
  };

  // Load analytics after page interaction
  let analyticsLoaded = false;
  const loadAnalytics = () => {
    if (!analyticsLoaded && window.gtag) {
      analyticsLoaded = true;
      console.log('✅ Analytics loaded on user interaction');
    }
  };

  // Load on first user interaction
  ['click', 'scroll', 'keydown', 'touchstart'].forEach(event => {
    document.addEventListener(event, loadAnalytics, { once: true, passive: true });
  });

  // Fallback: load after 5 seconds
  setTimeout(loadAnalytics, 5000);
};

/**
 * Resource hints for better performance
 */
export const addResourceHints = () => {
  const hints = [
    // DNS prefetch for external domains
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//cdnjs.cloudflare.com' },
    { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
    
    // Preconnect to critical origins
    { rel: 'preconnect', href: process.env.REACT_APP_ENDPOINT },
    { rel: 'preconnect', href: process.env.REACT_APP_IMAGEENDPOINT },
    
    // Prefetch critical resources
    { rel: 'prefetch', href: '/assets/img/image1.png' },
    { rel: 'prefetch', href: '/assets/img/image2.png' },
    { rel: 'prefetch', href: '/assets/fonts/Poppins-Regular.woff2' }
  ];

  hints.forEach(hint => {
    const link = document.createElement('link');
    Object.assign(link, hint);
    document.head.appendChild(link);
  });
};

/**
 * Optimize images on the page
 */
export const optimizePageImages = () => {
  // Convert images to WebP if supported
  const supportsWebP = () => {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  };

  if (supportsWebP()) {
    const images = document.querySelectorAll('img[data-webp]');
    images.forEach(img => {
      img.src = img.dataset.webp;
    });
  }

  // Add loading="lazy" to images below the fold
  const images = document.querySelectorAll('img:not([loading])');
  images.forEach((img, index) => {
    if (index > 2) { // Skip first 3 images (likely above fold)
      img.loading = 'lazy';
    }
  });
};

/**
 * Performance budget checker
 */
export const checkPerformanceBudget = () => {
  const budgets = {
    // Core Web Vitals
    LCP: 2500, // ms
    FID: 100,  // ms
    CLS: 0.1,  // score
    
    // Custom metrics
    TTI: 3800, // ms
    FCP: 1800, // ms
    
    // Resource budgets
    totalJS: 1024 * 1024,     // 1MB
    totalCSS: 200 * 1024,     // 200KB
    totalImages: 2048 * 1024, // 2MB
    totalFonts: 100 * 1024    // 100KB
  };

  // Check resource sizes
  const resources = performance.getEntriesByType('resource');
  const sizes = {
    totalJS: 0,
    totalCSS: 0,
    totalImages: 0,
    totalFonts: 0
  };

  resources.forEach(resource => {
    const size = resource.transferSize || 0;
    const url = resource.name;

    if (url.includes('.js')) sizes.totalJS += size;
    else if (url.includes('.css')) sizes.totalCSS += size;
    else if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) sizes.totalImages += size;
    else if (url.match(/\.(woff|woff2|ttf|otf)$/i)) sizes.totalFonts += size;
  });

  // Check violations
  const violations = [];
  Object.entries(budgets).forEach(([metric, budget]) => {
    if (sizes[metric] && sizes[metric] > budget) {
      violations.push({
        metric,
        actual: sizes[metric],
        budget,
        exceeded: sizes[metric] - budget
      });
    }
  });

  if (violations.length > 0) {
    console.warn('Performance budget violations:', violations);
  } else {
    console.log('✅ All performance budgets met');
  }

  return violations;
};

/**
 * Emergency performance mode for slow connections
 */
export const enableEmergencyMode = () => {
  console.log('🚨 Enabling emergency performance mode');

  // Disable animations
  const style = document.createElement('style');
  style.textContent = `
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  `;
  document.head.appendChild(style);

  // Reduce image quality
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    if (img.src && !img.dataset.originalSrc) {
      img.dataset.originalSrc = img.src;
      // Replace with lower quality version if available
      img.src = img.src.replace(/\.(jpg|jpeg|png)$/i, '_low.$1');
    }
  });

  // Clear non-essential caches
  if ('caches' in window) {
    caches.keys().then(names => {
      names.forEach(name => {
        if (name.includes('dynamic') || name.includes('images')) {
          caches.delete(name);
        }
      });
    });
  }
};

/**
 * Auto-detect and enable emergency mode for slow connections
 */
export const autoOptimizeForConnection = () => {
  if ('connection' in navigator) {
    const connection = navigator.connection;
    
    // Enable emergency mode for slow connections
    if (connection.effectiveType === 'slow-2g' || 
        connection.effectiveType === '2g' ||
        connection.saveData) {
      enableEmergencyMode();
    }
    
    // Monitor connection changes
    connection.addEventListener('change', () => {
      if (connection.effectiveType === 'slow-2g' || 
          connection.effectiveType === '2g') {
        enableEmergencyMode();
      }
    });
  }
};

export default {
  initializePerformanceOptimizations,
  addResourceHints,
  optimizePageImages,
  checkPerformanceBudget,
  enableEmergencyMode,
  autoOptimizeForConnection
};
