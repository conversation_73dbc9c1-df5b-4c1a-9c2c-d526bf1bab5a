import { Router } from "express";
import { awsupload } from "../middleware/upload";
import checkRoleMiddleware from "../middleware/routeMiddleware";
import { Controller as UserAuthController } from "../controllers/userAuth";

const router = Router();

// Common middleware for protected routes
const userAccessControl = checkRoleMiddleware(['subadmin', 'Blogger', 'Financial']);

// Registration routes
router.post("/registerByGoogle", UserAuthController.registerByGoogle);
router.post("/registerByEmail", UserAuthController.registerByEmail);
router.post("/checkemail", UserAuthController.CheckEmailOrUsername);

// Authentication routes
router.post("/loginByEmail", UserAuthController.login);
router.post("/updatepassword", UserAuthController.updateUserPassword);
router.post("/updatepasswordFromProfile", userAccessControl, UserAuthController.updatePassFromProfile);

// User profile management routes
router.post("/updateuserinfo", userAccessControl, awsupload.array('pics'), UserAuthController.updateUserInfo);
router.put("/userUpdateById/:id", userAccessControl, UserAuthController.userUpdateById);
router.put("/addRecoveryEmailById/:id", userAccessControl, UserAuthController.addRecoveryEmail);
router.post("/gmailVerification/:_id", userAccessControl, UserAuthController.userConnected);
router.put("/otpsend", userAccessControl, UserAuthController.phoneVerificationsendotp);

// User data retrieval routes
router.get("/allusers", userAccessControl, UserAuthController.AllUsers);
router.get("/user/:id",userAccessControl, UserAuthController.SingleUser);
router.get("/userdatabyId/:_id", userAccessControl, UserAuthController.userDataById);
router.get("/users/:userId/devices", userAccessControl, UserAuthController.getuserDevicesLogin);

// Role management
router.put("/updateRole", userAccessControl, UserAuthController.updateUserRole);

// User deletion
router.delete("/deleteUser/:userId", userAccessControl, UserAuthController.deleteUser);

// Subscription management
router.post("/subscriber", UserAuthController.subscriberEmail);
// refferral code management
router.post("/generateReferralCode", userAccessControl, UserAuthController.referralCode);
router.post("/getrefferduser",userAccessControl, UserAuthController.getRefferalUser);

export default router;