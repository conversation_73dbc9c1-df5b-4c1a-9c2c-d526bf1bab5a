import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import profileIcon from 'assets/img/profile.png'
import cameraIcon from 'assets/img/camera.svg'
import cooking from 'assets/img/cooking.png'
import animal from 'assets/img/animal.png'
import Exercise from 'assets/img/Exercise.png'
import plusIcon from 'assets/img/plusIcon.svg'
import { ButtonFilled } from "common/buttons/buttonFilled";
import { CiCircleRemove } from "react-icons/ci";


// InterestsContainer component
const InterestsContainer = ({ icon, name, onRemove }) => {
  return (
    <div className="flex cursor-pointer w-[150px] items-center capitalize justify-center p-4 h-[50px] bg-[#F9F9F9] border-2 text-[#646464]  border-[#C1E1C2] gap-4  font-medium rounded-2xl">
      <img src={icon} alt="" className='w-6 h-6 text-black ' />
      <p className="text-[16px] ">{name}</p>
      <span className="cursor-pointer " onClick={onRemove}>
        <CiCircleRemove className="text-red-600" />
      </span>
    </div>
  );
}

export const UpdateProfile = ({ setSteps }) => {
  const navigate = useNavigate()
  const [imageSrc, setImageSrc] = useState(profileIcon);
  const [textValue, setTextValue] = useState('');
  const [userInterests, setUserInterests] = useState([]);
  const [showAddInterestModal, setShowAddInterestModal] = useState(false);

  // Array of predefined interests with names and icons
  const predefinedInterests = [
    { name: "Cooking", icon: cooking },
    { name: "Animal", icon: animal },
    { name: "Gym", icon: Exercise },

    // Add more predefined interests as needed
  ];

  const addInterest = (name, icon) => {
    // Check if the interest is not already added
    if (!userInterests.find((interest) => interest.name === name)) {
      setUserInterests((prevInterests) => [
        ...prevInterests,
        { name, icon }
      ]);
    }
    // Close the modal after adding an interest
    setShowAddInterestModal(false);
  };

  // Function to remove an interest from the user's interests
  const removeInterest = (name) => {
    setUserInterests((prevInterests) =>
      prevInterests.filter((interest) => interest.name !== name)
    );
  };
  const letterCount = textValue.length; // Variable to track the current number of characters in the textarea
  const handleTextAreaChange = (event) => {
    const newTextValue = event.target.value;

    // Check if the length is less than or equal to 4000
    if (newTextValue.length <= 4000) {
      setTextValue(newTextValue);
    } else {
      console.error('Text limit exceeded (4000 characters)');
    }
  };

  // Handles the profile image change by reading the selected file and updating the image source.
  const handleImageChange = (event) => {
    const file = event.target.files[0];

    if (file) {
      const reader = new FileReader();

      reader.onloadend = () => {
        setImageSrc(reader.result);
      };

      reader.readAsDataURL(file);
    }
  };



  return (
    <div className="p-5 md:p-20 ">
      <div className="flex flex-col md:flex-row w-full h-auto my-14 md:my-20 border rounded-[15px] border-color-green overflow-hidden">
        <div className="flex flex-col items-center justify-start px-5 py-10 space-y-6 bg-white md:px-none md:w-4/12">
          <div className="relative">
            <img
              className="h-[187px] w-[187px] rounded-full object-cover"
              src={imageSrc}
              alt="Profile"
            />
            <div className="absolute w-full -bottom-3">
              <label htmlFor="fileInput" className=" mx-auto h-[31px] cursor-pointer w-[130px] text-white bg-[#E8AD21] flex gap-4 items-center justify-center rounded-full">
                <div className="text-white">
                  <img src={cameraIcon} alt="" />
                </div>
                <input id="fileInput"
                  className="hidden"
                  type="file"
                  onChange={handleImageChange}
                  accept="image/*" />
                <span>Add Image</span>
              </label>
            </div>
          </div>
          <div className="flex flex-col items-center justify-center">
            <p className="text-2xl font-medium ">Add a profile picture</p>
            <p className="text-base font-normal text-[#4B4B4B] text-center">
              We do not accept company logos or names, and they will be removed if
              applied.
            </p>
          </div>
        </div>
        <div className="w-full h-auto px-5 py-10 space-y-6 bg-white border-l border-color-green md:px-14">
          <p className="text-2xl font-semibold">About yourself</p>
          <textarea
            className="w-full p-4 text-xl bg-gray-100 border-2 rounded-md outline-none border-color-green hover:border-blue-500"
            id="myTextArea"
            name="myTextArea"
            placeholder="Type here"
            rows="4"
            onChange={handleTextAreaChange}
            value={textValue}
            readOnly={textValue.length >= 4001}
          />
          <span>{letterCount}/4000</span>
          <div className="">
            <p className="text-xl font-semibold">What you're into</p>
            <p className="py-5 text-lg">
              Find common ground with other guests and Hosts by adding interests to
              your profile.
            </p>


            <div className="flex flex-wrap gap-4">
              {userInterests.map((interest) => (
                <InterestsContainer
                  key={interest.name}
                  name={interest.name}
                  icon={interest.icon}
                  onRemove={() => removeInterest(interest.name)}
                />
              ))}
              <div
                className="flex w-[150px] cursor-pointer items-center justify-center h-[50px] bg-[#F9F9F9] border-2 text-[#646464] border-color-green font-medium rounded-2xl"
                onClick={() => setShowAddInterestModal(true)}
              >
                <img src={plusIcon} alt="" />
              </div>
            </div>

            {showAddInterestModal && (
              <div className="fixed top-0 left-0 flex items-center justify-center w-full h-full bg-black bg-opacity-50">
                <div className="p-8 bg-white rounded-md">
                  <p className="mb-4 text-lg font-semibold">Select an interest</p>
                  <div className="flex flex-wrap gap-4">
                    {predefinedInterests.map((interest) => (
                      <div
                        key={interest.name}
                        className="flex items-center justify-center p-4 bg-gray-200 rounded-md cursor-pointer"
                        onClick={() => addInterest(interest.name, interest.icon)}
                      >
                        <img src={interest.icon} alt={interest.name} className="mr-2" />
                        <p>{interest.name}</p>
                      </div>
                    ))}
                  </div>
                  <button
                    className="p-2 mt-4 text-white bg-blue-500 rounded-md"
                    onClick={() => setShowAddInterestModal(false)}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

          </div>
          <p className="text-lg font-medium">
            <span className="border-b border-black">
              Add interests and sports
            </span>
          </p>
          <p className="text-xl font-semibold ">Your past trips</p>
          <p className="text-lg">Show the destinations I've traveled to.</p>
          <div className="flex flex-wrap gap-4 ">
            <div className="border-2 w-[170px] h-[192px] text-center text-[#8d8c8c] flex items-center justify-center border-color-green rounded-xl hover:border-blue-500 bg-gray-100">
              <p>Your past trip 1</p>
            </div>
            <div className="border-2 w-[170px] h-[192px] text-center text-[#8d8c8c] flex items-center justify-center border-color-green rounded-xl hover:border-blue-500 bg-gray-100">
              <p>Your past trip 2</p>
            </div>
            <div className="border-2 w-[170px] h-[192px] text-center text-[#8d8c8c] flex items-center justify-center border-color-green rounded-xl hover:border-blue-500 bg-gray-100">
              <p>Your past trip 3</p>
            </div>
            <div className="border-2 w-[170px] h-[192px] text-center text-[#8d8c8c] flex items-center justify-center border-color-green rounded-xl hover:border-blue-500 bg-gray-100">
              <p>Your past trip 4</p>
            </div>
          </div>
          <div className="w-[20%] mr-auto" onClick={() => setSteps("profileDetails")}>
            <ButtonFilled text="save" />
          </div>
        </div>
      </div>
    </div>
  );
};



