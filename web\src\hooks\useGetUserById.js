import {  useQuery } from 'react-query';
import { fetchWrapper } from 'services/restApi';
import { toast } from 'react-toastify';
import ErrorService from 'services/formatError/ErrorService';
import { storeLocalData } from 'services/auth/localStorageData';
export const useGetUserById = (id) => {
  return useQuery(
    () => {
      return fetchWrapper('GET', `userAuth/getuserbyid/${id}`);
    },
    {
      onSuccess: (data) => {

      toast.success('User successfully  Login');
      
      },
      onError: (err) => {
        toast.error(ErrorService.uniformError(err));
      },
    }
  );
};
