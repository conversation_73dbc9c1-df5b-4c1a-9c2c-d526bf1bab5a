import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Rating from "react-rating";
import { FaStar, FaStarHalfAlt, FaRegStar } from "react-icons/fa";
import Divider from "@mui/material/Divider";
import { MdEditNote, MdCalendarToday, MdContentCopy, MdCheck } from "react-icons/md";
import Modal from 'components/Modal/Modal';
import { useMutation } from 'react-query';
import userServices from "services/httpService/userAuth/userServices";
import { toast } from 'react-toastify';
import dayjs from "dayjs";

const ActiveProperty = ({ activeProperty, loading }) => {
  const navigate = useNavigate();
    const today = dayjs();
    const checkIn = today.add(7, "day").format("YYYY-MM-DD");;
    const checkOut = today.add(11, "day").format("YYYY-MM-DD");;
    const customQuery = `?spaceTypeDetail=1&bedroomData=1&check_in=${checkIn}&check_out=${checkOut}`;
  const [isModalOpen, setModalOpen] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [importUrl, setImportUrl] = useState('');
  const [exportUrl, setExportUrl] = useState('');
  const [copyIcon, setCopyIcon] = useState("copy");

  // React Query mutation
  const syncCalendarMutation = useMutation(
    async (data) => {

      try {
        const result = await userServices.syncCalendar(data);
        return result;
      } catch (error) {
        console.error("Sync failed:", error.message || "Unknown error");
        throw new Error(error.message || "Failed to sync calendar");
      }
    },
    {
      onSuccess: () => {
        toast.success("Calendar sync successful!");
        handleCloseModal();
      },
      onError: (error) => {
        console.error("Mutation failed:", error.message);
        toast.error(`Error: ${error.message}`);
      },
    }
  );

  // Handler functions
  const handleImport = () => {
    if (!importUrl || !selectedProperty) return;

    syncCalendarMutation.mutate({
      propertyId: selectedProperty._id,
      url: importUrl
    });
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(exportUrl);
    setCopyIcon("check");
    setTimeout(() => {
      setCopyIcon("copy");
    }, 1000);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setImportUrl('');
    setExportUrl('');
    setCopyIcon("copy");
    setSelectedProperty(null);
  };

  const calculateAverageRating = (reviews) => {
    if (reviews && reviews.length > 0) {
      const total = reviews.reduce((acc, review) => acc + review.rating, 0);
      return Number((total / reviews.length).toFixed(1));
    }
    return 0;
  };

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center gap-3 w-full h-64">
        <p className="font-bold text-center">Loading Properties...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-wrap gap-6 pt-6">
      {activeProperty.length > 0 ? (
        activeProperty.map((property) => {
          const averageRating = calculateAverageRating(property.reviews || []);

          const minPoint =
            Array.isArray(property.points) && property.points.length > 0
              ? property.points.reduce(
                (min, p) => (p.point < min ? p.point : min),
                property.points[0].point
              )
              : null;

          return (
            <div key={property._id} className="md:w-[265px] w-full">
              <div
                className="relative cursor-pointer md:w-[265px] rounded-t-xl w-full h-[200px] bg-center bg-cover group"
                style={{
                  backgroundImage: `url(${property.pics[0]})`,
                }}
              >
                {/* Edit button */}
                <button
                  className="absolute top-2 right-2 hidden group-hover:flex items-center justify-center bg-blue-500 text-white rounded-full w-10 h-10 shadow-lg"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/propertylisting`, { state: property });
                  }}
                >
                  <MdEditNote className="w-8 h-8" title="Edit Property" />
                </button>

                {/* Calendar button */}
                <button
                  className="absolute top-2 right-14 hidden group-hover:flex items-center justify-center bg-blue-500 text-white rounded-full w-10 h-10 shadow-lg"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedProperty(property);
                    setExportUrl(`https://api.bnbyond.com/property/calendar/v1/${property._id}.ics`);
                    setModalOpen(true);
                  }}
                >
                  <MdCalendarToday className="w-6 h-6" title="Set to Calendar" />
                </button>
              </div>
              <div className="md:w-[265px] w-full py-3 h-auto rounded-b-xl bg-white border flex flex-col justify-between">
                <div className="px-4">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-2">
                      {averageRating > 0 ? (
                        <div className="flex items-center">
                          <Rating
                            initialRating={averageRating}
                            readonly
                            emptySymbol={<FaRegStar color="#d3d3d3" />}
                            halfSymbol={<FaStarHalfAlt color="#ffd700" />}
                            fullSymbol={<FaStar color="#ffd700" />}
                            fractions={2}
                          />
                          <span>({averageRating})</span>
                        </div>
                      ) : (
                        <span className="text-[#AFB5C1]">Not Rated Yet.</span>
                      )}
                    </div>
                    <div></div>
                  </div>
                  <div style={{ height: "3.3rem" }} className="mb-2">
                    <h4
                      className="text-[#000000] text-lg font-bold cursor-pointer hover:text-blue-500 overflow-hidden"
                      style={{
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                      }}
                        onClick={() => navigate(`/room/${property._id}${customQuery}`)} 

                    >
                    {property.title}
                  </h4>
                </div>
                <div>
                  <h4 className="text-[#000000] text-base font-medium">
                    {property.address.length > 25
                      ? property.address.substring(0, 25) + "..."
                      : property.address}
                  </h4>
                </div>
                <div className="text-[#AFB5C1]">
                  <span className="text-base font-normal">
                    {property.spaceTypeDetail &&
                      property.spaceTypeDetail.bathrooms &&
                      property.spaceTypeDetail.bedrooms &&
                      property.spaceTypeDetail.guests && (
                        <>
                          Guests: {property.spaceTypeDetail.guests || 0} |
                          Bedrooms: {property.spaceTypeDetail.bedrooms || 0} |
                          Bathrooms: {property.spaceTypeDetail.bathrooms || 0}
                        </>
                      )}
                  </span>
                </div>
              </div>
              <div className="pt-2">
                <Divider />
              </div>
              <div className="px-4 pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-[#AFB5C1] text-base font-normal">
                      From
                    </p>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-[#AFB5C1] text-sm line-through ">
                      {minPoint}
                    </span>
                    <span className="text-[#C0392D] text-base">
                      {minPoint} Points
                    </span>
                  </div>
                </div>
              </div>
            </div>
            </div>
  );
})
      ) : (
  <div className="flex flex-col justify-center items-center gap-3 w-full h-64">
    <p className="font-bold text-center">No Active Properties</p>
    <p className="text-[#AFB5C1]">
      {" "}
      There are currently no active properties.
    </p>
  </div>
)}

{/* Modal for Import/Export */ }
<Modal isOpen={isModalOpen} onClose={handleCloseModal}>
  <div className="p-4 lg:px-6 lg:py-8 w-full">
    <h2 className="text-xl font-bold mb-6 text-center text-grey-500">
      Sync Property with other platforms {selectedProperty ? `- ${selectedProperty.title}` : ""}
    </h2>

    {/* Import section */}
    <div className="mb-6 w-full">
      <div className="relative w-full mb-3">
        <label className="block mb-2 text-base font-medium text-[#000000]">
          Paste other platforms Link
        </label>
        <input
          className="w-full px-3 py-3 font-normal placeholder:text-sm text-[#929293] transition-all duration-150 ease-linear bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring"
          type="text"
          placeholder="https://airbnb.com/..."
          value={importUrl}
          onChange={(e) => setImportUrl(e.target.value)}
        />
      </div>
    </div>

    {/* Export section */}
    <div className="mb-4 w-full">
      <div className="relative w-full mb-3">
        <label className="block mb-2 text-base font-medium text-[#000000]">
          Generated Export URL
        </label>
        <div className="relative">
          <input
            className="w-full px-3 py-3 font-normal placeholder:text-sm text-[#929293] transition-all duration-150 ease-linear bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring pr-10"
            type="text"
            value={exportUrl}
            readOnly
          />
          {copyIcon === "copy" ? (
            <MdContentCopy
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 cursor-pointer hover:text-blue-500"
              size={20}
              onClick={copyToClipboard}
            />
          ) : (
            <MdCheck
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500"
              size={20}
            />
          )}
        </div>
      </div>
    </div>

    <div className="mt-6 text-center">
      <button
        className={`w-full px-6 py-3 mb-1 mr-1 font-bold text-white rounded-full shadow text-md hover:shadow-lg ${syncCalendarMutation.isLoading || !importUrl
          ? "bg-gray-400 cursor-not-allowed"
          : "bg-blue-500 hover:bg-blue-600 cursor-pointer"
          }`}
        onClick={handleImport}
        disabled={syncCalendarMutation.isLoading || !importUrl}
      >
        {syncCalendarMutation.isLoading ? "Submitting..." : "Submit"}
      </button>
    </div>
  </div>
</Modal>
    </div >
  );
};

export default ActiveProperty;