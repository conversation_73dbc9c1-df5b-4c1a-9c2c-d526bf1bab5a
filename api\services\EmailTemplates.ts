import { fn } from "moment";

export const forgetEmailBody = (data) => {
  return (
    `<body style="font-family: Arial, sans-serif; background-color: #f9f9f9; margin: 0; padding: 0;">
      <div style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border: 1px solid #ddd; border-radius: 8px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <div style="text-align: left; margin-bottom: 20px;">
              <img src="https://bnbpictures.s3.us-east-1.amazonaws.com/favicon.ico" alt="BnByond logo" style="height: 40px;">
          </div>

          <div style="font-size: 16px; color: #333333; line-height: 1.6;">
              <p>${data.userName},</p>

              <p>We’ve received a request to reset your password.</p>

              <p>If you didn’t make the request, just ignore this message. Otherwise, you can reset your password below:</p>

              <div style="text-align: center; margin: 20px 0;">
                  <a href=${data.uniqueLink} style="background-color: #ff5a5f; color: #ffffff; text-decoration: none; padding: 10px 20px; border-radius: 4px; font-size: 16px;">Reset your password</a>
              <p>If the button not working plz click on the link below</p>
                  <p>${data.uniqueLink}</p>
              </div>
          </div>

          <div style="font-size: 12px; color: #888888; text-align: center; margin-top: 20px;">
              <p>Thanks,<br>The Bnbyond team</p>
              <p>168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2</p>
          </div>
      </div>
  </body>
  `
  );
}

export const verifyEmailBody = (data) => {
  return `
  <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; line-height: 1.4; background-color: #f6f9fc;">
    <div style="max-width: 600px; margin: 40px auto; padding: 40px 20px;">
        <!-- Main Container -->
        <div style="background-color: #ffffff; border-radius: 8px; padding: 40px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <!-- Logo -->
            <div style="margin-bottom: 32px;">
                <img src="https://bnbpictures.s3.us-east-1.amazonaws.com/favicon.ico" alt="BnByond" style="height: 40px;">
            </div>
            
            <!-- Content -->
            <div style="margin-bottom: 32px;">
                <p style="color: #1a1f36; font-size: 16px; margin: 0 0 24px 0; line-height: 24px;">Thanks for creating a BnByond account. Verify your email so you can get up and running quickly.</p>
                
                <!-- Button -->
                <a href="${data.uniquelink}" style="display: inline-block; padding: 12px 24px; background-color: #635bff; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 15px;">Verify email</a>
            </div>
            
            <p style="color: #1a1f36; font-size: 16px; margin: 0 0 8px 0; line-height: 24px;">Once your email is verified, you can start setting up your account.</p>
            <p style="color: #666666; font-size: 14px; margin-bottom: 20px;">This link will expire in 15 minutes. If you did not create an account, please ignore this email.</p>
            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #f0f0f0;">
                <p style="color: #333333; margin-bottom: 5px;">Best regards,</p>
                <p style="color: #333333; font-weight: bold; margin-top: 0;">The BnByond Team</p>
            </div>
        </div>
        <div style="text-align: center; padding: 20px; background-color: #f8f8f8; border-radius: 0 0 10px 10px;">
             <div style="margin-top: 30px; text-align: center; color: #666666; font-size: 14px;">
            <p style="margin-bottom: 5px;">If you need any assistance, please contact <NAME_EMAIL> or call ****************</p>
            <p style="margin: 0;">168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2</p>
           <p style="color: #999999; font-size: 12px; margin-top: 10;">© 2025 BnByond. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>

`;
}

export const welcomeEmailBody = (data) => {
  return (
    `

    <p style='margin-top:0in;margin-right:0in;margin-bottom:8.0pt;margin-left:0in;line-height:107%;font-size:15px;font-family:"Calibri",sans-serif;text-align:center;'>Hi,` +
    data.fname +
    `<br>&nbsp;welcome, to BnByond! Your email has been verified
    
    
    `
  );
}

export const ThanksEmailBody = (data) => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email - BnByond</title>
    <style>
        :root {
            --primary: #04a5c2;
            --primary-light: #8dd5e2;
            --primary-dark: #038fa7;
            --primary-ultra-light: #e5f6f9;
            --white: #ffffff;
            --gray-light: #f5f9fa;
            --gray: #e0e0e0;
            --text: #333333;
            --text-secondary: #666666;
        }
        
        body {
            font-family: 'Montserrat', 'Arial', sans-serif;
            line-height: 1.6;
            color: var(--text);
            background-color: var(--gray-light);
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: var(--white);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 12px 24px rgba(4, 165, 194, 0.15);
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            padding: 35px 20px;
            text-align: center;
            position: relative;
        }
        
        .header:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: var(--primary-light);
        }
        
        .header img {
            max-width: 200px;
            filter: drop-shadow(0 4px 6px rgba(0,0,0,0.1));
        }
        
        .content {
            padding: 40px 30px;
            text-align: center;
            background-color: var(--white);
            position: relative;
        }
        
        .content:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-light) 0%, var(--primary) 50%, var(--primary-light) 100%);
            opacity: 0.5;
        }
        
        h1 {
            color: var(--primary);
            font-size: 28px;
            margin-top: 0;
            margin-bottom: 20px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }
        
        p {
            margin-bottom: 25px;
            font-size: 16px;
            color: var(--text);
        }
        
        .highlight {
            font-weight: bold;
            color: var(--primary);
        }
        
        .button {
            display: inline-block;
            background: var(--primary);
            color: var(--white) !important;
            text-decoration: none;
            padding: 16px 40px;
            border-radius: 50px;
            font-weight: bold;
            font-size: 18px;
            margin: 30px 0;
            transition: all 0.3s;
            box-shadow: 0 6px 12px rgba(4, 165, 194, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .button:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(4, 165, 194, 0.4);
        }
        
        .expiry {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 20px;
            padding: 10px 20px;
            background-color: var(--primary-ultra-light);
            border-radius: 30px;
            display: inline-block;
        }
        
        .expiry-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background-color: var(--primary);
            border-radius: 50%;
            margin-right: 5px;
            vertical-align: middle;
        }
        
        .accent-bar {
            height: 6px;
            background: linear-gradient(90deg, var(--primary-dark) 0%, var(--primary) 50%, var(--primary-light) 100%);
        }
        
        .decorative-wave {
            display: block;
            width: 100%;
            height: 50px;
            background: var(--primary-ultra-light);
            position: relative;
            overflow: hidden;
        }
        
        .decorative-wave:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--white);
            border-radius: 0 0 50% 50% / 0 0 100% 100%;
        }
        
        .footer {
            background-color: var(--primary-ultra-light);
            padding: 30px;
            text-align: center;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .social {
            margin: 25px 0;
        }
        
        .social a {
            display: inline-block;
            margin: 0 10px;
            width: 36px;
            height: 36px;
            background-color: var(--primary);
            border-radius: 50%;
            text-align: center;
            line-height: 36px;
            transition: transform 0.3s;
        }
        
        .social a:hover {
            transform: scale(1.1);
            background-color: var(--primary-dark);
        }
        
        .social img {
            width: 18px;
            height: 18px;
            vertical-align: middle;
            filter: brightness(0) invert(1);
        }
        
        .divider {
            height: 1px;
            background-color: var(--primary-light);
            opacity: 0.3;
            margin: 20px 0;
        }
        
        .circle-decoration {
            position: absolute;
            width: 200px;
            height: 200px;
            background: var(--primary);
            opacity: 0.05;
            border-radius: 50%;
        }
        
        .circle-1 {
            top: -100px;
            right: -50px;
        }
        
        .circle-2 {
            bottom: -70px;
            left: -100px;
            width: 150px;
            height: 150px;
        }
        
        @media only screen and (max-width: 600px) {
            .content {
                padding: 30px 20px;
            }
            
            .button {
                padding: 14px 30px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://bnbpictures.s3.us-east-1.amazonaws.com/favicon.ico" alt="BnByond Logo">
        </div>
        
        <div class="content">
            <div class="circle-decoration circle-1"></div>
            <div class="circle-decoration circle-2"></div>
            
            <h1>Welcome to BnByond!</h1>
            <p>Hi there! You're <span class="highlight">almost set</span> to join the BnByond community so you can get the most out of your vacation rental property!</p>
            <p>Simply click the button below to verify your email address.</p>
            
            <a href="${process.env.frontendLink}/auth/signin" class="button">Verify Now</a>
            
            <p class="expiry"><span class="expiry-icon"></span> This verification link expires in <strong>15 minutes</strong></p>
        </div>
        
        <div class="accent-bar"></div>
        <div class="decorative-wave"></div>
        
        <div class="footer">
            <p><strong style="color: var(--primary);">BnByond</strong> - Maximize Your Rental Property Potential</p>
            <div class="divider"></div>            
            <p>If you didn't create an account with BnByond, please ignore this email.</p>
            <p>© 2025 BnByond. All rights reserved.</p>
        </div>
    </div>
</body>
</html>

`;
}
// export const ThanksEmailBody = (data) => {
//   return `
// <!DOCTYPE html>
// <html lang="en">
//   <head>
//     <link
//       rel="stylesheet"
//       href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
//       integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T"
//       crossorigin="anonymous"
//     />

//     <script
//       src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
//       integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
//       crossorigin="anonymous"
//     ></script>
//     <script
//       src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"
//       integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1"
//       crossorigin="anonymous"
//     ></script>
//     <script
//       src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"
//       integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM"
//       crossorigin="anonymous"
//     ></script>

//     <meta charset="UTF-8" />
//     <meta http-equiv="X-UA-Compatible" content="IE=edge" />
//     <meta name="viewport" content="width=device-width, initial-scale=1.0" />
//     <title>Document</title>
//   </head>

//   <body class=" ">
//     <div class="container-fluid">
//       <div class="row">
//         <div
//           class="col-12 p-0 m-0 h- text-center border d-flex justify-content-center flex-column"
//           style="
//             height: 400px;
//             background-image: url(https://bnbpictures.s3.amazonaws.com/back.png);
//             background-repeat: no-repeat;
//             background-size: cover;
//             background-position-y: bottom;
//             object-fit: cover;
//           "
//         >
//           <img src="https://bnbpictures.s3.amazonaws.com/mail.png" class="d-block mx-auto" alt="" />
//           <p class="mb-0 my-2">Thanks for signing up!</p>
//           <h4>Verify Your E-mail Address</h4>
//         </div>
//         <div class="p-4 col-12">
//           <h4 class="text-center">Hi</h4>
//           <div class="col-md-6 d-block mx-auto">
//             <p class="text-center m-0">
//               You're almost set to start as BnByond Seller. Simply click the
//               link below to verify your email address and get started. The link
//               expires in 15mints.
//             </p>
//           </div>
//           <p class="text-center m-0"></p>

//           <div
//             class="px-4 py-2 d-flex justify-content-center align-items-center"
//           >
//             <a
//               class="px-4 py-2 my-2 text-white"
//               style="background-color: darkblue; border-radius: 20px"
//               href="${process.env.frontendLink}/auth/signin"
//               role="button"
//             >
//               Verify Email Address</a
//             >
//           </div>
//           <hr />
//           <p class="text-center m-0">Thanks! the BnByond Team</p>
//           <div class="col-12 d-flex justify-content-center align-items-center">
//             <img
//               src="https://bnbpictures.s3.amazonaws.com/facebook.png"
//               class="mx-2 my-2"
//               alt=""
//               style="cursor: pointer"
//             />
//             <img
//               src="https://bnbpictures.s3.amazonaws.com/insta.png"
//               class="mx-2 my-2"
//               alt=""
//               style="cursor: pointer"
//             />
//             <img
//               src="https://bnbpictures.s3.amazonaws.com/x.png"
//               class="mx-2 my-2"
//               alt=""
//               style="cursor: pointer"
//             />
//           </div>
//         </div>
//         <div
//           class="col-12 p-2 d-flex justify-content-center align-items-center"
//           style="background-color: #daf4f9"
//         >
//           <p class="m-2">| Privacy Policy</p>
//           <p class="m-2">| Contact Details |</p>
//         </div>
//       </div>
//     </div>
//   </body>
// </html>

// `;
// }

export const employDetail = (data) => {
  return (
    `
      <p style='margin-top:0in;margin-right:0in;margin-bottom:8.0pt;margin-left:0in;line-height:107%;font-size:15px;font-family:"Calibri",sans-serif;text-align:center;'>
        Hi ${data.name},<br>&nbsp;welcome to ${data.companyName}!!! your email has been verified.
      </p>
      <div style="background-color:#f4f7fc; padding:20px; text-align:center; border-radius:8px;">
        <h1 style="font-family:Arial, sans-serif; color:#4CAF50; font-size:24px; margin:0;">Welcome to ${data.companyName}!</h1>
        <p style="font-size:16px; line-height:1.6;">We are excited to have you onboard. Below are your account details:</p>
        <p style="font-size:16px; line-height:1.6;"><strong>Your Name:</strong> ${data.name}</p>
        <p style="font-size:16px; line-height:1.6;"><strong>Your Email:</strong> ${data.email}</p>
        <p style="font-size:16px; line-height:1.6;"><strong>Your Role:</strong> ${data.role}</p>
        <p style="font-size:16px; line-height:1.6;"><strong>Your Password:</strong> ${data.password}</p>
        <p style="font-size:16px; line-height:1.6;">To get started, please log in to your account:</p>
        <a href="${data.uniquelink}" style="background-color:#4CAF50; color:#fff; padding:12px 25px; text-decoration:none; font-weight:bold; border-radius:5px;">Log In to Your Account</a>
      </div>
      <div style="margin-top:30px; font-size:14px; text-align:center; color:#777;">
        <p>If you have any questions, feel free to <a href="mailto:<EMAIL>" style="color:#4CAF50; text-decoration:none;">contact support</a>.</p>
        <p>Thanks,<br>${data.companyName} Team</p>
      </div>
     
      <div style="background-color:#daf4f9; text-align:center; padding:10px;">
        <p style="margin:0;">| <a href="{data.privacyPolicy}" style="color:#777; text-decoration:none;">Privacy Policy</a> | <a href="{data.contactDetails}" style="color:#777; text-decoration:none;">Contact Details</a> |</p>
      </div>
      `
  );
}


export const bookingDetailsConfirmationMessage = (data) => {
  return (
    `<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #4A90E2; color: white; padding: 30px 20px; text-align: center; border-radius: 5px;">
            <h1 style="margin: 0; font-size: 24px;">Booking Confirmation</h1>
            <p style="margin: 10px 0 0 0;">Thank you for choosing to stay with us!</p>
        </div>

        <div style="margin-top: 30px;">
            <h2 style="color: #4A90E2; font-size: 18px; margin-bottom: 15px;">Booking Details</h2>
            <div style="margin-bottom: 5px;">
                <strong style="display: inline-block; width: 150px;">Check-in:</strong>
                <span>${data.checkIn}</span>
            </div>
            <div style="margin-bottom: 5px;">
                <strong style="display: inline-block; width: 150px;">Check-out:</strong>
                <span>${data.checkOut}</span>
            </div>
            <div style="margin-bottom: 5px;">
                <strong style="display: inline-block; width: 150px;">Number of nights:</strong>
                <span>${data.NumberOFNights}</span>
            </div>
            <div style="margin-bottom: 5px;">
                <strong style="display: inline-block; width: 150px;">Number of guests:</strong>
                <span>${data.NumberOFGuest}</span>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h2 style="color: #4A90E2; font-size: 18px; margin-bottom: 15px;">Property Address</h2>
            <p style="margin: 0;">${data.propertyAddress}</p>
        </div>

        <div style="margin-top: 30px; background-color: #F5F5F5; padding: 20px; border-radius: 5px;">
            <h2 style="color: #4A90E2; font-size: 18px; margin-bottom: 15px;">Payment Summary</h2>
            <div style="margin-bottom: 5px;">
                <strong style="display: inline-block; width: 150px;">Total amount:</strong>
                <span>${data.pointsCount} points</span>
            </div>
            <div style="margin-bottom: 5px;">
                <strong style="display: inline-block; width: 150px;">Service Fee:</strong>
                <span>${data.serviceFee}</span>
            </div>
           </div>
        <div style="text-align: center; margin-top: 30px;">
            <a href="${data.uniquelink}" style="display: inline-block; background-color: #4A90E2; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Booking Details</a>
        </div>

        <div style="margin-top: 30px; text-align: center; color: #666666; font-size: 14px;">
            <p style="margin-bottom: 5px;">If you need any assistance, please contact <NAME_EMAIL> or call ****************</p>
            <p style="margin: 0;">© 2025 Bnbyond.com. All rights reserved.</p>
            <p style="margin: 0;">168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2</p>
            </div>
    </div>
</body> `
  )
}

export const propertyListingEmailBody = (data) => {
  return `
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You Listing</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f9;">
    <div style="max-width: 600px; margin: 50px auto; background: #ffffff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="background-color: #2c3e50; color: #ffffff; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">Thank You for Your Listing!</h1>
            <p style="margin: 5px 0 0; font-size: 16px;">We're excited to review your property</p>
        </div>

        <div style="padding: 20px; color: #333;">
            <p style="margin: 0 0 10px;">Dear <b>${data.listerName}</b>,</p>
            <p style="margin: 0 0 20px;">Thank you for submitting your property listing to <b>${data.company}</b>. We're thrilled that you've chosen to partner with us!</p>

            <div style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 15px; background-color: #f9f9f9;">
                <h3 style="margin: 0 0 10px; font-size: 18px; color: #2c3e50;">Listing Details</h3>
                <p style="margin: 5px 0; font-size: 16px;"><b>Property Name:</b> ${data.propertyName}</p>
                <p style="margin: 5px 0; font-size: 16px;"><b>Listing ID:</b> #${data.listingId}</p>
                <p style="margin: 5px 0; font-size: 16px;"><b>Submission Date:</b> ${new Date(data.date_Time).toLocaleString()}</p>
            </div>
            <div style="max-width: 600px; margin: 50px auto; background-color: #ffffff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <div style="padding: 20px; text-align: center; background-color: #eaf6ff; border-bottom: 1px solid #d6e9f8;">
                    <h2 style="margin: 0; font-size: 20px; color: #007bff;">Current Status: ${data.currentStatus}</h2>
                    <p style="margin: 5px 0 0; font-size: 16px; color: #555;">Estimated approval time: <b style="color: #007bff;">3-5 business days</b></p>
                </div>
            </div>

            <div style="background-color: #f4f4f4; padding: 20px; border-radius: 8px; font-family: Arial, sans-serif;">
                <h2 style="color: #333; margin-top: 0;">What Happens Next?</h2>
                <ul style="list-style-type: disc; padding-left: 20px;">
                    <li style="color: #666;">Our team reviews your listing details and photos</li>
                    <li style="color: #666;">We verify the property information and documentation</li>
                    <li style="color: #666;">Final approval and listing activation</li>
                </ul>
            </div>

            <div style="background-color: #d4f5d1; margin: 50px auto; padding: 20px; border-radius: 8px; font-family: Arial, sans-serif;">
                <h2 style="color: #333; margin-top: 0;">While You Wait</h2>
                <ul style="list-style-type: disc; padding-left: 20px;">
                    <li style="color: #666;">Complete your host profile</li>
                    <li style="color: #666;">Set up your payment information</li>
                    <li style="color: #666;">Review our hosting guidelines</li>
                    <li style="color: #666;">Prepare your house rules</li>
                </ul>
            </div>

            <div style="background-color: #e6f3ff; padding: 20px; border-radius: 8px; font-family: Arial, sans-serif; text-align: center;">
                <h2 style="color: #333; margin-top: 0;">Need Assistance?</h2>
                <p style="color: #666;">Our host support team is here to help!</p>
                <p style="color: #666;">Email: <a href="mailto:<EMAIL>" style="color: #007bff;">support team</a></p>
                <p style="color: #666;">Phone: ****************</p>
                <a href="#" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">Contact Support</a>
            </div>

            <div style="background-color: #e6f3ff; margin: 50px auto; padding: 20px; border-radius: 8px; font-family: Arial, sans-serif; text-align: center;">
                <p style="color: #666;">This is an automated message. Please do not reply directly to this email.</p>
                <p style="color: #666;">© 2025 ${data.company}. All rights reserved.</p>
                <p style="color: #666;">168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2</p>
            </div>
        </div>
    </div>
</body>
</html>
  `;
}

export const bookingConfirmed = (data) => {
  return `
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You Listing</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f9;">

    <table role="presentation" style="width: 100%; border-collapse: collapse; background-color: #f7f7f7; padding: 20px;">
        <tr>
            <td>
                <!-- Main container -->
                <table role="presentation" style="width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(90deg, #ff5a5f, #17c8b2); border-radius: 12px; text-align: center; padding: 40px 20px; color: #ffffff;">
                    <!-- Header -->
                    <tr>
                        <td style="font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; line-height: 1.5; text-transform: uppercase;">
                            BOOKING CONFIRMED!
                        </td>
                    </tr>
                    <!-- Subtext -->
                    <tr>
                        <td style="font-family: Arial, sans-serif; font-size: 16px; margin-top: 10px;">
                            Get ready for an amazing stay
                        </td>
                    </tr>
                    <!-- Booking Number -->
                    <tr>
                        <td style="padding-top: 20px;">
                            <div style="display: inline-block; background-color: rgba(255, 255, 255, 0.2); padding: 10px 20px; font-size: 16px; font-weight: bold; border-radius: 25px; color: #ffffff; font-family: Arial, sans-serif;">
                                Booking #: 12345678
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <table role="presentation" style="width: 100%; border-collapse: collapse; background-color: #f7f7f7; padding: 20px;">
        <tr>
            <td>
                <table role="presentation" style="width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e3e3e3; border-radius: 6px; padding: 20px;">
                    <!-- Header Section -->
                    <tr>
                        <td style="padding: 10px 20px; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; color: #ff5a5f; border-left: 4px solid #17c8b2;">
                            <span style="font-size: 18px; display: inline-block;">•</span> Booking Details
                        </td>
                    </tr>
                    <!-- Booking Details Section -->
                    <tr>
                        <td style="padding: 10px 20px;">
                            <table role="presentation" style="width: 100%; font-family: Arial, sans-serif; font-size: 14px; color: #333333;">
                                <tr>
                                    <td style="width: 50%; padding: 10px;">
                                        CHECK-IN
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">March 15, 2025</div>
                                    </td>
                                    <td style="width: 50%; padding: 10px;">
                                        CHECK-OUT
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">March 20, 2025</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 50%; padding: 10px;">
                                        GUESTS
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">2 Adults, 1 Child</div>
                                    </td>
                                    <td style="width: 50%; padding: 10px;">
                                        ROOM TYPE
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">Deluxe Ocean View</div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <table role="presentation" style="width: 100%; border-collapse: collapse; background-color: #f7f7f7; padding: 20px;">
        <tr>
            <td>
                <table role="presentation" style="width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e3e3e3; border-radius: 6px; padding: 20px;">
                    <!-- Payment Information Section -->
                    <tr>
                        <td style="padding: 10px 20px; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; color: #ff5a5f; border-left: 4px solid #17c8b2;">
                            <span style="font-size: 18px; display: inline-block;">•</span> Payment Information
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 20px;">
                            <table role="presentation" style="width: 100%; font-family: Arial, sans-serif; font-size: 14px; color: #333333;">
                                <tr>
                                    <td style="width: 50%; padding: 10px;">TOTAL AMOUNT
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">$1,250.00</div>
                                    </td>
                                    <td style="width: 50%; padding: 10px;">PAID AMOUNT
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">$375.00</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 50%; padding: 10px;">BALANCE DUE
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">$875.00</div>
                                    </td>
                                    <td style="width: 50%; padding: 10px;">DUE DATE
                                        <div style="font-size: 16px; font-weight: bold; color: #333333; margin-top: 5px;">March 1, 2025</div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    
    <body style="margin: 0; padding: 0;">
    <table
      role="presentation"
      style="width: 100%; border-collapse: collapse; border-spacing: 0; background-color: #f7f7f7; padding: 20px;"
    >
      <tr>
        <td>
          <table
            role="presentation"
            style="
              width: 100%;
              max-width: 600px;
              margin: 0 auto;
              border-collapse: collapse;
              background-color: #ffffff;
              border: 1px solid #e3e3e3;
              border-radius: 6px;
              padding: 20px;
            "
          >
            <tr>
              <td
                style="
                  padding: 10px 20px;
                  font-family: Arial, sans-serif;
                  font-size: 16px;
                  font-weight: bold;
                  color: #ff5a5f;
                  border-left: 4px solid #17c8b2;
                  line-height: 1.5;
                "
              >
                <span style="font-size: 18px; display: inline-block; vertical-align: middle;">•</span>
                Included Amenities
              </td>
            </tr>
            <tr>
              <td>
                <table
                  role="presentation"
                  style="
                    width: 100%;
                    border-collapse: collapse;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                    color: #333333;
                  "
                >
                  <tr>
                    <td style="width: 50%; padding: 10px;">
                      <span style="color: #17c8b2; font-size: 16px;">✔</span> Free Wi-Fi
                    </td>
                    <td style="width: 50%; padding: 10px;">
                      <span style="color: #17c8b2; font-size: 16px;">✔</span> Breakfast
                    </td>
                  </tr>
                  <tr>
                    <td style="width: 50%; padding: 10px;">
                      <span style="color: #17c8b2; font-size: 16px;">✔</span> Pool Access
                    </td>
                    <td style="width: 50%; padding: 10px;">
                      <span style="color: #17c8b2; font-size: 16px;">✔</span> Parking
                    </td>
                  </tr>
                  <tr>
                    <td style="width: 50%; padding: 10px;">
                      <span style="color: #17c8b2; font-size: 16px;">✔</span> Room Service
                    </td>
                    <td style="width: 50%; padding: 10px;">
                      <span style="color: #17c8b2; font-size: 16px;">✔</span> Gym Access
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>

    <table role="presentation" style="width: 100%; border-collapse: collapse; background-color: #f7f7f7; padding: 20px;">
        <tr>
            <td>
                <table role="presentation" style="width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e3e3e3; border-radius: 6px; padding: 20px;">
                    <!-- Button Section -->
                    <tr>
                        <td style="text-align: center; padding: 20px 0;">
                            <a href="#" style="display: inline-block; background: linear-gradient(to right, #ff5a5f, #17c8b2); color: #ffffff; text-decoration: none; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; padding: 10px 20px; border-radius: 30px;">
                                VIEW BOOKING DETAILS
                            </a>
                        </td>
                    </tr>

                    <!-- Need Help Section -->
                    <tr>
                        <td style="padding: 10px 20px; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; color: #ff5a5f; border-left: 4px solid #17c8b2;">
                            <span style="font-size: 18px;">•</span> Need Help?
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 20px; font-family: Arial, sans-serif; font-size: 14px; color: #333333;">
                            <table role="presentation" style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="width: 50%; padding: 5px;">📞 +1 (555) 123-4567</td>
                                    <td style="width: 50%; padding: 5px;">✉ <EMAIL></td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Footer Section -->
                    <tr>
                        <td style="text-align: center; padding: 20px 10px; font-family: Arial, sans-serif; font-size: 12px; color: #666666;">
                            Thank you for choosing our property!<br>© 2025 Luxury Resort & Spa. All rights reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

</body>
</html>

  `;
}

export const toTheOwnerTemplate = (data) => {
  return `
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trip Invitation</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f9;">
    
    <div style="max-width: 600px; margin: 50px auto; background: #ffffff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="background-color: #FF5A5F; color: #ffffff; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">Get ready! </h1>
        </div>

        <div style="padding: 20px; color: #333;">
            <p style="margin: 0 0 10px;">Hi <b>${data.ownerName}</b>,</p>
            <p style="margin: 0 0 20px;"><b>${data.guestName}</b> booked a place in <b>${data.address}</b>.</p>

            <div style="text-align: center; margin: 20px 0;">
                <a href="${data.tripLink}" style="background-color: #FF5A5F; color: white; padding: 12px 20px; text-decoration: none; font-size: 16px; font-weight: bold; border-radius: 5px; display: inline-block;">
                    View trip details
                </a>
            </div>
        </div>

        <div style="padding: 20px; color: #333;">
            <h2 style="margin: 0; font-size: 22px;">${data.propertyTitle}</h2>
            <p style="margin: 5px 0 10px; font-size: 16px; color: #777;">Entire home/apartment hosted by ${data.ownerName}</p>

            <div style="text-align: center; margin: 20px 0;">
                <a href="${data.propertyLink}" style="background-color: #FF5A5F; color: white; padding: 12px 20px; text-decoration: none; font-size: 16px; font-weight: bold; border-radius: 5px; display: inline-block;">
                    View Listing
                </a>
            </div>
            
            <div style="max-width: 600px; margin: 50px auto; background: #ffffff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="padding: 20px; color: #333;">
            <h2 style="margin: 0; font-size: 22px; text-align: center;">Booking Confirmation</h2>
        </div>

        <div style="padding: 20px; text-align: center; border-bottom: 1px solid #ddd;">
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="width: 50%; padding: 10px; font-size: 18px; font-weight: bold;">${data.checkIn}</td>
                    <td style="width: 50%; padding: 10px; font-size: 18px; font-weight: bold;">${data.checkOut}</td>
                </tr>
                <tr>
                    <td style="padding: 5px; font-size: 14px; color: #777;">Check-in is anytime after 3PM</td>
                    <td style="padding: 5px; font-size: 14px; color: #777;">Check-out 11AM</td>
                </tr>
            </table>
        </div>

        <div style="padding: 20px; text-align: center;">
            <h3 style="margin: 0; font-size: 18px; color: #333;">Confirmation Code</h3>
            <p style="font-size: 20px; font-weight: bold; color: #FF5A5F; margin: 10px 0;">${data.confirmationCode}</p>
        </div>
    </div>

    <div style="max-width: 600px; margin: 50px auto; background: #ffffff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 20px;">
        <h2 style="margin-bottom: 10px;">Meet your host</h2>
        <p style="margin: 5px 0; font-weight: bold;">${data.ownerName}</p>
        <p style="margin: 5px 0; color: #555;">${data.ownerEmail}</p>

        <h3 style="margin-top: 20px; margin-bottom: 5px;">House Manual</h3>
        <p style="margin: 5px 0; color: #555;">
            ${data.houseManual}
        </p>

        <h3 style="margin-top: 20px; margin-bottom: 5px;">House Rules</h3>
        <p style="margin: 5px 0; color: #555;">${data.houseRules}</p>

        <p style="margin-top: 20px; font-size: 12px; color: #777;">
            By emailing the host, you agree to our
            <a href="${data.termsLink}" style="color: #ff5a5f; text-decoration: none;">Terms of Service</a> and
            <a href="${data.privacyLink}" style="color: #ff5a5f; text-decoration: none;">Privacy Policy</a>.
        </p>
    </div>

    <div style="max-width: 600px; margin: 50px auto; background: #ffffff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 20px;">
        <h2 style="margin-bottom: 10px;">Customer support</h2>

        <p style="margin: 5px 0;">
            <a href="${data.helpLink}" style="color: #ff5a5f; text-decoration: none; margin-right: 15px;">Visit Help Center</a>
            <a href="${data.contactLink}" style="color: #ff5a5f; text-decoration: none;">Contact Us</a>
        </p>

        <p style="margin-top: 20px; font-size: 12px; color: #777;">
            Sent with ❤️ from ${data.companyName}
        </p>

        <p style="margin: 5px 0; font-size: 12px; color: #777;">
            ${data.companyAddress}
        </p>
    </div>
        </div>
    </div>
</body>
</html>
  `;
};


export const emailsubscription = (data) => {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Confirmation</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333333; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; padding: 40px 20px; text-align: center; background-color: #ffffff;">
          <div style="font-size: 24px; font-weight: bold; color: #333333; margin-bottom: 20px;">
            Hey ${data.email}! Thank You for Subscribing!
          </div>
          
          <div style="font-size: 16px; margin-bottom: 20px;">
            We're excited to have you on board. You'll start receiving our updates,
            offers, and news very soon!
          </div>
          
          <div style="font-size: 16px; margin-bottom: 20px;">
            If you have any questions or need assistance, feel free to 
            <a href="#" style="color: #ffc107; text-decoration: none;">contact us</a>.
          </div>
          
          <div style="background-color: #ffc107; padding: 20px; border-radius: 8px; margin: 30px 0; color: #ffffff; font-size: 16px;">
            You are now officially subscribed to our service. We hope you enjoy our updates!
          </div>
          
          <div style="font-size: 14px; color: #666666; margin-top: 30px;">
            We value your privacy and will never share your information.
          </div>
        </div>
      </body>
    </html>
  `;
}


export const propertyVerify = (data) => {
  const { userName, property_address, property_id } = data;
  return (
    `
 <body style="font-family: 'Arial', sans-serif; margin: 0; padding: 0; background-color: #f7f7f7;">
  <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
    <h2 style="text-align: center; color: #4CAF50; font-size: 24px;">🎉 Congratulations, ${userName}!</h2>
    <p style="font-size: 16px; color: #555555; text-align: center; margin-bottom: 20px; line-height: 1.6;">
      We are thrilled to inform you that your property <strong>${property_address}</strong> (Property ID: <strong>${property_id}</strong>) has been successfully added to BnByond’s available STR listings for other Members to consider. 
    </p>

    <div style="text-align: center; margin-bottom: 30px;">
      <a href="https://beta.bnbyond.com/my-properties" style="background-color: #4CAF50; color: #ffffff; padding: 12px 30px; text-decoration: none; border-radius: 30px; font-size: 16px; font-weight: bold; transition: background-color 0.3s;">
        View Your Property
      </a>
    </div>

    <p style="font-size: 14px; color: #777777; text-align: center; line-height: 1.6;">
      Your vacation rental property is now live and ready for the BnByond community to see. If you have any questions, feel free to reach out to our team at any time. We’re here to assist you make the most out of your experience with the community!
    </p>

    <footer style="text-align: center; font-size: 12px; color: #aaaaaa; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eeeeee;">
      <p>&copy; If you need any assistance, please contact <NAME_EMAIL> or call ****************</p>
      <p>&copy; 2025 Bnbyond.com All Rights Reserved.</p>
      <p style="font-size: 12px;">168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2</p>
      <p><a href="https://beta.bnbyond.com/unsubscribe" style="color: #4CAF50; text-decoration: none;">Unsubscribe</a></p>
    </footer>
  </div>
</body>
  `
  )
}

export const propertyRejected = (data) => {
  return (
    `
<body style="font-family: 'Arial', sans-serif; margin: 0; padding: 0; background-color: #f7f7f7;">
  <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
    <h2 style="text-align: center; color: #FF6347; font-size: 24px;">We're Sorry!</h2>
    <p style="font-size: 16px; color: #555555; text-align: center; margin-bottom: 20px; line-height: 1.6;">
      Unfortunately, your property listing has not been approved. We know this news may be disappointing, but we encourage you to review our listing guidelines and make any necessary adjustments.
    </p>

    <div style="text-align: center; margin-bottom: 30px;">
      <a href="https://beta.bnbyond.com/guidelines" style="background-color: #FF6347; color: #ffffff; padding: 12px 30px; text-decoration: none; border-radius: 30px; font-size: 16px; font-weight: bold; transition: background-color 0.3s;">
        Review Guidelines
      </a>
    </div>

    <p style="font-size: 14px; color: #777777; text-align: center; line-height: 1.6;">
      Please don't be discouraged! We're here to help you improve your listing and give it another shot. If you believe this decision was made in error, feel free to reach out to our support team.
    </p>

    <footer style="text-align: center; font-size: 12px; color: #aaaaaa; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eeeeee;">
      <p>&copy; If you need any assistance, please contact <NAME_EMAIL> or call ****************</p>
      <p>&copy; 2025 Bnbyond.com All Rights Reserved.</p>
      <p style="font-size: 12px;">168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2</p>
      <p><a href="https://beta.bnbyond.com/unsubscribe" style="color: #FF6347; text-decoration: none;">Unsubscribe</a></p>
    </footer>
  </div>
</body>
`
  )
}


export const transactionDetails = (data) => {

  if (!data) {
    console.warn("No transaction data provided.");
    return "Error: No transaction details available.";
  }

  const {
    reservationCode,
    propertyTitle,
    propertyAddress,
    checkInDate,
    checkOutDate,
    guestCount,
    roomCount,
    pointsUsed,
    serviceFee,
    hostName,
    totalAmount,
    propertyImage
  } = data;

  if (!reservationCode || !propertyTitle || !propertyAddress || !checkInDate || !checkOutDate || !guestCount || !roomCount || !hostName || !totalAmount) {
    console.warn("Missing required transaction details.");
  }

  return `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; background-color: #f9f9f9;">
      <div style="background-color: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div style="text-align: center; padding-bottom: 20px; border-bottom: 2px solid #f0f0f0;">
          <h1 style="color: #333; margin: 0;">Transaction Details</h1>
          <p style="color: #666; margin: 10px 0;">Reservation Code: ${reservationCode}</p>
        </div>

        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin: 0 0 15px 0;">Stay Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666;">Check-in</td>
              <td style="padding: 8px 0; color: #333; text-align: right;">${checkInDate}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;">Check-out</td>
              <td style="padding: 8px 0; color: #333; text-align: right;">${checkOutDate}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;">Guests</td>
              <td style="padding: 8px 0; color: #333; text-align: right;">${guestCount}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;">Rooms</td>
              <td style="padding: 8px 0; color: #333; text-align: right;">${roomCount}</td>
            </tr>
          </table>
        </div>

        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin: 0 0 15px 0;">Payment Summary</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666;">Points Used</td>
              <td style="padding: 8px 0; color: #333; text-align: right;">${pointsUsed ? pointsUsed : 0} points</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666;">Service Fee</td>
              <td style="padding: 8px 0; color: #333; text-align: right;">$${serviceFee}</td>
            </tr>
            <tr style="border-top: 1px solid #ddd;">
              <td style="padding: 15px 0; color: #333; font-weight: bold;">Total</td>
              <td style="padding: 15px 0; color: #333; font-weight: bold; text-align: right;">${totalAmount}</td>
            </tr>
          </table>
        </div>

        <div style="margin: 20px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h3 style="color: #333; margin: 0 0 10px 0;">Host Information</h3>
          <p style="color: #666; margin: 5px 0;">Your host for this stay is ${hostName}</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
          <a href="https://beta.bnbyond.com/mytrip" style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; display: inline-block;">View Booking Details</a>
        </div>

        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #999; text-align: center;">
          <p>If you need any assistance, please contact <NAME_EMAIL> or call ****************</p>
          <p>© 2025 Bnbyond.com All Rights Reserved.</p>
          <p>168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2</p>
          <p><a href="#" style="color: #999;">Unsubscribe</a></p>
        </div>
      </div>
    </div>
  `;
};

export const contactHostEmailTemplate = (data) => {
  const { guestName, hostName, propertyTitle, checkInDate, checkOutDate, guestMessage } = data;

  return `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <div style="background-color: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2 style="color: #333;">Property Inquiry</h2>
        
        <div style="margin: 20px 0;">
          <p style="color: #666;">Dear ${hostName},</p>
          <p style="color: #666;">I'm interested in your property and would like to learn more about it.</p>

          ${guestMessage ? `
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin: 0 0 15px 0;">Message</h3>
            <p style="color: #666;">${guestMessage}</p>
          </div>
          ` : ''}
        </div>

      </div>
    </div>
  `;
};

export const activateSubscription = (data) => {
  const {
    customerName,
    plan,
    amount,
    startDate,
    nextBillingDate,
    paymentMethod,
    receiptNumber,
    hostedInvoiceUrl,
  } = data;
  return `
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 600px; margin: auto; background-color: #ffffff;">
        <!-- Header -->
        <tr>
            <td style="padding: 20px 0; text-align: center; background-color: #635BFF;">
                <h1 style="color: #ffffff; margin: 0; padding: 0; font-size: 24px;">Your Subscription Confirmation</h1>
            </td>
        </tr>
        
        <!-- Main Content -->
        <tr>
            <td style="padding: 40px 30px;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                        <td style="padding-bottom: 20px;">
                            <h2 style="margin: 0; font-size: 20px; color: #32325d;">Your ${plan} Subscription is Active!</h2>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding-bottom: 20px; line-height: 1.5; color: #525f7f;">
                            <p style="margin: 0;">Hello ${customerName},</p>
                            <p style="margin: 16px 0 0 0;">Thank you for subscribing to our ${plan} plan. Your subscription is now active and ready to use.</p>
                            <p style="margin: 16px 0 0 0;">We've processed your payment successfully through Stripe, and you'll be billed according to the terms of your subscription.</p>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding-bottom: 20px; line-height: 1.5; color: #525f7f;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="border: 1px solid #e6ebf1; border-radius: 4px;">
                                <tr>
                                    <td style="padding: 15px; background-color: #f6f9fc; border-bottom: 1px solid #e6ebf1;">
                                        <h3 style="margin: 0; font-size: 16px; color: #32325d;">Subscription Details</h3>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 15px;">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                            <tr>
                                                <td style="padding: 5px 0; color: #525f7f; width: 40%;">Plan:</td>
                                                <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${plan}</td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 5px 0; color: #525f7f; width: 40%;">Amount:</td>
                                                <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${amount}</td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 5px 0; color: #525f7f; width: 40%;">Start Date:</td>
                                                <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${startDate}</td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 5px 0; color: #525f7f; width: 40%;">Next Billing Date:</td>
                                                <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${nextBillingDate}</td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 5px 0; color: #525f7f; width: 40%;">Payment Method:</td>
                                                <td style="padding: 5px 0; color: #32325d; font-weight: bold;">•••• •••• •••• ${paymentMethod}</td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 5px 0; color: #525f7f; width: 40%;">Receipt #:</td>
                                                <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${receiptNumber}</td>
                                            </tr>
                                            ${hostedInvoiceUrl ? `
                                               <tr>
                                       <td style="padding: 5px 0; color: #525f7f; width: 40%;">Receipt Link:</td>
                                      <td style="padding: 10px 0;">
                                            <a href="${hostedInvoiceUrl}" target="_blank" style="display: inline-block; padding: 10px 20px; background-color: #6772e5; color: #ffffff; text-decoration: none; font-weight: bold; border-radius: 5px; font-family: Arial, sans-serif; font-size: 14px;">
                                                View Invoice
                                                           </a>
                                                              </td>
                                                               </tr>
                                                                  ` : ''}                                           
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding-bottom: 20px;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" style="margin: auto;">
                                <tr>
                                    <td style="border-radius: 4px; background-color: #635BFF; text-align: center;">
                                        <a href="https://beta.bnbyond.com/manage-subscription" style="background-color: #635BFF; border: 15px solid #635BFF; font-family: Arial, sans-serif; font-size: 16px; line-height: 1.1; text-align: center; text-decoration: none; display: block; border-radius: 4px; font-weight: bold; color: #ffffff;">Manage Your Subscription</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        
        <tr>
            <td style="padding: 30px; background-color: #f6f9fc; border-top: 1px solid #e6ebf1; text-align: center;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                        <td style="color: #525f7f; font-size: 14px; line-height: 1.5;">
                            <p style="margin: 0;">Questions about your subscription? Visit our <a href="mailto:<EMAIL>" style="color: #635BFF; text-decoration: underline;">Help Center</a> or contact our <a href="mailto:<EMAIL>" style="color: #635BFF; text-decoration: underline;">support team</a>.</p>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding-top: 20px; color: #8898aa; font-size: 12px;">
                            <p style="margin: 0;">This transaction appears on your statement as Bnbyond.</p>
                            <p style="margin: 8px 0 0 0;">You can <a href="https://beta.bnbyond.com/manage-subscription" style="color: #635BFF; text-decoration: underline;">cancel your subscription</a> at any time.</p>
                            <p style="margin: 8px 0 0 0;">
                                <a href="https://beta.bnbyond.com/terms" style="color: #635BFF; text-decoration: underline;">Terms of Service</a> | 
                                <a href="https://beta.bnbyond.com/privacy" style="color: #635BFF; text-decoration: underline;">Privacy Policy</a>
                            </p>
                            <p style="margin: 8px 0 0 0;">© ${new Date().getFullYear()} Bnbyond. All rights reserved.</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
  `;
};

export const CancelSubscription = (data) => {
  return `
    <html>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 600px; margin: auto; background-color: #ffffff;">
            <!-- Header -->
            <tr>
                <td style="padding: 20px 0; text-align: center; background-color: #635BFF;">
                    <h1 style="color: #ffffff; margin: 0; padding: 0; font-size: 24px;">Subscription Cancellation</h1>
                </td>
            </tr>
            
            <!-- Main Content -->
            <tr>
                <td style="padding: 40px 30px;">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                        <tr>
                            <td style="padding-bottom: 20px;">
                                <h2 style="margin: 0; font-size: 20px; color: #32325d;">Your Subscription Has Been Cancelled</h2>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-bottom: 20px; line-height: 1.5; color: #525f7f;">
                                <p style="margin: 0;">Hello ${data.customerName},</p>
                                <p style="margin: 16px 0 0 0;">We're sorry to see you go. Your subscription to <strong>${data.subscriptionPlan}</strong> has been successfully cancelled.</p>
                                <p style="margin: 16px 0 0 0;">You will continue to have access to your subscription benefits until the end of your current billing period on <strong>${data.accessUntil}</strong>.</p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-bottom: 20px; line-height: 1.5; color: #525f7f;">
                                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="border: 1px solid #e6ebf1; border-radius: 4px;">
                                    <tr>
                                        <td style="padding: 15px; background-color: #f6f9fc; border-bottom: 1px solid #e6ebf1;">
                                            <h3 style="margin: 0; font-size: 16px; color: #32325d;">Cancellation Details</h3>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px;">
                                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                                <tr>
                                                    <td style="padding: 5px 0; color: #525f7f; width: 40%;">Subscription:</td>
                                                    <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${data.subscriptionPlan}</td>
                                                </tr>
                                                <tr>
                                                    <td style="padding: 5px 0; color: #525f7f; width: 40%;">Cancellation Date:</td>
                                                    <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${data.cancellationDate}</td>
                                                </tr>
                                                <tr>
                                                    <td style="padding: 5px 0; color: #525f7f; width: 40%;">Access Until:</td>
                                                    <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${data.accessUntil}</td>
                                                </tr>
                                                <tr>
                                                    <td style="padding: 5px 0; color: #525f7f; width: 40%;">Confirmation #:</td>
                                                    <td style="padding: 5px 0; color: #32325d; font-weight: bold;">${data.cancellationReference}</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-bottom: 20px; line-height: 1.5; color: #525f7f;">
                                <p style="margin: 0;">If you have any questions about your cancellation or would like to share why you decided to cancel, please contact our customer support team.</p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="padding: 30px; background-color: #f6f9fc; text-align: center;">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                        <tr>
                            <td style="color: #525f7f; font-size: 14px; line-height: 1.5;">
                                <p style="margin: 0;">Need help? Contact our <a href="mailto:<EMAIL>" style="color: #635BFF; text-decoration: underline;">support team</a>.</p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-top: 20px; color: #8898aa; font-size: 12px;">
                                <p style="margin: 0;">If you did not cancel your subscription or believe this is an error, please contact us immediately.</p>
                                <p style="margin: 8px 0 0 0;">
                                    <a href="https://beta.bnbyond.com/terms" style="color: #635BFF; text-decoration: underline;">Terms of Service</a> | 
                                    <a href="https://beta.bnbyond.com/privacy" style="color: #635BFF; text-decoration: underline;">Privacy Policy</a>
                                </p>
                                <p style="margin: 8px 0 0 0;">© ${new Date().getFullYear()} BnByond. All rights reserved.</p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
  `;
};



export const influencerSignupTemplate = (data) => {

  const { UserName, referralLink, userReferralCode } = data;
  return (
    `<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Welcome to BnByond</title>
    <style>
      :root {
        --yellow: #f2b619;
        --white: #ffffff;
        --primary: #04a5c2;
        --color-lightgrey: #f9f9f9;
        --bg-auth: rgba(255, 255, 255, 0.3);
      }
      body {
        margin: 0;
        padding: 0;
        font-family: 'Helvetica Neue', sans-serif;
        background-color: var(--color-lightgrey);
        color: #333;
      }
      .container {
        max-width: 600px;
        margin: 40px auto;
        background: var(--white);
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        overflow: hidden;
      }
      .header {
        background-color: var(--primary);
        color: var(--white);
        padding: 30px 40px;
        text-align: center;
      }
      .header h1 {
        margin: 0;
        font-size: 28px;
      }
      .content {
        padding: 40px;
        line-height: 1.6;
      }
      .content h2 {
        color: var(--primary);
        margin-bottom: 12px;
      }
      .code-box {
        background-color: #f2f4f8;
        padding: 15px;
        border-radius: 6px;
        margin: 10px 0 20px;
        font-family: monospace;
        font-size: 16px;
        word-break: break-word;
      }
      .btn {
        display: inline-block;
        margin-top: 20px;
        padding: 12px 25px;
        background-color: var(--primary);
        color: var(--white);
        text-decoration: none;
        border-radius: 8px;
        font-weight: bold;
      }
      .footer {
        background-color: #f2f2f2;
        text-align: center;
        padding: 20px 30px;
        font-size: 14px;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Welcome to the STR Innovator Program!</h1>
      </div>
      <div class="content">
        <p>Hi ${UserName}!</p>

        <p>Welcome to the <strong>BnByond STR Innovator Program</strong>! We're excited to have you join the community. The program is designed to reward your influence in this industry, to reward you with unlimited free accommodations, and to further celebrate your role (and opportunities) as a community expert.</p>

        <h2>Your Innovator Code:</h2>
        <div class="code-box">${userReferralCode}</div>
        <h2>Your Shareable Link:</h2>
        <div class="code-box">${referralLink}</div>

        <p>As you're aware, <strong>BnByond</strong> is the Vacation Rental Property Owners Exchange Community. It's a simple concept: Host other STR owners, earn points, and use those points for free stays at member properties around the world, all while allowing for a more successful industry through sharing ideas and expert advice from our Innovators.</p>

        <p>You now have a free membership. You are not required to own or list an STR property yourself.</p>

        <p>Your participation is easy: share your unique link with STR owners. For every new member who signs up via your link, you will receive <strong>50 points</strong>. These points allow you and your team free accommodations at any STR in the BnByond network. No limitations.</p>

        <p style="margin-top: 30px;">We are thrilled to have you as a partner in growing this community-centric brand.</p>

        <p>Welcome aboard!</p>

        <p>The BnByond Team<br />
        <a href="https://www.beta.bnbyond.com">bnbyond.com</a></p>
      </div>
      <div class="footer">
        &copy; 2025 BnByond. All rights reserved.
      </div>
    </div>
  </body>
</html>

`
  )
}



export const afterListingNotification = (data) => {
  return (`
  
  <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Congratulations Email</title>
  <style>
    :root {
      --primary: #04a5c2;
    }
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f4f6f8;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: auto;
      background: #fff;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    .header {
      text-align: center;
      padding-bottom: 20px;
    }
    .header h1 {
      color: var(--primary);
      font-size: 24px;
    }
    .content {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    .button-wrapper {
      text-align: center;
      margin-bottom: 30px;
    }
    .btn {
      background-color: var(--primary);
      color: #fff;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 6px;
      font-weight: bold;
      display: inline-block;
    }
    .footer {
      text-align: center;
      font-size: 13px;
      color: #666;
      line-height: 1.5;
    }
    .footer a {
      color: var(--primary);
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🎉 Congratulations!</h1>
    </div>
    <div class="content">
      <p>Congratulations <strong>${data.userName}</strong> on listing your <strong>BnByond</strong> property! We're looking forward to a long, beautiful relationship that will result in not just travel for you, but a chance to learn from your community of STR owners.</p>
      <p><strong>Note:</strong> Your annual Membership Fee <strong>${data.subscriptionAmount}$</strong> will not be charged to the credit card on file until you make or accept your first booking.</p>
      <p>Now go make the world your happy place!</p>
      <p><strong>Thank you for choosing BnByond.</strong> We are excited to have you as part of our community!</p>
    </div>
    <div class="button-wrapper">
      <a href="https://beta.bnbyond.com/profile" target=_blank class="btn">Subscribe to Membership</a>
    </div>
    <div class="footer">
      <p>Need help? Email us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
      <p>168 Hobsons Lake Dr Suite 301<br>Beechville, NS, Canada B3S 1A2</p>
      <p>&copy; 2025 BnByond. All rights reserved.</p>
    </div>
  </div>
</body>
</html>

  
  
  `)

}
export const subscriptionNotification = (data) => {
  const { userName, upgradeLink } = data
  return (`
  <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>First Listing & Reservation Notification</title>
  <style>
    :root {
      --primary: #04a5c2;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f4f6f8;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 30px auto;
      background: #fff;
      padding: 40px;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header h2 {
      color: var(--primary);
      font-size: 26px;
      margin: 0;
    }
    .content {
      font-size: 16px;
      line-height: 1.7;
      margin-bottom: 30px;
    }
    .content strong {
      color: var(--primary);
    }
    .btn-wrapper {
      text-align: center;
    }
    .btn {
      background-color: var(--primary);
      color: #fff;
      padding: 12px 28px;
      text-decoration: none;
      border-radius: 6px;
      font-weight: bold;
      display: inline-block;
      font-size: 15px;
      margin-top: 10px;
    }
    .footer {
      text-align: center;
      font-size: 13px;
      color: #777;
      margin-top: 40px;
      line-height: 1.6;
    }
    .footer a {
      color: var(--primary);
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2>🚀 Great News!</h2>
    </div>
    <div class="content">
      <p>Hello ${userName}</p>
      <p>We noticed you've just created your <strong>first listing</strong> and completed your <strong>first reservation</strong>  congratulations! 🎉</p>
      <p>We truly hope you're enjoying your experience with <strong>BnByond</strong>, and we’re so excited to see your journey unfold in the short-term rental world.</p>
      <p>To unlock the <strong>full power and premium features</strong> of BnByond, including priority support, visibility boosts, exclusive resources, and more  it's time to activate your membership!</p>
    </div>
    <div class="btn-wrapper">
      <a href=${upgradeLink} class="btn">Subscribe to Membership</a>
    </div>
    <div class="footer">
      <p>Thank you for being a part of the BnByond community.</p>
      <p>Need help? Email us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
      <p>168 Hobsons Lake Dr Suite 301,<br />Beechville, NS, Canada B3S 1A2</p>
      <p>&copy; 2025 BnByond. All rights reserved.</p>
    </div>
  </div>
</body>
</html> 
  `)

}



export const thanksEmailTemplate = (data) => {
  const { fname, lname } = data;
  return (
    `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Thank You</title>
</head>
<body style="margin:0; padding:0; background-color:#f0f2f5; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <table width="100%" cellpadding="0" cellspacing="0" style="padding:40px 0;">
    <tr>
      <td align="center">
        <table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff; border-radius:10px; padding:40px 30px; box-shadow:0 4px 12px rgba(0,0,0,0.08);">
          <tr>
            <td align="center" style="font-size:22px; font-weight:500; color:#1a1a1a; padding-bottom:10px;">
              Hello <strong>${fname} ${lname}</strong>,
            </td>
          </tr>
          <tr>
            <td align="center" style="font-size:26px; font-weight:600; color:#1a1a1a; padding-bottom:10px;">
              🎉 Thank You!
            </td>
          </tr>
          <tr>
            <td align="center" style="font-size:17px; color:#444; line-height:1.6; padding-bottom:20px;">
              We appreciate you taking the time to submit the form. <br/>
              One of our team members will be in touch with you shortly.
            </td>
          </tr>
          <tr>
            <td align="center" style="border-top:1px solid #eee; padding-top:25px; font-size:14px; color:#666;">
              <strong>Contact Us</strong><br/>
              <a href="mailto:<EMAIL>" style="color:#3366cc; text-decoration:none;"><EMAIL></a><br/>
              ****************<br/>
              168 Hobsons Lake Dr Suite 301,<br/>
              Beechville, NS, Canada B3S 1A2
            </td>
          </tr>
          <tr>
            <td align="center" style="padding-top:30px; font-size:12px; color:#aaa;">
              © 2025 BnByond. All rights reserved.
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>

  `
  );
}