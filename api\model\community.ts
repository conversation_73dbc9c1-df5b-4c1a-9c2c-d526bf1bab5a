import mongoose from "mongoose";

const repliesSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Users',
  },
  description: {
    type: String,
  },
  timeStamp: {
    type: Date,
    default: Date.now,
  },
});

const likesSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Users',
  },
});

const communitySchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'Users' },
 
  subject: { type: String },

  description: { type: Object },

  category: { type: String },
  replies: [ repliesSchema ],
  likes: [ likesSchema ],
  timeStamp: {
    type: Date,
    default: Date.now,
  },
});

export const CommunityPost = mongoose.model("community", communitySchema);
