import React from "react";
import { ButtonFilled } from "common/buttons/buttonFilled";
import { useNavigate } from "react-router-dom";

export const PayoutsStepTwo = ({ setViewPayout, setViewStepTwo }) => {

  const navigate = useNavigate();

  const handleSubmit = () => {
    setViewPayout(false)
    setViewStepTwo(false)

  }

  const AddDebitCardInfo = () => {
    navigate('/account')
  }
  return (
    <div>
      <div className="p-5 lg:ml-5 flex flex-col">
        <p>Set up payouts</p>
        <div className="">
          <p className="text-black  text-[30px] font-medium leading-[43px] capitalize">
            Add debit card info
          </p>
          <p className="text-gray-700 font-Avenir text-base font-normal leading-[143.4%] capitalize">
            Most debit Visa and Mastercards will work, including reloadable
            cards.{" "}
            <span className="cursor-pointer text-[#000] font-Lato text-base font-normal leading-[164%] underline">
              {" "}
              Learn more
            </span>
          </p>
        </div>

        <div className="md:flex mt-8">
          <div className="  flex mx-2 gap-4">

            <div className="cursor-pointer">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="116"
                height="50"
                viewBox="0 0 116 50"
                fill="none"
              >
                <path
                  d="M0.499023 10.8965C0.499023 5.64978 4.75232 1.39648 9.99902 1.39648H105.764C111.01 1.39648 115.264 5.64978 115.264 10.8965V39.7125C115.264 44.9592 111.01 49.2125 105.764 49.2125H9.99902C4.75231 49.2125 0.499023 44.9592 0.499023 39.7125V10.8965Z"
                  fill="#2459BF"
                  stroke="white"
                />
                <path
                  d="M48.4913 13.0352L44.4708 38.1462H50.8971L54.9153 13.0352H48.4913ZM67.8756 23.264C65.6299 22.1509 64.2532 21.3997 64.2532 20.2613C64.2807 19.2262 65.4174 18.1658 67.9558 18.1658C70.0437 18.1132 71.5783 18.6056 72.7425 19.0956L73.3256 19.3569L74.1993 14.103C72.93 13.6106 70.9174 13.0679 68.4314 13.0679C62.0852 13.0679 57.6165 16.3795 57.5888 21.1159C57.5363 24.611 60.7881 26.5505 63.2216 27.714C65.7076 28.9075 66.5539 29.6813 66.5539 30.7417C66.5264 32.3698 64.5437 33.1211 62.6936 33.1211C60.1302 33.1211 58.7532 32.7341 56.6629 31.827L55.8167 31.44L54.918 36.9C56.4276 37.5735 59.2038 38.1663 62.0855 38.1966C68.8298 38.1966 73.2183 34.9352 73.2735 29.8874C73.2954 27.1185 71.5807 24.9976 67.8756 23.264ZM90.6745 13.1129H85.7026C84.1705 13.1129 83.0063 13.5552 82.3429 15.1331L72.7971 38.1462H79.5414L81.3989 33.1638H88.9445L89.9082 38.1663H95.8566L90.6745 13.1129ZM83.2693 28.1356C83.3995 28.1483 85.858 20.0023 85.858 20.0023L87.8157 28.1356H83.2693ZM39.1009 13.0352L32.8047 30.0958L32.1188 26.7313C30.9546 22.8492 27.3047 18.6307 23.2314 16.5352L28.9969 38.1211H35.7965L45.9005 13.0379H39.1009V13.0352Z"
                  fill="white"
                />
                <path
                  d="M30.1891 16.2321C29.6937 14.3391 28.1138 13.0624 25.9681 13.0352H15.9006L15.7969 13.4945C23.6503 15.3973 30.2421 21.2523 32.3806 26.7596L30.1891 16.2321Z"
                  fill="white"
                />
              </svg>
            </div>
            <div className="cursor-pointer">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="117"
                height="50"
                viewBox="0 0 117 50"
                fill="none"
              >
                <path
                  d="M1.2627 10.8965C1.2627 5.64978 5.51599 1.39648 10.7627 1.39648H106.527C111.774 1.39648 116.027 5.64978 116.027 10.8965V39.7125C116.027 44.9592 111.774 49.2125 106.527 49.2125H10.7627C5.51599 49.2125 1.2627 44.9592 1.2627 39.7125V10.8965Z"
                  fill="white"
                  stroke="#AFB5C1"
                />
                <path
                  d="M73.7512 15.2246H67.1236C66.7337 15.2246 66.3439 15.6145 66.1489 16.0043L63.4199 33.1581C63.4199 33.548 63.6148 33.7429 64.0047 33.7429H67.5134C67.9033 33.7429 68.0982 33.548 68.0982 33.1581L68.8779 28.2849C68.8779 27.895 69.2678 27.5052 69.8526 27.5052H71.9968C76.4802 27.5052 79.0143 25.3609 79.5991 21.0725C79.9889 19.3181 79.5991 17.7587 78.8193 16.784C77.6498 15.8094 75.8954 15.2246 73.7512 15.2246ZM74.5309 21.6573C74.141 23.9964 72.3867 23.9964 70.6323 23.9964H69.4627L70.2425 19.5131C70.2425 19.3181 70.4374 19.1232 70.8272 19.1232H71.2171C72.3867 19.1232 73.5563 19.1232 74.141 19.9029C74.5309 20.0978 74.5309 20.6826 74.5309 21.6573Z"
                  fill="#139AD6"
                />
                <path
                  d="M25.9943 15.2246H19.3667C18.9769 15.2246 18.587 15.6145 18.3921 16.0043L15.6631 33.1581C15.6631 33.548 15.858 33.7429 16.2479 33.7429H19.3667C19.7566 33.7429 20.1465 33.353 20.3414 32.9632L21.1211 28.2849C21.1211 27.895 21.511 27.5052 22.0958 27.5052H24.24C28.7234 27.5052 31.2574 25.3609 31.8422 21.0725C32.2321 19.3181 31.8422 17.7587 31.0625 16.784C29.8929 15.8094 28.3335 15.2246 25.9943 15.2246ZM26.7741 21.6573C26.3842 23.9964 24.6298 23.9964 22.8755 23.9964H21.9008L22.6805 19.5131C22.6805 19.3181 22.8755 19.1232 23.2653 19.1232H23.6552C24.8248 19.1232 25.9943 19.1232 26.5791 19.9029C26.7741 20.0978 26.969 20.6826 26.7741 21.6573ZM46.0721 21.4624H42.9532C42.7583 21.4624 42.3684 21.6573 42.3684 21.8522L42.1735 22.8269L41.9786 22.437C41.1988 21.4624 39.8343 21.0725 38.2749 21.0725C34.7662 21.0725 31.6473 23.8015 31.0625 27.5052C30.6726 29.4545 31.2574 31.2088 32.2321 32.3784C33.2067 33.548 34.5712 33.9378 36.3256 33.9378C39.2495 33.9378 40.809 32.1835 40.809 32.1835L40.6141 33.1581C40.6141 33.548 40.809 33.7429 41.1988 33.7429H44.1228C44.5126 33.7429 44.9025 33.353 45.0974 32.9632L46.8518 22.0471C46.6569 21.8522 46.267 21.4624 46.0721 21.4624ZM41.5887 27.7001C41.1988 29.4545 39.8343 30.819 37.885 30.819C36.9104 30.819 36.1307 30.4291 35.7408 30.0392C35.351 29.4545 35.156 28.6747 35.156 27.7001C35.351 25.9457 36.9104 24.5812 38.6648 24.5812C39.6394 24.5812 40.2242 24.9711 40.809 25.3609C41.3938 25.9457 41.5887 26.9204 41.5887 27.7001Z"
                  fill="#263B80"
                />
                <path
                  d="M93.6336 21.4621H90.5148C90.3198 21.4621 89.93 21.6571 89.93 21.852L89.735 22.8266L89.5401 22.4368C88.7604 21.4621 87.3959 21.0723 85.8365 21.0723C82.3277 21.0723 79.2089 23.8013 78.6241 27.5049C78.2342 29.4542 78.819 31.2086 79.7936 32.3782C80.7683 33.5478 82.1328 33.9376 83.8872 33.9376C86.8111 33.9376 88.3705 32.1832 88.3705 32.1832L88.1756 33.1579C88.1756 33.5477 88.3705 33.7427 88.7604 33.7427H91.6843C92.0742 33.7427 92.4641 33.3528 92.659 32.963L94.4134 22.0469C94.2184 21.852 94.0235 21.4621 93.6336 21.4621ZM89.1503 27.6999C88.7604 29.4542 87.3959 30.8187 85.4466 30.8187C84.472 30.8187 83.6922 30.4289 83.3024 30.039C82.9125 29.4542 82.7176 28.6745 82.7176 27.6999C82.9125 25.9455 84.4719 24.581 86.2263 24.581C87.201 24.581 87.7858 24.9709 88.3705 25.3607C89.1503 25.9455 89.3452 26.9201 89.1503 27.6999Z"
                  fill="#139AD6"
                />
                <path
                  d="M63.032 21.4629H59.7182C59.3284 21.4629 59.1334 21.6578 58.9385 21.8527L54.6501 28.4804L52.7008 22.2426C52.5058 21.8527 52.3109 21.6578 51.7261 21.6578H48.6072C48.2174 21.6578 48.0225 22.0477 48.0225 22.4375L51.5312 32.7688L48.2174 37.4471C48.0225 37.837 48.2174 38.4217 48.6072 38.4217H51.7261C52.116 38.4217 52.3109 38.2268 52.5058 38.0319L63.227 22.6325C63.8117 22.0477 63.4219 21.4629 63.032 21.4629Z"
                  fill="#263B80"
                />
                <path
                  d="M97.3374 15.8101L94.6084 33.3537C94.6084 33.7436 94.8033 33.9385 95.1932 33.9385H97.9222C98.3121 33.9385 98.7019 33.5487 98.8968 33.1588L101.626 16.005C101.626 15.6151 101.431 15.4202 101.041 15.4202H97.9222C97.7273 15.2253 97.5323 15.4202 97.3374 15.8101Z"
                  fill="#139AD6"
                />
              </svg>
            </div>
          </div>
          <div className="mt-2 md:mt-0 flex mx-2 gap-4">
            <div>
              <div className="flex cursor-pointer items-center justify-center w-[115px] border-[1px] border-[#AFB5C1]  rounded-lg h-[50px]  ">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="62"
                  height="27"
                  viewBox="0 0 62 27"
                  fill="none"
                >
                  <path
                    d="M12.2493 3.95681C12.965 3.06152 13.4509 1.8594 13.3227 0.630859C12.275 0.682967 10.9963 1.32232 10.2559 2.21834C9.59132 2.98541 9.00286 4.23795 9.15627 5.41486C10.3325 5.5169 11.5077 4.82689 12.2493 3.95681ZM13.3094 5.64486C11.6012 5.54307 10.1488 6.61431 9.3332 6.61431C8.51692 6.61431 7.26779 5.69624 5.91661 5.72097C4.15827 5.74666 2.52644 6.74107 1.63406 8.32224C-0.201348 11.4858 1.14982 16.1784 2.93458 18.755C3.80151 20.0296 4.8461 21.4331 6.22247 21.3827C7.52299 21.3315 8.03268 20.5407 9.61362 20.5407C11.1931 20.5407 11.6524 21.3827 13.029 21.3572C14.4567 21.3316 15.3491 20.0819 16.2158 18.8059C17.2102 17.3529 17.6174 15.9499 17.6428 15.8728C17.6174 15.8474 14.8901 14.8011 14.8649 11.6639C14.839 9.03697 17.0059 7.78759 17.108 7.71004C15.8843 5.90031 13.9723 5.69624 13.3094 5.64486ZM28.183 2.08964C31.8957 2.08964 34.481 4.64899 34.481 8.37484C34.481 12.1143 31.8424 14.6867 28.0899 14.6867H23.9792V21.2237H21.0093V2.08964H28.183ZM23.9792 12.1937H27.387C29.9728 12.1937 31.4444 10.8016 31.4444 8.38816C31.4444 5.97496 29.9728 4.59591 27.4004 4.59591H23.9792V12.1937ZM35.2568 17.2591C35.2568 14.8193 37.1266 13.321 40.4417 13.1353L44.2603 12.9099V11.836C44.2603 10.2844 43.2128 9.35641 41.4627 9.35641C39.805 9.35641 38.7706 10.1518 38.519 11.3983H35.814C35.973 8.87871 38.1211 7.02245 41.5687 7.02245C44.9499 7.02245 47.111 8.81254 47.111 11.6104V21.2239H44.3663V18.93H44.3003C43.4916 20.4813 41.7276 21.4624 39.8981 21.4624C37.1664 21.4624 35.2568 19.7652 35.2568 17.2591ZM44.2603 15.9996V14.899L40.8258 15.1111C39.1155 15.2306 38.1475 15.9862 38.1475 17.1796C38.1475 18.3994 39.1552 19.1951 40.6935 19.1951C42.6956 19.1951 44.2603 17.8161 44.2603 15.9996ZM49.7024 26.3555V24.0349C49.9142 24.0879 50.3914 24.0879 50.6304 24.0879C51.9561 24.0879 52.6723 23.531 53.1097 22.0991C53.1097 22.0724 53.3618 21.2504 53.3618 21.237L48.3233 7.27427H51.4258L54.9531 18.6251H55.006L58.5333 7.27427H61.5566L56.3317 21.953C55.1388 25.3344 53.7598 26.4217 50.8691 26.4217C50.6304 26.4217 49.9142 26.3952 49.7024 26.3555Z"
                    fill="black"
                  />
                </svg>
              </div>
            </div>
            <div>
              <div className="flex cursor-pointer items-center justify-center w-[115px] border-[1px] border-[#AFB5C1]  rounded-lg h-[50px]  ">
                <p className="text-black font-Avenir text-base font-medium leading-[116.6%]">
                  Other
                </p>
              </div>
            </div>

          </div>


        </div>
        <div className=" mt-2 flex mx-2 gap-4">
          <div className="cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="28"
              viewBox="0 0 40 28"
              fill="none"
            >
              <g clipPath="url(#clip0_231_8859)">
                <rect
                  x="-0.000976562"
                  y="0.507812"
                  width="40"
                  height="27"
                  rx="2"
                  fill="white"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M39.6134 20.5328V23.4668H35.9334L34.5734 21.8558L33.16 23.4668H22.7067V14.8898H19.2578L23.569 5.00781H27.7645L28.7778 7.24881V5.00781H33.9867L34.8578 7.35681L35.7111 5.00781H39.6134V6.4028H36.6L35.2311 10.0568L34.8667 11.0468L33.1156 6.4028H30.1023V13.6028L26.9734 6.4028H24.5467L21.4 13.6028H23.4623L24.0134 12.2168H27.4623L28.0134 13.6028H30.1023H31.9156V8.91381L31.9067 7.9688L32.2623 8.91381L33.9956 13.6028H35.72L37.4623 8.91381L37.8 7.97781V13.6028H39.6134V16.4198L37.6045 18.4628L39.6134 20.5328ZM24.1645 22.0718V14.8988H30.1023V16.4558H25.9778V17.7158H30.0223V19.2638H25.9778V20.5148H30.1023V22.0718H24.1645ZM39.0623 22.0718H36.7067L34.5556 19.7318L32.3956 22.0718H30.1023L33.4178 18.4988L30.1023 14.8988H32.4667L34.6 17.2298L36.7423 14.8988H39.0623L35.7378 18.4718L39.0623 22.0718Z"
                  fill="#016FD0"
                />
                <path
                  d="M25.7378 7.9418L25.3823 8.84183L24.6445 10.6598H26.8311L26.0934 8.84183L25.7378 7.9418Z"
                  fill="#016FD0"
                />
              </g>
              <rect
                x="0.499023"
                y="1.00781"
                width="39"
                height="26"
                rx="1.5"
                stroke="#C1E1C2"
              />
              <defs>
                <clipPath id="clip0_231_8859">
                  <rect
                    x="-0.000976562"
                    y="0.507812"
                    width="40"
                    height="27"
                    rx="2"
                    fill="white"
                  />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div className="cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="28"
              viewBox="0 0 40 28"
              fill="none"
            >
              <rect
                x="0.499023"
                y="1.00781"
                width="39"
                height="26"
                rx="1.5"
                fill="white"
              />
              <rect
                x="0.499023"
                y="1.00781"
                width="39"
                height="26"
                rx="1.5"
                stroke="#C1E1C2"
              />
              <path
                d="M24.7072 8.73828C22.4982 8.73828 20.5241 9.89756 20.5241 12.0394C20.5241 14.4957 24.0252 14.6654 24.0252 15.8994C24.0252 16.419 23.4371 16.8841 22.4327 16.8841C21.0072 16.8841 19.9418 16.2342 19.9418 16.2342L19.4859 18.3956C19.4859 18.3956 20.7132 18.9445 22.3427 18.9445C24.7579 18.9445 26.6583 17.7283 26.6583 15.5498C26.6583 12.9543 23.1426 12.7897 23.1426 11.6443C23.1426 11.2373 23.6254 10.7913 24.6269 10.7913C25.757 10.7913 26.679 11.264 26.679 11.264L27.1251 9.17647C27.1251 9.17647 26.1219 8.73828 24.7072 8.73828ZM4.49685 8.89583L4.44336 9.21093C4.44336 9.21093 5.37269 9.38314 6.20971 9.72666C7.28744 10.1206 7.36421 10.3499 7.54572 11.0621L9.52359 18.782H12.1749L16.2596 8.89583H13.6143L10.9897 15.6175L9.91868 9.9199C9.82046 9.26781 9.32295 8.89583 8.71396 8.89583H4.49685ZM17.3233 8.89583L15.2481 18.782H17.7706L19.8384 8.89583H17.3233ZM31.3921 8.89583C30.7838 8.89583 30.4615 9.22555 30.225 9.80174L26.5294 18.782H29.1747L29.6865 17.2853H32.9092L33.2204 18.782H35.5545L33.5182 8.89583H31.3921ZM31.7361 11.5668L32.5202 15.2766H30.4195L31.7361 11.5668Z"
                fill="#1434CB"
              />
            </svg>
          </div>
          <div className="cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="28"
              viewBox="0 0 40 28"
              fill="none"
            >
              <rect
                x="0.499023"
                y="1.00781"
                width="39"
                height="26"
                rx="1.5"
                fill="white"
              />
              <rect
                x="0.499023"
                y="1.00781"
                width="39"
                height="26"
                rx="1.5"
                stroke="#C1E1C2"
              />
              <path
                d="M16.5938 7.73438H23.4035V20.1232H16.5938V7.73438Z"
                fill="#FF5F00"
              />
              <path
                d="M17.0263 13.9287C17.0263 11.4115 18.1937 9.17887 19.988 7.73424C18.6693 6.68359 17.0047 6.04883 15.1888 6.04883C10.8868 6.04883 7.40625 9.57286 7.40625 13.9287C7.40625 18.2844 10.8868 21.8085 15.1888 21.8085C17.0047 21.8085 18.6693 21.1737 19.988 20.1231C18.1937 18.7003 17.0263 16.4458 17.0263 13.9287Z"
                fill="#EB001B"
              />
              <path
                d="M32.5915 13.9287C32.5915 18.2844 29.111 21.8085 24.809 21.8085C22.9931 21.8085 21.3285 21.1737 20.0098 20.1231C21.8257 18.6784 22.9715 16.4458 22.9715 13.9287C22.9715 11.4115 21.8041 9.17887 20.0098 7.73424C21.3285 6.68359 22.9931 6.04883 24.809 6.04883C29.111 6.04883 32.5915 9.59475 32.5915 13.9287Z"
                fill="#F79E1B"
              />
            </svg>
          </div>
          <div className="cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="28"
              viewBox="0 0 40 28"
              fill="none"
            >
              <g clipPath="url(#clip0_231_8868)">
                <rect
                  x="-0.000976562"
                  y="0.507812"
                  width="40"
                  height="27"
                  rx="2"
                  fill="white"
                />
                <path
                  d="M39.9987 18.5078C39.9987 18.5078 31.1032 24.7215 14.8135 27.5078H39.9987V18.5078Z"
                  fill="#F48120"
                />
                <path
                  d="M4.52249 10.9316H2.96191V16.4669H4.52249C5.3475 16.4669 5.9439 16.2657 6.47072 15.8329C7.09693 15.3096 7.46471 14.5246 7.46471 13.7094C7.45477 12.0689 6.25204 10.9316 4.52249 10.9316ZM5.77492 15.0982C5.43696 15.4001 5.00954 15.541 4.31375 15.541H4.02549V11.8777H4.31375C4.9996 11.8777 5.41708 11.9984 5.77492 12.3205C6.1427 12.6526 6.36138 13.1659 6.36138 13.6993C6.36138 14.2327 6.1427 14.7661 5.77492 15.0982Z"
                  fill="#231F20"
                />
                <path
                  d="M9.01572 10.9336H7.95215V16.4689H9.01572V10.9336Z"
                  fill="#231F20"
                />
                <path
                  d="M11.6199 13.0556C10.9838 12.8141 10.7949 12.6531 10.7949 12.3612C10.7949 12.0089 11.1329 11.7473 11.5901 11.7473C11.9082 11.7473 12.1766 11.8781 12.4449 12.2002L13.0016 11.4655C12.5443 11.0629 11.9976 10.8516 11.3913 10.8516C10.4271 10.8516 9.68164 11.5359 9.68164 12.4417C9.68164 13.2066 10.0295 13.5991 11.0235 13.9614C11.441 14.1124 11.6497 14.213 11.7591 14.2734C11.9678 14.4143 12.0772 14.6156 12.0772 14.847C12.0772 15.2999 11.7293 15.6321 11.2522 15.6321C10.7452 15.6321 10.3377 15.3704 10.0892 14.8974L9.40332 15.5717C9.89038 16.3064 10.4868 16.6284 11.2919 16.6284C12.3952 16.6284 13.1805 15.8837 13.1805 14.8068C13.2004 13.9111 12.8326 13.5085 11.6199 13.0556Z"
                  fill="#231F20"
                />
                <path
                  d="M13.5283 13.711C13.5283 15.3414 14.7907 16.5994 16.4109 16.5994C16.8681 16.5994 17.2657 16.5088 17.7429 16.2774V15.0093C17.3154 15.442 16.9377 15.6131 16.4606 15.6131C15.3871 15.6131 14.6217 14.8281 14.6217 13.7009C14.6217 12.6341 15.407 11.7988 16.4109 11.7988C16.9178 11.7988 17.3055 11.9799 17.7429 12.4228V11.1547C17.2757 10.9131 16.888 10.8125 16.4308 10.8125C14.8305 10.8125 13.5283 12.1007 13.5283 13.711Z"
                  fill="#231F20"
                />
                <path
                  d="M26.2111 14.6573L24.7499 10.9336H23.5869L25.9029 16.6098H26.4794L28.8352 10.9336H27.6822L26.2111 14.6573Z"
                  fill="#231F20"
                />
                <path
                  d="M29.3223 16.4689H32.344V15.5329H30.3858V14.0434H32.2744V13.1075H30.3858V11.8796H32.344V10.9336H29.3223V16.4689Z"
                  fill="#231F20"
                />
                <path
                  d="M36.5685 12.5718C36.5685 11.5352 35.8628 10.9414 34.6303 10.9414H33.0498V16.4767H34.1134V14.2525H34.2525L35.7237 16.4767H37.0357L35.3161 14.1418C36.1212 13.9707 36.5685 13.4172 36.5685 12.5718ZM34.4215 13.4876H34.1134V11.8069H34.4414C35.1074 11.8069 35.4652 12.0887 35.4652 12.6322C35.4652 13.1857 35.1074 13.4876 34.4215 13.4876Z"
                  fill="#231F20"
                />
                <path
                  d="M21.013 16.6691C22.6215 16.6691 23.9254 15.3489 23.9254 13.7203C23.9254 12.0917 22.6215 10.7715 21.013 10.7715C19.4045 10.7715 18.1006 12.0917 18.1006 13.7203C18.1006 15.3489 19.4045 16.6691 21.013 16.6691Z"
                  fill="#F48120"
                />
              </g>
              <rect
                x="0.499023"
                y="1.00781"
                width="39"
                height="26"
                rx="1.5"
                stroke="#C1E1C2"
              />
              <defs>
                <clipPath id="clip0_231_8868">
                  <rect
                    x="-0.000976562"
                    y="0.507812"
                    width="40"
                    height="27"
                    rx="2"
                    fill="white"
                  />
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>

        <div className=" mt-4 gap-8 md:flex mx-2">
          <div>
            <p className="text-black my-2 font-Avenir text-base font-medium leading-[116.6%]">
              Card number*
            </p>
            <input
              type="text"
              className="border-[1px] border-[#AFB5C1] rounded-lg w-full lg:w-[338px] h-[50px]"
            />
          </div>
          <div>
            <p className="text-black my-2 font-Avenir text-base font-medium leading-[116.6%]">
              Name on card*
            </p>
            <input
              type="text"
              className="border-[1px] border-[#AFB5C1] rounded-lg w-full lg:w-[338px] h-[50px]"
            />
          </div>
        </div>

        <div className=" mt-1 gap-8 md:flex mx-2">
          <div>
            <p className="text-black my-2 font-Avenir text-base font-medium leading-[116.6%]">
              Expiration date*
            </p>
            <input
              type="text"
              className="border-[1px] border-[#AFB5C1] rounded-lg w-full lg:w-[338px] h-[50px]"
            />
          </div>
          <div>
            <p className="text-black my-2 font-Avenir text-base font-medium leading-[116.6%]">
              CVV*
            </p>
            <input
              type="text"
              className="border-[1px] border-[#AFB5C1] rounded-lg w-full lg:w-[338px] h-[50px]"
            />
          </div>
        </div>

        <div className="mt-4 gap-8  mx-2">
          <div className="flex items-center p-3 w-full lg:w-[711px] h-auto md:h-[51px] rounded-[15px]  bg-[#58C0D0]/10">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="19"
                height="22"
                viewBox="0 0 19 22"
                fill="none"
              >
                <path
                  d="M17.6914 6.10181C17.6813 5.87549 17.6748 5.64902 17.6717 5.4225C17.6591 4.57153 16.9896 3.87708 16.1399 3.82962C13.5411 3.68445 11.5087 2.82002 9.79196 1.14672C9.50469 0.883387 9.07382 0.883387 8.78655 1.14672C7.06982 2.82002 5.0374 3.68445 2.43861 3.82962C1.58889 3.87708 0.919426 4.57153 0.906792 5.42245C0.903729 5.64899 0.897174 5.87548 0.887132 6.10181C0.791625 11.1167 0.648446 17.9898 8.98974 20.8984C9.02968 20.9123 9.07054 20.9234 9.11202 20.9316L9.11243 20.9317C9.22916 20.955 9.34935 20.955 9.46608 20.9317C9.50772 20.9234 9.54903 20.9122 9.58914 20.8982C17.9062 17.9896 17.7869 11.1405 17.6914 6.10181Z"
                  stroke="black"
                  stroke-miterlimit="10"
                />
                <path
                  d="M13.9608 10.9492C13.9608 13.5294 11.8692 15.6211 9.289 15.6211C6.70884 15.6211 4.61719 13.5294 4.61719 10.9492C4.61719 8.36903 6.70884 6.27738 9.289 6.27738C11.8692 6.27738 13.9608 8.36903 13.9608 10.9492Z"
                  stroke="black"
                  stroke-miterlimit="10"
                />
                <path
                  d="M11.2519 9.74613L8.35156 12.6465"
                  stroke="black"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                />
                <path
                  d="M7.24316 11.5381L8.35152 12.6465"
                  stroke="black"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                />
              </svg>
            </div>
            <p className="mx-2  text-black my-2 font-Avenir text-base font-medium leading-[116.6%]">
              This transaction is protected with a secure and encrypted payment
              system by BnByond.
            </p>
          </div>
        </div>

        <div className="w-full md:w-[150px] h-[50px]  my-6 pt-4" onClick={handleSubmit}>
          <ButtonFilled onClick={AddDebitCardInfo} text="Continue" />
        </div>
      </div>
    </div>
  );
};
