name: Frontend AWS Deploy Action

on:
  workflow_call:
    secrets:
      aws_access_key_id:
        required: true
      aws_secret_access_key:
        required: true
      react_app_map_key:
        required: true
      react_app_google_api:
        required: true
      react_app_secret_encryption_key:
        required: true
      react_app_standard_client_id:
        required: true
      react_app_google_analytic_id:
        required: true
      react_app_texteditor_tiny_key:
        required: true
      react_app_stripe_publishkey:
        required: true
    inputs:
      environment:
        required: true
        type: string
      ecr_repository:
        required: true
        type: string
      react_app_endpoint:
        required: true
        type: string
      react_app_base_url:
        required: true
        type: string
      react_app_image_endpoint:
        required: true
        type: string
      ecs_cluster:
        required: true
        type: string
      ecs_service:
        required: true
        type: string

env:
  AWS_REGION: us-east-1
  REACT_APP_LOCALTOKEN: Bnbyond
  REACT_APP_MAPKEY: ${{ secrets.react_app_map_key }}


jobs:
  publish:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.aws_access_key_id }}
          aws-secret-access-key: ${{ secrets.aws_secret_access_key }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build and push
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          docker build \
            --build-arg REACT_APP_ENDPOINT="${{ inputs.react_app_endpoint }}" \
            --build-arg REACT_APP_BASEURL="${{ inputs.react_app_base_url }}" \
            --build-arg REACT_APP_IMAGEENDPOINT="${{ inputs.react_app_image_endpoint }}" \
            --build-arg REACT_APP_LOCALTOKEN="${{ env.REACT_APP_LOCALTOKEN }}" \
            --build-arg REACT_APP_MAPKEY="${{ secrets.react_app_map_key }}" \
            --build-arg REACT_APP_GOOGLEAPI="${{ secrets.react_app_google_api }}" \
            --build-arg REACT_APP_SECRET_ENCRYPTION_KEY="${{ secrets.react_app_secret_encryption_key }}" \
            --build-arg REACT_APP_STANDARD_CLIENTID="${{ secrets.react_app_standard_client_id }}" \
            --build-arg REACT_APP_TEXTEDITORTINYKEY="${{ secrets.react_app_texteditor_tiny_key }}" \
            --build-arg REACT_APP_GOOGLE_ANALYTICS_MEASURMENT_ID="${{ secrets.react_app_google_analytic_id }}" \
            --build-arg REACT_APP_STRIPE_PUBLISHKEY="${{ secrets.react_app_stripe_publishkey }}" \
            -f deploy/frontend/Dockerfile \
            -t $ECR_REGISTRY/${{ inputs.ecr_repository }}:latest .
          docker push $ECR_REGISTRY/${{ inputs.ecr_repository }}:latest
      
      - name: Force ECS Update
        run: |
          aws ecs update-service \
            --cluster ${{ inputs.ecs_cluster }} \
            --service ${{ inputs.ecs_service }} \
            --force-new-deployment
