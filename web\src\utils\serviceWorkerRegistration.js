/**
 * Service Worker registration and management utilities
 */

const isLocalhost = Boolean(
  window.location.hostname === 'localhost' ||
  window.location.hostname === '[::1]' ||
  window.location.hostname.match(
    /^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/
  )
);

/**
 * Register service worker with enhanced error handling
 */
export function registerSW(config = {}) {
  if ('serviceWorker' in navigator) {
    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);
    if (publicUrl.origin !== window.location.origin) {
      return;
    }

    window.addEventListener('load', () => {
      const swUrl = `${process.env.PUBLIC_URL}/sw.js`;

      if (isLocalhost) {
        checkValidServiceWorker(swUrl, config);
        navigator.serviceWorker.ready.then(() => {
          console.log(
            'This web app is being served cache-first by a service worker.'
          );
        });
      } else {
        registerValidSW(swUrl, config);
      }
    });
  }
}

/**
 * Register valid service worker
 */
async function registerValidSW(swUrl, config) {
  try {
    const registration = await navigator.serviceWorker.register(swUrl);
    
    registration.onupdatefound = () => {
      const installingWorker = registration.installing;
      if (installingWorker == null) {
        return;
      }
      
      installingWorker.onstatechange = () => {
        if (installingWorker.state === 'installed') {
          if (navigator.serviceWorker.controller) {
            console.log(
              'New content is available and will be used when all tabs for this page are closed.'
            );
            
            if (config && config.onUpdate) {
              config.onUpdate(registration);
            }
          } else {
            console.log('Content is cached for offline use.');
            
            if (config && config.onSuccess) {
              config.onSuccess(registration);
            }
          }
        }
      };
    };

    // Listen for service worker messages
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'CACHE_UPDATED') {
        console.log('Cache updated:', event.data.payload);
        if (config && config.onCacheUpdate) {
          config.onCacheUpdate(event.data.payload);
        }
      }
    });

    // Check for updates periodically
    setInterval(() => {
      registration.update();
    }, 60000); // Check every minute

    return registration;
  } catch (error) {
    console.error('Error during service worker registration:', error);
    if (config && config.onError) {
      config.onError(error);
    }
  }
}

/**
 * Check if service worker is valid
 */
function checkValidServiceWorker(swUrl, config) {
  fetch(swUrl, {
    headers: { 'Service-Worker': 'script' },
  })
    .then((response) => {
      const contentType = response.headers.get('content-type');
      if (
        response.status === 404 ||
        (contentType != null && contentType.indexOf('javascript') === -1)
      ) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.unregister().then(() => {
            window.location.reload();
          });
        });
      } else {
        registerValidSW(swUrl, config);
      }
    })
    .catch(() => {
      console.log(
        'No internet connection found. App is running in offline mode.'
      );
    });
}

/**
 * Unregister service worker
 */
export function unregisterSW() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
      })
      .catch((error) => {
        console.error(error.message);
      });
  }
}

/**
 * Update service worker
 */
export async function updateSW() {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.update();
      console.log('Service worker updated');
      return true;
    } catch (error) {
      console.error('Failed to update service worker:', error);
      return false;
    }
  }
  return false;
}

/**
 * Get service worker status
 */
export async function getSWStatus() {
  if (!('serviceWorker' in navigator)) {
    return { supported: false };
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    return {
      supported: true,
      registered: true,
      active: !!registration.active,
      waiting: !!registration.waiting,
      installing: !!registration.installing,
      scope: registration.scope
    };
  } catch (error) {
    return {
      supported: true,
      registered: false,
      error: error.message
    };
  }
}

/**
 * Clear service worker caches
 */
export async function clearSWCaches() {
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('All caches cleared');
      return true;
    } catch (error) {
      console.error('Failed to clear caches:', error);
      return false;
    }
  }
  return false;
}

/**
 * Get cache storage usage
 */
export async function getCacheUsage() {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        quota: estimate.quota,
        usage: estimate.usage,
        usagePercentage: (estimate.usage / estimate.quota) * 100
      };
    } catch (error) {
      console.error('Failed to get storage estimate:', error);
      return null;
    }
  }
  return null;
}

/**
 * Preload critical resources using service worker
 */
export async function preloadCriticalResources(resources) {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.ready;
      if (registration.active) {
        registration.active.postMessage({
          type: 'PRELOAD_RESOURCES',
          payload: resources
        });
      }
    } catch (error) {
      console.error('Failed to preload resources:', error);
    }
  }
}

/**
 * Enable background sync
 */
export async function enableBackgroundSync(tag = 'background-sync') {
  if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.sync.register(tag);
      console.log('Background sync enabled');
      return true;
    } catch (error) {
      console.error('Failed to enable background sync:', error);
      return false;
    }
  }
  return false;
}

/**
 * Request persistent storage
 */
export async function requestPersistentStorage() {
  if ('storage' in navigator && 'persist' in navigator.storage) {
    try {
      const persistent = await navigator.storage.persist();
      console.log(`Persistent storage: ${persistent ? 'granted' : 'denied'}`);
      return persistent;
    } catch (error) {
      console.error('Failed to request persistent storage:', error);
      return false;
    }
  }
  return false;
}

/**
 * Service worker configuration for BnbYond
 */
export const swConfig = {
  onSuccess: (registration) => {
    console.log('BnbYond is now available offline!');
    // Show user notification about offline capability
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('BnbYond is ready for offline use!', {
        icon: '/assets/img/icon-192x192.png',
        badge: '/assets/img/badge-72x72.png'
      });
    }
  },
  
  onUpdate: (registration) => {
    console.log('New version of BnbYond is available!');
    // Show update notification to user
    const updateAvailable = new CustomEvent('sw-update-available', {
      detail: { registration }
    });
    window.dispatchEvent(updateAvailable);
  },
  
  onError: (error) => {
    console.error('Service worker registration failed:', error);
  },
  
  onCacheUpdate: (payload) => {
    console.log('Cache updated:', payload);
  }
};

export default {
  registerSW,
  unregisterSW,
  updateSW,
  getSWStatus,
  clearSWCaches,
  getCacheUsage,
  preloadCriticalResources,
  enableBackgroundSync,
  requestPersistentStorage,
  swConfig
};
