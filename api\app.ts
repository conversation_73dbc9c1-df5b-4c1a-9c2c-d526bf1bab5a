require("dotenv").config();
import express from 'express';
import path from 'path';
import * as Sentry from '@sentry/node';
import Stripe from 'stripe';

import { isSentryEnabled } from './instrument';
import router from './route';
import { User } from './model';
import { connect as connectDB } from './config/database';
import { errorMiddleware, notFound } from './middleware/errorHanlder';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Create the Express app
export const createApp = async () => {
  const app = express();
  
  // Connect to database if not in test mode or if explicitly requested
  await connectDB();
  
  app.use(express.json({ limit: "200mb" }));
  app.use(express.urlencoded({ extended: true }));

  // CORS middleware
  app.use((req, res, next): void => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header(
      "Access-Control-Allow-Headers",
      "Origin,X-Requested-With,Content-Type,Accept,Authorization,*"
    );
    if (req.method === "OPTIONS") {
      res.header("Access-Control-Allow-Methods", "PUT,POST,PATCH,DELETE,GET");
      res.status(200).json({});
      return;
    }
    next();
  });

  app.use(router);

  // Static file route
  app.get("/readfiles/:img", (req, res) => {
    try {
      res.sendFile(path.join(__dirname, "/images/", req.params.img));
    } catch (error) {
      console.error('Error serving image:', error);
      res.status(500).send('Error loading image');
    }
  });

  // Root route
  app.get("/", (req, res) => {
    res.send("Server is running good");
  });
  

  app.post("/webhook", express.raw({ type: "application/json" }), async (req, res): Promise<void> => {
    let event;
  
    try {
      const sig = req.headers["stripe-signature"];
      event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
    } catch (err) {
      console.error("Webhook signature verification failed:", err.message);
      res.status(400).send(`Webhook Error: ${err.message}`);
      return;
    }
  
    console.log(`Webhook event received: ${event.type}`);
  
    if (event.type === "customer.subscription.deleted") {
      try {
        const subscription = event.data.object;
        console.log(`Subscription canceled: ${subscription.id}`);
  
        const updatedUser = await User.updateOne(
          { subscriptionId: subscription.id },
          {
            $set: {
              subscription: false,
              subscriptionStatus: "canceled",
              subRole: null,
            },
          }
        );
  
        if (updatedUser.modifiedCount > 0) {
          console.log(`Subscription updated for user with subscriptionId: ${subscription.id}`);
        } else {
          console.warn(`No user found with subscriptionId: ${subscription.id}`);
        }
      } catch (err) {
        console.error("Error updating user subscription:", err);
      }
    }
  
    res.status(200).send("Webhook received");
  });

  // Add this to a route in your app to test Sentry
  app.get('/test-sentry', (req, res) => {
    throw new Error('Test Sentry Error');
  });

  app.use(notFound);

  // The error handler must be registered before any other error middleware and after all controllers
  if (isSentryEnabled()) {
    Sentry.setupExpressErrorHandler(app);
  }
  app.use(errorMiddleware);

  app.on('error', (error) => {
    console.error('Express App Error:', error);
  });

  return app;
};

export const getApp = async () => {
  return await createApp();
};
