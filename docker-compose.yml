services:
  frontend:
    build:
      context: .
      dockerfile: deploy/frontend/Dockerfile
      args:
        - REACT_APP_ENDPOINT=${REACT_APP_ENDPOINT}
        - REACT_APP_BASEURL=${REACT_APP_BASEURL}
        - REACT_APP_IMAGEENDPOINT=${REACT_APP_IMAGEENDPOINT}
        - REACT_APP_LOCALTOKEN=${REACT_APP_LOCALTOKEN}
        - REACT_APP_MAPKEY=${REACT_APP_MAPKEY}
        - REACT_APP_GOOGLEAPI=${REACT_APP_GOOGLEAPI}
        - REACT_APP_SECRET_ENCRYPTION_KEY=${REACT_APP_SECRET_ENCRYPTION_KEY}
        - REACT_APP_STANDARD_CLIENTID=${REACT_APP_STANDARD_CLIENTID}
        - REACT_APP_GOOGLE_ANALYTICS_MEASURMENT_ID=${REACT_APP_GOOGLE_ANALYTICS_MEASURMENT_ID}
        - REACT_APP_TEXTEDITORTINYKEY=${REACT_APP_TEXTEDITORTINYKEY}
        - REACT_APP_STRIPE_PUBLISHKEY=${REACT_APP_STRIPE_PUBLISHKEY}
    env_file:
      - ./src/.env
    ports:
      - "3000:80"
    depends_on:
      - backend

  backend:
    build:
      context: .
      dockerfile: deploy/backend/Dockerfile
    ports:
      - "5000:5000"
    env_file:
      - ./api/.env
    volumes:
      - ./api:/app
    depends_on:
      - mongodb

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=bnbyond

volumes:
  mongodb_data: