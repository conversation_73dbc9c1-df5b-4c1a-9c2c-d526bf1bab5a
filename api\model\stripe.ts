import mongoose from "mongoose";

const orderChargeSchema = new mongoose.Schema({
  charge: {
    type: String
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'user',
  },
  superUserId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'superUser',
  },
  chargeFor:{
    type:String,
    default:'orderCharge'
  }
}, { timestamps:true });

const userHardareSchema = new mongoose.Schema({
  charge: {
    type: String
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'user',
  },
  superUserId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'superUser',
  },
  chargeFor:{
    type:String,
    default:'HardwareCharge'
  },
  plan:{
    type:String
  }
}, { timestamps:true });

const AppChargeSchema = new mongoose.Schema({
  charge: {
    type: String
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'user',
  },
  superUserId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'superUser',
  },
  chargeFor:{
    type:String,
    default:'appCharge'
  },
  plan:{
    type:String
  }
}, { timestamps:true });

export const OrderCharge = mongoose.model("charge", orderChargeSchema);
export const AppCharge = mongoose.model("chargeCustomer", AppChargeSchema);
export const UserHardware = mongoose.model("chargeHardwate", userHardareSchema);
