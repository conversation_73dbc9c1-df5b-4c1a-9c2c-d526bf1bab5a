import React from 'react';

const PopupCards = ({ isOpen = true, onClose }) => {
  const cards = [
    {
      id: 1,
      title: "BECOME A MEMBER",
      subtitle: "Complete your profile",
      description: "on beta.Bnbyond.com",
      content:
        "Fill out your profile and list your STR. Upload photos and fill out your availability calendar.",
      bgColor: "bg-gradient-to-br from-amber-400 to-orange-500",
      textColor: "text-amber-900",
      subtitleColor: "text-amber-800",
      contentColor: "text-amber-900",
    },
    {
      id: 2,
      title: "SITE GOES LIVE",
      subtitle: "You'll be notified",
      description: "",
      content:
        "As a preferred Member, you will be the first to learn when properties are available for booking on Bnbyond.com\n",
      bgColor: "bg-gradient-to-br from-cyan-400 to-teal-500",
      textColor: "text-white",
      subtitleColor: "text-cyan-100",
      contentColor: "text-cyan-50",
    },
    {
      id: 3,
      title: "START YOUR TRAVELS!",
      subtitle: "That's it! You're good to go!",
      description: "",
      content:
        "Your Membership fee will be collected only after you make or take your first booking. 5% service fee applies only to the Guest, not the Host. Go make the world your happy place!",
      bgColor: "bg-gradient-to-br from-emerald-300 to-teal-400",
      textColor: "text-emerald-900",
      subtitleColor: "text-emerald-800",
      contentColor: "text-emerald-800",
    },
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center overflow-auto px-4 py-8">
      <div className="relative max-w-7xl w-full mx-auto">
        {/* Close Button */}
        {onClose && (
          <button
            onClick={onClose}
            className="absolute top-[0.5rem] right-[0.5rem] bg-white rounded-full w-10 h-10 flex items-center justify-center text-gray-600 hover:text-gray-800 shadow-lg z-10"
          >
            ×
          </button>
        )}

        {/* Card Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {cards.map((card) => (
            <div
              key={card.id}
              className={`${card.bgColor} rounded-3xl p-10 shadow-2xl relative min-h-[400px] flex items-center justify-center text-center overflow-hidden`}
            >
              {/* Background Number */}
              <div className="absolute inset-0 flex justify-center items-center pointer-events-none">
                <span className="text-[12rem] font-extrabold opacity-10 select-none">
                  {card.id}
                </span>
              </div>

              {/* Decorative Blobs */}
              <div className="absolute top-4 right-4 w-32 h-32 rounded-full bg-white opacity-10 blur-xl"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 rounded-full bg-white opacity-10 blur-xl"></div>

              {/* Card Content */}
              <div className="relative z-10 max-w-xs mx-auto">
                <h1 className={`text-3xl font-bold ${card.textColor} mb-4`}>
                  {card.title}
                </h1>
                <h2 className={`text-lg font-semibold ${card.subtitleColor} mb-2`}>
                  {card.subtitle}
                </h2>
                {card.description && (
                  <p className={`text-sm ${card.subtitleColor} mb-4`}>
                    {card.description}
                  </p>
                )}
                <p className={`${card.contentColor} whitespace-pre-line text-base leading-relaxed`}>
                  {card.content}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Get Started Button */}
        <div className="mt-10 text-center">
          <button
            onClick={onClose}
            className="bg-white text-gray-800 font-semibold py-3 px-10 rounded-full shadow-lg transition-transform duration-200 hover:scale-105 hover:bg-gray-100"
          >
            Get Started
          </button>
        </div>
      </div>
    </div>
  );
};

export default PopupCards;
