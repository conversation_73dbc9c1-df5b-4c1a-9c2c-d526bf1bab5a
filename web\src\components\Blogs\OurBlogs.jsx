import { CircularProgress, Container } from "@mui/material";
import React, { useState, useEffect } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Link, useNavigate } from "react-router-dom";
import userServices from "services/httpService/userAuth/userServices";

const OurBlogs = () => {
  const [slider, setSlider] = useState(null);
  const [allBlog, setAllBlog] = React.useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const getBlog = async () => {
    try {
      let res = await userServices.commonGetService(`post/blogs`);
      setIsLoading(false);
      setAllBlog(res.data.data);
    } catch (error) {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    if (allBlog.length === 0) {
      getBlog();
    }
  }, []);


  const settings = {
    autoplay: true,
    infinite: allBlog.length > 4, // Only enable infinite scrolling if more than 4 blogs exist
    speed: 500,
    slidesToShow: Math.min(4, allBlog.length), // Prevent excess slides
    slidesToScroll: 1,
    arrows: false,
    responsive: [
      { breakpoint: 1024, settings: { slidesToShow: Math.min(3, allBlog.length) } },
      { breakpoint: 768, settings: { slidesToShow: Math.min(2, allBlog.length) } },
      { breakpoint: 640, settings: { slidesToShow: 1 } },
    ],
  };


  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const options = { month: "long", day: "numeric", year: "numeric" };
    return date.toLocaleDateString("en-US", options);
  };

  // Empty state component
  const EmptyBlogsState = () => (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <div className="text-[#AFB5C1] mb-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="60"
          height="60"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
      </div>
      <h3 className="text-[#000000] text-xl font-medium">No Blogs Available Yet</h3>
      <p className="text-[#AEAFB0] text-center max-w-md">
        We're working on creating amazing content for you. Check back soon for the latest blogs!
      </p>
    </div>
  );

  return (
    <>
      <section className=" flex items-center justify-center pt-3  mx-4 md:mx-10 lg:mx-0 ">
        <div className=" w-full lg:w-4/5">
          <div>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-[#000000] md:text-3xl text-2xl font-medium">
                  Our Blogs
                </h4>
              </div>
              <div className="flex items-center gap-2 cursor-pointer">
                <div
                  onClick={() => slider && slider.slickPrev()}
                  className="md:w-[46px] w-[36px] flex items-center justify-center md:h-[46px] h-[36px] border-2 border-[#AFB5C1] rounded-full text-[#AFB5C1]"
                >
                  <i className="fa-solid fa-angle-left text-xl"></i>
                </div>
                <div
                  onClick={() => slider && slider.slickNext()}
                  className="md:w-[46px] w-[36px] flex items-center justify-center md:h-[46px] h-[36px] border-2 border-[#AFB5C1] rounded-full text-[#AFB5C1]"
                >
                  <i className="fa-solid fa-angle-right text-xl"></i>
                </div>
              </div>
            </div>
            <div>
              {isLoading ? (
                <div className="flex justify-center items-center h-52">
                  <CircularProgress />
                </div>
              ) : allBlog?.length === 0 ? (
                <EmptyBlogsState />
              ) : (
                <Slider ref={(c) => setSlider(c)} {...settings}>
                  {allBlog?.length > 0 &&
                    allBlog.map((item, index) => {
                      return (
                        <div className="relative" key={index}>
                          <div
                            className="w-full xl:w-[270px] mt-5 h-[200px] bg-center bg-cover relative overflow-hidden cursor-pointer"
                            style={{
                              backgroundImage: `url(${item?.pic})`,
                            }}
                            onClick={() => navigate(`/blog/${item?._id}`)}
                          >
                            <div className="absolute inset-0 transition-opacity bg-[#58C0D0] opacity-0 overlay hover:opacity-90"></div>
                          </div>
                          <div className="w-full  xl:w-[270px] py-3 h-[160px] ">
                            <div className="">
                              <div className="flex items-center gap-2 text-[#AEAFB0]">
                                <i className="fa-solid fa-calendar-days"></i>
                                <span>{formatDate(item?.timeStamp)}</span>
                              </div>
                              <div className="w-[292px]">
                                <h4
                                  className="text-[#000000] hover:text-[#58C0D0] text-xl font-medium cursor-pointer"
                                  onClick={() => navigate(`/blog/${item?._id}`)}
                                >
                                  {item?.title?.length > 20
                                    ? item?.title.substring(0, 20) + "..."
                                    : item?.title}
                                </h4>
                              </div>
                              <div className="pt-2">
                                <p
                                  className="overflow-hidden font-normal text-md line-clamp-3"
                                  dangerouslySetInnerHTML={{
                                    __html:
                                      item?.description?.length > 50
                                        ? `${item?.description.substring(
                                          0,
                                          60
                                        )}...`
                                        : item?.description,
                                  }}
                                ></p>
                              </div>
                              <div className="text-[#C0392D]">
                                <span
                                  className="text-base font-medium cursor-pointer"
                                  onClick={() => navigate(`/blog/${item._id}`)}
                                >
                                  READ MORE
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </Slider>
              )}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default OurBlogs;