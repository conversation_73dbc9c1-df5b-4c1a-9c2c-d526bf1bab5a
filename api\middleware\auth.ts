import * as jwt from "jsonwebtoken";

const config = process.env;

// Function to verify and decode JWT token
const verifyTokenAndGetUser = (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { error: "A valid token is required for authentication" };
  }

  try {
    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, config.TOKEN_KEY);
    return { decoded };
  } catch (err) {
    console.error("JWT Verification Error:", err);
    return { error: "Invalid Token" };
  }
};

// Middleware to verify token for user-side access
const verifyToken = (req, res, next) => {
  const { error, decoded } = verifyTokenAndGetUser(req.headers.authorization);

  if (error) {
    return res.status(403).json({ message: error });
  }

  req.user = decoded;
  next();
};

// Middleware to verify token for admin-side access
const adminAuthMiddleware = (req, res, next) => {
  const { error, decoded } = verifyTokenAndGetUser(req.headers.authorization);

  if (error) {
    return res.status(403).json({ message: error });
  }

  req.user = decoded;
  if (req.user.isAdmin || req.user.roles.some(role => ['subadmin', 'Blogger', 'Financial'].includes(role))) {
    return next();
  }

  return res.status(403).json({
    message: "Forbidden. You don't have the necessary permissions to access this resource."
  });
};

// Middleware to check if the user has the required roles
const roleCheckMiddleware = (allowedRoles = []) => {
  return (req, res, next) => {
    const user = req.user;

    if (user.isAdmin || user.roles.some(role => allowedRoles.includes(role))) {
      return next();
    }

    return res.status(403).json({
      message: "Forbidden. You don't have the necessary permissions to access this resource."
    });
  };
};

export { verifyToken, roleCheckMiddleware, adminAuthMiddleware };
