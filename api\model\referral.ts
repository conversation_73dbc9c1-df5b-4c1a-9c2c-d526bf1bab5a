import mongoose from "mongoose";

const referralSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Users', // Reference to the User model
    // required: true,
  },
  referredUser: {
    type:  [String],
    ref: 'Users', // Reference to the User model
  },
  referralCode: {
    type: String,
    unique: true,
  },
  referralSource: {
    type: String, 
  },
  points: {
    type: Boolean, // Set the type to Boolean
    default: false,
  },
  isActive: {
    type: Boolean, // Set the type to Boolean
    default: true,
  },
});

export const Referral = mongoose.model('Referral', referralSchema);
