import React, { useState, useEffect } from 'react';
import visaCard from "assets/img/cardTypes.jpg";
import { PaymentCard } from 'components/Cards/PaymentCard';
import { ButtonOutlined } from 'common/buttons/buttonOutlined';
import { useMutation } from "react-query";
import { toast } from 'react-toastify';
import userServices from "services/httpService/userAuth/userServices";
import { localStorageData } from "services/auth/localStorageData";
import {
  CardNumberElement,
  CardExpiryElement,
  CardCvcElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

export const Payments = () => {
  const [showAddCard, setShowAddCard] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingRemove, setLoadingRemove] = useState({});
  const [cardName, setCardName] = useState('');
  const [savedCards, setSavedCards] = useState([]);

  const stripe = useStripe();
  const elements = useElements();

  const { mutate: fetchCards } = useMutation(
    () => userServices.userById(`/userAuth/user/${localStorageData("_id")}`),
    {
      onError: (error) => toast.error(error.message),
      onSuccess: (data) => {
        setSavedCards(data?.data?.cardInfo?.slice(0, 2) || []);
      },
    }
  );

  const { mutate: addOrUpdateCard } = useMutation(
    (cardData) => userServices.Reservation('/stripe/AddCardInfo', cardData),
    {
      onError: (error) => {
        if(error.response.status===400){
         return  toast.error(error.response.data.error);
        }
        toast.error("Failed to add card:");
        setLoading(false);
      },
      onSuccess: () => {
        toast.success('Your card was added successfully!');
        setShowAddCard(false);
        fetchCards();
      },
    }
  );

  const { mutate: removeCard } = useMutation(
    (cardId) => {
      setLoadingRemove((prev) => ({ ...prev, [cardId]: true }));
      return userServices.Reservation('/stripe/removeCard', { cardId, userId: localStorageData("_id") });
    },
    {
      onError: (error, cardId) => {
        toast.error("Failed to remove card: " + error.message);
        setLoadingRemove((prev) => ({ ...prev, [cardId]: false }));
      },
      onSuccess: (_, cardId) => {
        toast.success('Card removed successfully!');
        setLoadingRemove((prev) => ({ ...prev, [cardId]: false }));
        fetchCards();
      },
    }
  );

  const handleAddCard = async () => {
    setLoading(true);
    if (!stripe || !elements) {
      toast.error("Stripe has not loaded properly.");
      setLoading(false);
      return;
    }

    if (!cardName.trim()) {
      toast.error("Please enter the name on the card.");
      setLoading(false);
      return;
    }

    const cardNumberElement = elements.getElement(CardNumberElement);
    const cardExpiryElement = elements.getElement(CardExpiryElement);
    const cardCvcElement = elements.getElement(CardCvcElement);

    if (!cardNumberElement || !cardExpiryElement || !cardCvcElement) {
      toast.error("Invalid card details.");
      setLoading(false);
      return;
    }

    const { token, error } = await stripe.createToken(cardNumberElement);

    if (error) {
      toast.error(`Card error: ${error.message}`);
      setLoading(false);
      return;
    }

    await addOrUpdateCard({ stripeToken: token.id, userId: localStorageData("_id"), cardName });
    setLoading(false);
  };

  useEffect(() => {
    fetchCards();
  }, []);

  return (
    <div className="p-5 md:p-10 flex flex-col gap-5">
      <div className="pb-4">
        <p className="text-2xl font-medium">Manage Payment</p>
        <p className="text-base font-normal text-[#4B4B4B]">
          You can manage your credit cards from here. Adding cards makes checkout easier.
        </p>
      </div>

      <div className="flex gap-4">
        {savedCards.length > 0 ? (
          savedCards.map((card, index) => (
            <div key={index} className="relative w-[600px]">
              <PaymentCard
                name={card.cardName || "Unknown Card"}
                icon={visaCard}
                number={card.cardNumber || "**** XXXX"}
                verification={index === 0 ? "Primary" : "Secondary"}
                cardType={card.cardType || "Unknown"}
                funding={card.funding || "N/A"}
                country={card.country || "N/A"}
              />
              <button
                className={`w-full mt-3 px-4 py-2 rounded-lg text-sm font-medium transition-all bg-red-500 text-black hover:bg-red-600`}
                onClick={(e) => {
                  removeCard(card._id)
                }}
              >
                {loadingRemove[card._id] ? "Removing..." : "Remove Card"}
              </button>
            </div>
          ))
        ) : (
          <p className="text-gray-500">No cards saved. Add a card to manage payments.</p>
        )}
      </div>

      <div className="w-full">
        {!showAddCard && savedCards.length < 2 && (
          <ButtonOutlined text="Add Card" style={{ width: "300px" }} onClick={() => setShowAddCard(true)} />
        )}

        {showAddCard && (
          <div className="p-4 border rounded-lg border-[#C1E1C2] bg-white shadow-sm">
            <div className="flex justify-between items-center">
              <h4 className="text-lg font-semibold">Credit/Debit Card</h4>
              <button
                className="text-gray-500 hover:text-gray-700 transition"
                onClick={() => setShowAddCard(false)}
              >
                <i className="fa-solid fa-x text-lg"></i>
              </button>
            </div>

            <hr className="my-3 border-[#C1E1C2]" />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-700">Card Number*</label>
                <CardNumberElement className="input-styl placeholder:text-gray-400" />
              </div>
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-700">Name on Card*</label>
                <input
                  type="text"
                  name="cardName"
                  placeholder="Name on card"
                  className="input-styl placeholder:text-gray-400"
                  value={cardName.toUpperCase()}
                  onChange={(e) => setCardName(e.target.value)}
                />
              </div>
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-700">Expiration Date*</label>
                <CardExpiryElement className="input-styl placeholder:text-gray-400" />
              </div>
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-700">CVV / CVC*</label>
                <CardCvcElement className="input-styl placeholder:text-gray-400" />
              </div>
            </div>

            <button className="btn-styl-blue mt-4" onClick={handleAddCard} disabled={loading}>
              {loading ? 'Adding Card...' : 'Add Card'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
