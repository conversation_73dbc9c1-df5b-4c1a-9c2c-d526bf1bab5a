import { Router } from "express";

import { awsupload } from "../middleware/upload";
import checkRoleMiddleware from "../middleware/routeMiddleware";
import { Controller } from "../controllers/property";

// Create router instance
const router = Router();

// Define middleware for common use
const authMiddleware = checkRoleMiddleware(['subadmin', 'Blogger', 'Financial']);
const uploadMiddleware = awsupload.array('pics');

// Property CRUD operations
router.post("/uploadProperty", authMiddleware, uploadMiddleware, Controller.uploadProperty);
router.put("/updatePropertyById/:_id", authMiddleware, uploadMiddleware, Controller.updatePropertyById);
router.delete("/deletePropertyById/:_id", authMiddleware, Controller.deletePropertyById);
router.put("/updatePropertystatus/:_id", authMiddleware, uploadMiddleware, Controller.updatePropertyStatus);

// Property retrieval routes
router.get("/getAllPropertyAvailable", Controller.getPropertyOnlyAvailable);
router.get("/getAllProperty", Controller.getAllProperty);
router.get("/getPropertyByUserId/:userId", authMiddleware, Controller.getPropertyByUserId);
router.get("/getPropertyByPropertyId/:Id", authMiddleware, Controller.getPropertyByPropertyId);

// Special operations
router.put("/addReviewToProperty", authMiddleware, Controller.addReviewToProperty);
router.put("/updatePropertyforFavourite", authMiddleware, Controller.addToFavourite);

// Analytics route - this was a duplicate route, adding a specific path
router.get("/countProperties", authMiddleware, Controller.countProperties);

// Calendar and synchronization
router.get("/calendar/v1/:propertyId.ics", Controller.iClaFuntion);
router.post("/calendar/v1/sync", Controller.fetchBookedDatesFromOtherWebsite);

router.post("/calendar/:id", Controller.updatePropertyDates);

export default router;
