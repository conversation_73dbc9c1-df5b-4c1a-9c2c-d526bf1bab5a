import React, { useState } from 'react'
import ReportList from 'pages/admin/Reviews/ReportList'
import ReviewsList from 'pages/admin/Reviews/ReviewsList'
import AdminPropertyDetail from 'pages/admin/PropertyManagement/AdminPropertyDetail';

const ReviewsSteps = ({ active, All, Report, setSteps }) => {

   const [propertyDetail, setPropertyDetail] = useState(null);

  const handleSetStepsAndDetail = (_id) => {
    const detail = All.find(property => property._id === _id);
    setSteps('AdminPropertyDetail');
    setPropertyDetail(detail);
  };

  return (
    <>
      {active === 1 && <ReviewsList allProperty={All} setSteps={propertyDetail} setStepsAndDetail={handleSetStepsAndDetail} />}
      {active === 2 && <ReportList Report={Report} />}
      {active === "AdminPropertyDetail" &&
        <AdminPropertyDetail propertyDetail={propertyDetail}  />
      }
    </>
  )
}

export default ReviewsSteps