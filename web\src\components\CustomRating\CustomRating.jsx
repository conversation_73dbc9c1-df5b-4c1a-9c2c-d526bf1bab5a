import React, { useState } from 'react';

/**
 * Lightweight custom rating component
 */
const CustomRating = ({ 
  value = 0, 
  onChange, 
  max = 5, 
  size = 20, 
  color = '#ffc107',
  emptyColor = '#e4e5e9',
  readonly = false,
  className = ''
}) => {
  const [hoverValue, setHoverValue] = useState(0);

  const handleClick = (rating) => {
    if (!readonly && onChange) {
      onChange(rating);
    }
  };

  const handleMouseEnter = (rating) => {
    if (!readonly) {
      setHoverValue(rating);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverValue(0);
    }
  };

  const renderStar = (index) => {
    const rating = index + 1;
    const filled = (hoverValue || value) >= rating;

    return (
      <span
        key={index}
        className={`inline-block cursor-${readonly ? 'default' : 'pointer'} ${className}`}
        style={{
          fontSize: size,
          color: filled ? color : emptyColor,
          transition: 'color 0.2s ease'
        }}
        onClick={() => handleClick(rating)}
        onMouseEnter={() => handleMouseEnter(rating)}
        onMouseLeave={handleMouseLeave}
      >
        ★
      </span>
    );
  };

  return (
    <div className="inline-flex items-center">
      {Array.from({ length: max }, (_, index) => renderStar(index))}
    </div>
  );
};

export default CustomRating;