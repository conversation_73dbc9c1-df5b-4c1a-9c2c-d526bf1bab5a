import { Router } from "express";
import checkRoleMiddleware from "../middleware/routeMiddleware";
import { Controller as ReservationController } from "../controllers/reservation";

const router = Router();

// Common middleware for protected routes
const reservationAccessControl = checkRoleMiddleware(['subadmin', 'Blogger', 'Financial']);

// Reservation creation and management
router.post("/reservation", reservationAccessControl, ReservationController.createReservation);
router.put("/reservation/:_id", reservationAccessControl, ReservationController.updateReservation);
router.delete("/reservation/:_id", reservationAccessControl, ReservationController.deleteReservation);
// admin routes
router.get("/reservations/:id", reservationAccessControl, ReservationController.getAllReservations);

// Reservation retrieval routes
router.get("/reservation/:_id", reservationAccessControl, ReservationController.getReservationById);
router.get("/reservationbyUserId/:_id", reservationAccessControl, ReservationController.getReservationsByUserId);
router.get("/getreservationbytoday",reservationAccessControl, ReservationController.reservationToday);

// Host and guest relationship routes
router.get("/hosts/:userId", reservationAccessControl, ReservationController.gethostByGuestId);
router.get("/guests/:userId", reservationAccessControl, ReservationController.getGuestByHostId);

// Refund processing routes
router.put("/refundReservation/:_id", reservationAccessControl, ReservationController.refundReservation);
router.put("/refundApproval/:_id", reservationAccessControl, ReservationController.refundApproval);

export default router;