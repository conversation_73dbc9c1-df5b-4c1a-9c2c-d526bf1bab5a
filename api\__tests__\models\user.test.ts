import { describe, it, expect } from '@jest/globals';
import { User } from '../../model/user.js'; 

describe('User Model Test', () => {
  it('should create & save a user successfully', async () => {
    // This is a placeholder test. Replace with your actual model tests
    // when you implement them
    const mockUser = {
      fname: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    };
    
    // Simulate creating a user
    const savedUser = await User.create(mockUser);
    expect(savedUser._id).toBeDefined();
    expect(savedUser.fname).toBe(mockUser.fname);
  });
});