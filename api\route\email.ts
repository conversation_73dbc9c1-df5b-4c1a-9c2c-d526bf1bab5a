import { Router } from "express";
import { Controller as EmailController } from "../controllers/email";

const router = Router();

// Authentication-related email routes
router.post("/applyforgetpass", EmailController.applyForgotPassword);
router.get("/verify/:email/uniqueid/:uniqueId", EmailController.verifyEmail);

// Communication email routes
router.post("/send-transaction-details", EmailController.sendTransactionDetails);
router.post("/contact-host", EmailController.contactHost);

export default router;
