import bcrypt from 'bcrypt';
import crypto from 'crypto';
import { Request, Response } from 'express';
import { MulterS3Request } from '../middleware/upload';
import { Employee } from '../model';
import { sendEmail } from '../services/mailJetEmail';

const addEmploye = async (req: MulterS3Request, res: Response): Promise<void> => {
  const { emplname, jobTitle, email, phone, gender, password, registeredByAdmin, role } = req.body;
  const pic = req.file.location;  // Image file if uploaded (e.g., profile picture)
  if (!emplname || !jobTitle || !email || !phone || !gender) {
    res.status(400).json({ message: 'All fields are required' });
    return;
  }
  let hashedPassword;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 10);
  }

  const validRoles = ["Financial", "Blogger", "subadmin"];

  // this one is for json array
  const parsedRoles = Array.isArray(JSON.parse(role)) ? JSON.parse(role) : [];
  const validatedRoles = parsedRoles.filter(r => validRoles.includes(r));
  // If there are validated roles, use them; otherwise, return an empty array
  const finalRoles = validatedRoles.length > 0 ? validatedRoles : [];
  try {
    // Check if the email already exists in the database
    const existingEmployee = await Employee.findOne({ email });
    if (existingEmployee) {
      res.status(409).json({ message: 'Email already exists' });
      return;
    }
    const newEmployee = new Employee({
      emplname,
      jobTitle,
      email,
      phone,
      gender,
      password: hashedPassword,
      role: finalRoles, 
      registeredByAdmin,
      pic: pic ? pic : 'https://bnbpictures.s3.amazonaws.com/emptyProfilepic.jpeg',
    });
    await newEmployee.save();
    res.status(201).json({ message: 'Employee created successfully', employee: newEmployee });
  } catch (error) {
    console.error(error); // Log the error for debugging purposes
    if (error.code === 11000) {
      res.status(400).json({ message: 'Email already exists' });
      return;
    }
    res.status(500).json({ message: 'Server error', error });
  }
};

const getEmploye = async (req: Request, res: Response): Promise<void> => {
  try {
    const employees = await Employee.find();
    res.status(200).json(employees);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
}

const generateTempPassword = (): string => {
  return crypto.randomBytes(8).toString('hex'); // Generates a 16-character hexadecimal password
};

const assignRole = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;
  const { role, status } = req.body;

  // Validate the role
  const validRoles = ["Financial", "Blogger", "subadmin"];
  const validatedRoles = Array.isArray(role) ? role.filter(r => validRoles.includes(r)) : [];
  const finalRoles = validatedRoles.length > 0 ? validatedRoles : ["Employee"];

  // Validate the status (must be boolean)
  const validatedStatus = typeof status === 'boolean' ? status : undefined;

  if (validatedStatus === undefined) {
    res.status(400).json({ message: 'Invalid status. It must be a boolean.' });
    return;
  }

  try {
    // Find the employee by ID
    const employee = await Employee.findById(id);

    if (!employee) {
      res.status(404).json({ message: 'Employee not found' });
      return;
    }

    // Generate a temporary password
    const tempPassword = generateTempPassword();

    // Hash the temporary password using bcrypt
    const hashedPassword = await bcrypt.hash(tempPassword, 10);

    // Update the employee with the hashed password, role, and status
    const updatedEmployee = await Employee.findByIdAndUpdate(
      id,
      { password: hashedPassword, role: finalRoles, status: validatedStatus },
      { new: true }
    );

    if (!updatedEmployee) {
      res.status(404).json({ message: 'Employee not found after update' });
      return;
    }

    // Prepare the email parameters to send to the employee
    const emailParameters = {
      name: updatedEmployee.emplname,
      password: tempPassword, // Send the temporary password to the employee
      email: updatedEmployee.email,
      role: updatedEmployee.role,
      companyName: "BnByond.com",
      uniquelink: process.env.frontendLink + "/admin/login/",
    };

    // Send the email to the employee with the temporary password
    await sendEmail(
      updatedEmployee.email,
      "Welcome to BnByond",
      emailParameters,
      "send_employe_detail"
    );

    // Return success response with updated employee details
    res.status(200).json({
      message: 'Employee updated successfully and email sent with temporary password',
      employee: updatedEmployee,
    });

  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error', error });
  }
}

export const Controller = {
  addEmploye,
  getEmploye,
  assignRole,
};
