# Step 1: Use Node.js 20.11.0 image to build the React app
FROM node:18 AS nodework

# Set the working directory in the container
WORKDIR /app

# Add build arguments for React environment variables
ARG REACT_APP_ENDPOINT
ARG REACT_APP_BASEURL
ARG REACT_APP_IMAGEENDPOINT
ARG REACT_APP_LOCALTOKEN
ARG REACT_APP_MAPKEY
ARG REACT_APP_GOOGLEAPI
ARG REACT_APP_SECRET_ENCRYPTION_KEY
ARG REACT_APP_STANDARD_CLIENTID
ARG REACT_APP_TEXTEDITORTINYKEY
ARG REACT_APP_GOOGLE_ANALYTICS_MEASURMENT_ID
ARG REACT_APP_STRIPE_PUBLISHKEY

# Set environment variables from build args
ENV REACT_APP_ENDPOINT=$REACT_APP_ENDPOINT \
    REACT_APP_BASEURL=$REACT_APP_BASEURL \
    REACT_APP_IMAGEENDPOINT=$REACT_APP_IMAGEENDPOINT \
    REACT_APP_LOCALTOKEN=$REACT_APP_LOCALTOKEN \
    REACT_APP_MAPKEY=$REACT_APP_MAPKEY \
    REACT_APP_GOOGLEAPI=$REACT_APP_GOOGLEAPI \
    REACT_APP_SECRET_ENCRYPTION_KEY=$REACT_APP_SECRET_ENCRYPTION_KEY \
    REACT_APP_STANDARD_CLIENTID=$REACT_APP_STANDARD_CLIENTID \
    REACT_APP_TEXTEDITORTINYKEY=$REACT_APP_TEXTEDITORTINYKEY \
    REACT_APP_GOOGLE_ANALYTICS_MEASURMENT_ID=$REACT_APP_GOOGLE_ANALYTICS_MEASURMENT_ID \
    REACT_APP_STRIPE_PUBLISHKEY=$REACT_APP_STRIPE_PUBLISHKEY

# Copy package.json and package-lock.json to install dependencies
COPY web/package*.json ./

RUN npm install -g npm@10.2.0
RUN npm config set registry https://registry.npmjs.org/
RUN npm config set fetch-retry-mintimeout 20000
RUN npm config set fetch-retry-maxtimeout 120000
RUN npm cache clean --force
RUN npm install

# Copy the rest of the React app files (selectively)
COPY web .

# Build the React app for production
RUN npm run build

# Step 2: Use NGINX to serve the built app
FROM nginx:alpine

# Copy custom Nginx configuration file (if you have one)
COPY deploy/frontend/nginx.conf /etc/nginx/nginx.conf

# Set the working directory for NGINX
WORKDIR /usr/share/nginx/html

# Clean the existing content in Nginx's HTML folder (to avoid conflicts)
RUN rm -rf ./*

# Copy the build files from the build stage to the NGINX HTML directory
COPY --from=nodework /app/build .

# Expose port 80 for the NGINX web server (use 80 instead of 3000)
EXPOSE 80

# Start NGINX to serve the app
ENTRYPOINT ["nginx", "-g", "daemon off;"]
