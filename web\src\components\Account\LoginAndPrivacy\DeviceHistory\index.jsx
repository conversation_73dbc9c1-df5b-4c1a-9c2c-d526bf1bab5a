import React from 'react';
import { FaDesktop, FaMobileAlt, FaTabletAlt } from 'react-icons/fa';

export const DeviceHistory = ({ deviceInfo }) => {
  const getDeviceIcon = (deviceType) => {
    switch (deviceType?.toLowerCase()) {
      case 'pc':
      case 'laptop':
        return <FaDesktop className="text-3xl text-gray-600" />;
      case 'mobile':
        return <FaMobileAlt className="text-3xl text-gray-600" />;
      case 'tablet':
      case 'ipad':
        return <FaTabletAlt className="text-3xl text-gray-600" />;
      default:
        return <FaDesktop className="text-3xl text-gray-600" />;
    }
  };

  return (
    <>
      <div className="pt-12 pb-48 px-4">
        {deviceInfo?.length > 0 ? (
          deviceInfo.map((device, index) => (
            <div
              key={index}
              className="flex items-center justify-between w-full max-w-[400px] mr-auto mb-6 border p-4 rounded-md shadow-sm"
            >
              <div className="flex items-center gap-4 w-full">
                <div className="shrink-0">{getDeviceIcon(device.deviceType)}</div>
                <div className="flex flex-col">
                  <p className="text-base md:text-lg font-semibold">{device.deviceType || 'Unknown Device'}</p>
                  <p className="text-sm md:text-base text-gray-600">
                    {new Date(device.loginTimestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p className="text-center text-gray-500">No device history available.</p>
        )}
      </div>
    </>
  );
};
