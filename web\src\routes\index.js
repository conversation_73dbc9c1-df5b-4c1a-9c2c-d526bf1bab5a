import React, { useEffect, Suspense } from 'react';
import history from 'history.js';

import {
  BrowserRouter as Router,
  Route,
  Routes,
} from 'react-router-dom';
import BnbNav from 'components/NavBar/BnbNav';
import ProtectedRoute from 'routes/ProtectedRoute.js';
import ScrollToTop from 'components/ScrollToTop';
import { ChatProvider } from 'common/contexts/chatConversationsContext';
import { initGA } from 'services/googleAnalytics/Analytics';
import RouteChangeTracker from 'services/googleAnalytics/RouteChangeTracker';

// Lazy load heavy components
const Main = React.lazy(() => import('routes/Main.js'));
const Auth = React.lazy(() => import('routes/Auth.js'));
const Member = React.lazy(() => import('routes/Member.js'));
const Admin = React.lazy(() => import('routes/Admin.js'));
const LandingPage = React.lazy(() => import('pages/Landingpage'));
const PropertyDetails = React.lazy(() => import('pages/customer/PropertyDetails'));
const HowItWorks = React.lazy(() => import('pages/HowItWorks/HowItWorks'));
const SearchListings = React.lazy(() => import('pages/customer/SearchListings'));
const BlogsDetails = React.lazy(() => import('pages/Blogs/BlogsDetails'));
const Blogs = React.lazy(() => import('pages/Blogs').then(module => ({ default: module.Blogs })));
const AboutUs = React.lazy(() => import('components/AboutUs/AboutUs'));
const CommingSoon = React.lazy(() => import("pages/CommingSoon/CommingSoon"));
const ContactUs = React.lazy(() => import('components/ContactUs/ContactUs'));
const Influencer = React.lazy(() => import('pages/influencer'));
const StrInnovator = React.lazy(() => import('components/Str/StrInnovator'));
const PartnerResource = React.lazy(() => import('components/PartnerResource/PartnerResource'));
const Referrals = React.lazy(() => import('pages/guest/Referrals/Referrals'));
const Innovator = React.lazy(() => import("pages/newinnovatorwelcome/Innovator"));
const Innovator500 = React.lazy(() => import("pages/newinnovatorwelcome/Fivehundreed/Innovator500"));

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
  </div>
);

function Root() {
  useEffect(() => {
    initGA();
  }, []);

  return (
    <>
      <Router history={history}>
        <RouteChangeTracker />
        <ScrollToTop />
        <ChatProvider>
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              <Route element={<ProtectedRoute />}>
                <Route
                  path="/*"
                  element={<Main />} />

                <Route
                  path="member/*"
                  element={<Member />} />
              </Route>
              <Route
                path="/"
                element={<> <BnbNav /> <LandingPage /></>} />
              <Route path="/rooms/search" element={<> <BnbNav /> <SearchListings /></>} />
              <Route path='/room/:id' element={<PropertyDetails />} />
              <Route path="/blogs" element={<Blogs />} />
              <Route path="blog/:id" element={<BlogsDetails />} />
              <Route path='auth/*' element={<Auth />} />
              <Route path="/aboutus" element={<AboutUs />} />
              <Route path="/influencer" element={<Influencer />} />
              <Route path='/contactus'
                element={<ContactUs />}
              />
              <Route path='/str-innovator'
                element={<StrInnovator />}
              />
              <Route path='/referrals'
                element={<Referrals />}
              />
              <Route path='/partner-resource'
                element={<PartnerResource />}
              />
              <Route path='/how-it-works'
                element={<HowItWorks />}
              />
              <Route path="/coming-soon" element={<CommingSoon />} />
              <Route
                path="admin/*"
                element={<Admin />} />
              <Route path="/innovator" element={<Innovator />} />
              <Route path="/first500" element={<Innovator500 />} />
            </Routes>
          </Suspense>
        </ChatProvider>
        {/*  new welcome pages  */}

      </Router>

    </>
  );
}

export default Root;

