import React, { useEffect, Suspense } from 'react';
import history from 'history.js';
import {
  BrowserRouter as Router,
  Route,
  Routes,
} from 'react-router-dom';
import { initGA } from 'services/googleAnalytics/Analytics';
import { dynamicImport, createRouteComponent } from 'utils/bundleOptimizer';
import PerformanceMonitor from 'components/PerformanceMonitor/PerformanceMonitor';

// Immediate imports for critical components
import ProtectedRoute from 'routes/ProtectedRoute.js';
import ScrollToTop from 'components/ScrollToTop';
import { ChatProvider } from 'common/contexts/chatConversationsContext';
import RouteChangeTracker from 'services/googleAnalytics/RouteChangeTracker';

// Optimized lazy loading with retry logic
const Main = React.lazy(() => dynamicImport(() => import('routes/Main.js')));
const Auth = React.lazy(() => dynamicImport(() => import('routes/Auth.js')));
const Member = React.lazy(() => dynamicImport(() => import('routes/Member.js')));
const Admin = React.lazy(() => dynamicImport(() => import('routes/Admin.js')));

// Critical path components (loaded with higher priority)
const LandingPage = React.lazy(() => dynamicImport(() => import('pages/Landingpage')));
const BnbNav = React.lazy(() => dynamicImport(() => import('components/NavBar/BnbNav')));

// Secondary components (loaded on demand)
const PropertyDetails = React.lazy(() => dynamicImport(() => import('pages/customer/PropertyDetails')));
const SearchListings = React.lazy(() => dynamicImport(() => import('pages/customer/SearchListings')));

// Tertiary components (loaded with lower priority)
const HowItWorks = React.lazy(() => dynamicImport(() => import('pages/HowItWorks/HowItWorks')));
const BlogsDetails = React.lazy(() => dynamicImport(() => import('pages/Blogs/BlogsDetails')));
const Blogs = React.lazy(() => dynamicImport(() => import('pages/Blogs').then(module => ({ default: module.Blogs }))));
const AboutUs = React.lazy(() => dynamicImport(() => import('components/AboutUs/AboutUs')));
const CommingSoon = React.lazy(() => dynamicImport(() => import("pages/CommingSoon/CommingSoon")));
const ContactUs = React.lazy(() => dynamicImport(() => import('components/ContactUs/ContactUs')));
const Influencer = React.lazy(() => dynamicImport(() => import('pages/influencer')));
const StrInnovator = React.lazy(() => dynamicImport(() => import('components/Str/StrInnovator')));
const PartnerResource = React.lazy(() => dynamicImport(() => import('components/PartnerResource/PartnerResource')));
const Referrals = React.lazy(() => dynamicImport(() => import('pages/guest/Referrals/Referrals')));
const Innovator = React.lazy(() => dynamicImport(() => import("pages/newinnovatorwelcome/Innovator")));
const Innovator500 = React.lazy(() => dynamicImport(() => import("pages/newinnovatorwelcome/Fivehundreed/Innovator500")));

// Optimized loading components
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
    <div className="ml-4 text-lg text-gray-600">Loading...</div>
  </div>
);

const RouteLoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);

function Root() {
  useEffect(() => {
    initGA();

    // Preload critical components after initial render
    const preloadCriticalComponents = () => {
      // Preload components likely to be used soon
      import('components/NavBar/BnbNav').catch(() => {});
      import('pages/customer/PropertyDetails').catch(() => {});
      import('pages/customer/SearchListings').catch(() => {});
    };

    // Use requestIdleCallback for low priority preloading
    if ('requestIdleCallback' in window) {
      requestIdleCallback(preloadCriticalComponents);
    } else {
      setTimeout(preloadCriticalComponents, 2000);
    }
  }, []);

  return (
    <>
      <PerformanceMonitor
        enableLogging={process.env.NODE_ENV === 'development'}
        onMetric={(metric) => {
          // Send metrics to analytics in production
          if (process.env.NODE_ENV === 'production' && window.gtag) {
            window.gtag('event', 'performance_metric', {
              metric_name: metric.name,
              metric_value: metric.value
            });
          }
        }}
      />
      <Router history={history}>
        <RouteChangeTracker />
        <ScrollToTop />
        <ChatProvider>
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              <Route element={<ProtectedRoute />}>
                <Route
                  path="/*"
                  element={<Main />} />

                <Route
                  path="member/*"
                  element={<Member />} />
              </Route>
              <Route
                path="/"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <BnbNav />
                    <LandingPage />
                  </Suspense>
                }
              />
              <Route
                path="/rooms/search"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <BnbNav />
                    <SearchListings />
                  </Suspense>
                }
              />
              <Route
                path='/room/:id'
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <PropertyDetails />
                  </Suspense>
                }
              />
              <Route
                path="/blogs"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <Blogs />
                  </Suspense>
                }
              />
              <Route
                path="blog/:id"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <BlogsDetails />
                  </Suspense>
                }
              />
              <Route path='auth/*' element={<Auth />} />
              <Route
                path="/aboutus"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <AboutUs />
                  </Suspense>
                }
              />
              <Route
                path="/influencer"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <Influencer />
                  </Suspense>
                }
              />
              <Route
                path='/contactus'
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <ContactUs />
                  </Suspense>
                }
              />
              <Route
                path='/str-innovator'
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <StrInnovator />
                  </Suspense>
                }
              />
              <Route
                path='/referrals'
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <Referrals />
                  </Suspense>
                }
              />
              <Route
                path='/partner-resource'
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <PartnerResource />
                  </Suspense>
                }
              />
              <Route
                path='/how-it-works'
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <HowItWorks />
                  </Suspense>
                }
              />
              <Route
                path="/coming-soon"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <CommingSoon />
                  </Suspense>
                }
              />
              <Route
                path="admin/*"
                element={<Admin />}
              />
              <Route
                path="/innovator"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <Innovator />
                  </Suspense>
                }
              />
              <Route
                path="/first500"
                element={
                  <Suspense fallback={<RouteLoadingSpinner />}>
                    <Innovator500 />
                  </Suspense>
                }
              />
            </Routes>
          </Suspense>
        </ChatProvider>
        {/*  new welcome pages  */}

      </Router>

    </>
  );
}

export default Root;

