import mongoose from 'mongoose';
import moment from 'moment';
import { Strip<PERSON> } from 'stripe';
import { Request, Response } from 'express';
import { Reservation, User, PointsMang, Property } from '../model';
import { sendEmail } from '../services/mailJetEmail';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

const createReservation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, property_id, offerState, propertyUserId, cardId } = req.body;

    
    if (!offerState.checkIn || !offerState.checkOut) {
      res.status(400).json({ status: false, error: 'Please fill all check-in and check-out fields' });
      return;
    }

    let propertyData = await Property.findById(property_id).populate('userId');
    if (!propertyData) {
      res.status(404).json({ status: false, error: 'Property not found' });
      return;
    }

    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ status: false, error: 'User not found' });
      return;
    }

    const selectedCard = user.cardInfo.find(card => card._id.toString() === cardId);

    if (!selectedCard) {
      res.status(400).json({ status: false, error: 'Card not found or invalid card ID' });
      return;
    }

    if (!selectedCard.paymentMethodId) {
      res.status(400).json({ status: false, error: 'Payment method ID is missing for this card' });
      return;
    }

    const paymentMethodId = selectedCard.paymentMethodId;
    const stripeCustomerId = user.stripeCustomerId;

    if (!stripeCustomerId) {
      res.status(400).json({ status: false, error: 'User does not have a Stripe customer ID' });
      return;
    }

    // Create a payment intent with the selected card
    const paymentIntent = await stripe.paymentIntents.create({
      amount: parseInt(offerState.serviceFee) * 100,
      currency: 'usd',
      description: 'Reservation payment for property',
      payment_method: paymentMethodId,
      customer: stripeCustomerId,
      confirm: true,
      return_url: 'https://example.com/order/123/complete',
      off_session: true,
    });

    if (!paymentIntent || paymentIntent.status !== 'succeeded') {
      res.status(500).json({ status: false, error: 'Payment failed to complete' });
      return;
    }

    req.body.paymentIntentId = paymentIntent.id;
    const reservation = await Reservation.create(req.body);
    user.reservationCount = (user.reservationCount || 0) + 1;
    if (user.reservationCount === 1) {
      user.firstReservationDate = new Date();
    }
    await user.save();
    const checkInDate = moment(offerState?.checkIn);
    const checkOutDate = moment(offerState?.checkOut);
    const datesInRange = [];
    let currentDate = moment(checkInDate);
    while (currentDate.isSameOrBefore(checkOutDate)) {
      datesInRange.push(currentDate.toDate());
      currentDate.add(1, 'day');
    }
    propertyData?.bookDates.push(...datesInRange);
    await propertyData.save();

    user.points = user.points - parseInt(offerState.total);
    await user.save();

    await PointsMang.create({
      pointsCount: offerState.points,
      sendBy: userId,
      recievedBy: propertyUserId,
      checkIn: moment(offerState?.checkIn),
      checkOut: moment(offerState?.checkOut),
      reservationId: reservation?._id,
    });

    const checkinformattedDateTime = checkInDate.format('dddd, MMMM DD, YYYY [at] hh:mm A');
    const checkoutformattedDateTime = checkOutDate.format('dddd, MMMM DD, YYYY [at] hh:mm A');

    const ownerEmailParameters = {
      ownerName: (propertyData.userId as any).fname,
      guestName: user.fname,
      address: propertyData.address,
      tripLink: `${process.env.frontendLink}/mytrip/${reservation._id}`,
      propertyTitle: propertyData.title,
      propertyLink: `${process.env.frontendLink}/property/${property_id}`,
      checkIn: checkinformattedDateTime,
      checkOut: checkoutformattedDateTime,
      confirmationCode: reservation._id,
      ownerEmail: (propertyData.userId as any).email,
      houseManual: 'House manual information will be provided soon.',
      houseRules: 'Please follow standard house rules.',
      termsLink: `${process.env.frontendLink}/terms`,
      privacyLink: `${process.env.frontendLink}/privacy`,
      helpLink: `${process.env.frontendLink}/help`,
      contactLink: `${process.env.frontendLink}/contact`,
      companyName: "BnByond.com",
      companyAddress: "168 Hobsons Lake Dr Suite 301, Beechville, NS, Canada B3S 1A2"
    };

    await sendEmail(
      (propertyData.userId as any).email,
      "New Booking: Your Property Has Been Reserved!",
      ownerEmailParameters,
      "send_to_owner_template"
    );

    const reservatorEmailParameters = {
      checkIn: checkinformattedDateTime,
      checkOut: checkoutformattedDateTime,
      NumberOFNights: checkOutDate.diff(checkInDate, 'days'),
      NumberOFGuest: offerState.guests || 1,
      propertyAddress: propertyData.address,
      pointsCount: offerState.points,
      serviceFee: offerState.serviceFee,
      uniquelink: `${process.env.frontendLink}mytrip/${reservation._id}`
    };

    await sendEmail(
      user.email,
      "Booking Confirmed! Your Stay Awaits",
      reservatorEmailParameters,
      "booking_details_confirmation"
    );

    res.status(201).json({ status: true, data: reservation });

  } catch (error) {
    console.error(error);
    res.status(500).json({ status: false, error: 'Internal Server Error' });
  }
};

const getAllReservations = async (req, res) => {
  const { id } = req.params;
  const { role } = req.query;

  try {
    let reservations;

    if (role === 'admin') {
      reservations = await Reservation.find()
        .populate('userId', 'fname lname pic username email address')
        .populate({
          path: 'property_id',
          populate: {
            path: 'userId',
            select: 'fname lname pic username email address'
          }
        });
    } else {
      reservations = await Reservation.find({ userId: id })
        .populate('userId', 'fname lname pic username email address')
        .populate({
          path: 'property_id',
          populate: {
            path: 'userId',
            select: 'fname lname pic username email address'
          }
        });
    }

    res.status(200).json(reservations);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const getReservationsByUserId = async (req: Request, res: Response): Promise<void> => {
  const { _id } = req.params;

  try {
    const reservations = await Reservation.find({ userId: _id })
    if (!reservations || reservations.length === 0) {
      res.status(404).json({ message: 'No reservations found for this user.' });
      return;
    }

    res.status(200).json(reservations);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const getReservationById = async (req: Request, res: Response): Promise<void> => {
  try {
    const reservation = await Reservation.findById(req.params._id).populate('userId', 'fname lname pic username email address').populate('property_id');
    if (!reservation) {
      res.status(404).json({ error: 'Reservation not found' });
      return;
    }
    res.status(200).json(reservation);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const gethostByGuestId = async (req: Request, res: Response): Promise<void> => {
  try {
    const reservations = await Reservation.find({ 'userId': req.params.userId })
      .populate({
        path: 'userId',
        select: 'fname lname pic username email address',
      })
      .populate({
        path: 'property_id',
        populate: {
          path: 'userId',
          select: 'fname lname pic username email address',
        },
      });

    if (!reservations || reservations.length === 0) {
      res.status(404).json({ error: 'Reservations not found for the given userId' });
      return;
    }

    const responseArray = reservations
      .map((reservation) => {
        if (!reservation.property_id || !(reservation.property_id as any).userId) {
          return null;
        }
        const propertyIdObject = reservation.property_id instanceof mongoose.Document
          ? reservation.property_id.toObject()
          : reservation.property_id;
        if (!propertyIdObject) {
          return null;
        }

        return {
          users: propertyIdObject.userId,
        };
      })
      .filter((entry) => entry !== null);

    if (responseArray.length === 0) {
      res.status(404).json({ error: 'No valid data found for the given userId' });
      return;
    }

    const uniqueUsersMap = new Map();
    responseArray.forEach((entry) => {
      if (entry && entry.users && entry.users._id) {
        const userId = entry.users._id.toString();
        if (!uniqueUsersMap.has(userId)) {
          uniqueUsersMap.set(userId, entry.users);
        }
      }
    });

    const uniqueUsersArray = Array.from(uniqueUsersMap.values());

    res.status(200).json(uniqueUsersArray);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const filterReservationsByUserId = (reservations: any[], userId: string): any[] => {
  return reservations.filter((reservation) => {
    return (
      reservation.property_id &&
      reservation.property_id.userId &&
      String(reservation.property_id.userId._id) === String(userId)
    );
  });
};

const getGuestByHostId = async (req: Request, res: Response): Promise<void> => {
  try {
    const allReservations = await Reservation.find({}).populate({
      path: 'userId',
      select: 'fname lname pic username email address',
    }).populate({
      path: 'property_id',
      populate: {
        path: 'userId',
        select: 'fname lname pic username email address',
      },
    });
    const filteredReservations = filterReservationsByUserId(allReservations, req.params.userId);
    if (!filteredReservations || filteredReservations.length === 0) {
      res.status(404).json({ error: 'User not found for the given userId inside property_id' });
      return;
    }

    const uniqueUsersMap = new Map();
    filteredReservations.forEach((reservation) => {
      const userIdObject = reservation.userId instanceof mongoose.Document
        ? reservation.userId.toObject()
        : reservation.userId;

      if (userIdObject) {
        uniqueUsersMap.set(String(userIdObject._id), userIdObject);
      }
    });

    const uniqueUsersArray = Array.from(uniqueUsersMap.values());

    if (uniqueUsersArray.length === 0) {
      res.status(404).json({ error: 'No valid data found for the given userId inside property_id' });
      return;
    }

    res.status(200).json(uniqueUsersArray);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const updateReservation = async (req: Request, res: Response): Promise<void> => {
  try {
    const updatedReservation = await Reservation.findByIdAndUpdate(req.params._id, req.body, { new: true });
    if (!updatedReservation) {
      res.status(404).json({ error: 'Reservation not found' });
      return;
    }
    res.status(200).json(updatedReservation);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const deleteReservation = async (req: Request, res: Response): Promise<void> => {
  try {
    const deletedReservation = await Reservation.findByIdAndDelete(req.params._id);
    if (!deletedReservation) {
      res.status(404).json({ error: 'Reservation not found' });
      return;
    }
    res.status(200).json(deletedReservation);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const refundReservation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { _id } = req.params;
    const { refundReason } = req.body;
    const refundReservation = await PointsMang.findOne({ reservationId: _id });
    if (!refundReservation) {
      res.status(404).json({ error: 'Reservation not found' });
      return;
    }

    if (!refundReservation.isActive) {
      res.status(404).json({ error: 'Active reservation not found' });
      return;
    }

    if (refundReservation.isRefund) {
      res.status(400).json({ error: 'Refund request already made' });
      return;
    }

    refundReservation.isRefund = true;
    await refundReservation.save();
    await Reservation.findByIdAndUpdate(
      refundReservation.reservationId._id,
      { refundReason: refundReason },
      { new: true }
    );

    res.status(200).json(refundReservation);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const refundApproval = async (req: Request, res: Response): Promise<void> => {
  try {
    const { _id } = req.params;
    const { status } = req.body;

    const refundApprovalResult = await PointsMang.findOne({ reservationId: _id }).populate('reservationId');

    if (!refundApprovalResult) {
      res.status(404).json({ error: 'Reservation not found' });
      return;
    }

    if (!refundApprovalResult.isRefund) {
      res.status(400).json({ error: 'Refund not applicable for this reservation' });
      return;
    }

    if ((refundApprovalResult.reservationId as any).isRefundStatus === 'refunded' || (refundApprovalResult.reservationId as any).isRefundStatus === 'rejected') {
      res.status(400).json({ error: 'Already processed' });
      return;
    }

    if (status === 'approved') {
      refundApprovalResult.isActive = false;

      const Reservationdata = await Reservation.findById(refundApprovalResult.reservationId._id).populate("property_id");


      const checkInDate = new Date(Reservationdata.offerState.checkIn);
      const checkOutDate = new Date(Reservationdata.offerState.checkOut);
      const now = new Date();
      const daysBeforeCheckIn = Math.ceil((checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      let refundPercentageGuest: number, refundPercentageHost: number;

      if (daysBeforeCheckIn >= 60) {
        refundPercentageGuest = 1;
        refundPercentageHost = 0;
      } else if (daysBeforeCheckIn >= 15) {
        refundPercentageGuest = 0.5;
        refundPercentageHost = 0.5;
      } else {
        refundPercentageGuest = 0;
        refundPercentageHost = 1;
      }

      const totalPoints = (refundApprovalResult.reservationId as any).offerState.points;
      const pointsToGuest = Math.floor(totalPoints * refundPercentageGuest);
      const pointsToHost = Math.floor(totalPoints * refundPercentageHost);



      const updatedBookDates = (Reservationdata.property_id as any).bookDates.filter((date: string) => {
        const currentDate = new Date(date);
        return currentDate < checkInDate || currentDate > checkOutDate;
      });

      (Reservationdata.property_id as any).bookDates = updatedBookDates;
      await (Reservationdata.property_id as any).save();

      await Reservation.findByIdAndUpdate(
        refundApprovalResult.reservationId._id,
        {
          isRefundStatus: "refunded", status: "cancelled", refundRecord: {
            refundedBy: refundApprovalResult.sendBy,
            pointsToGuest,
            pointsToHost,
            refundProcessedAt: new Date(),
          }
        },
        { new: true }
      );


      refundApprovalResult.refundDetails = {
        pointsToGuest,
        pointsToHost,
        refundProcessedAt: new Date(),
      };

      await refundApprovalResult.save();
      if (pointsToGuest > 0) {
        await User.findByIdAndUpdate(
          refundApprovalResult.sendBy._id,
          { $inc: { points: pointsToGuest } },
          { new: true }
        );
      }

      if (pointsToHost > 0) {
        await User.findByIdAndUpdate(
          refundApprovalResult.recievedBy._id,
          { $inc: { points: pointsToHost } },
          { new: true }
        );
      }

      res.status(200).json({ success: true, message: "Refund successfully processed" });
    } else if (status === 'rejected') {
      await Reservation.findByIdAndUpdate(refundApprovalResult.reservationId._id, { isRefundStatus: "rejected" }, { new: true });
      res.status(200).json({ success: true, message: "Refund request successfully rejected" });
    } else {
      res.status(400).json({ error: 'Invalid status' });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
};


const reservationToday = async (req, res) => {
  try {
    const { hostId } = req.query;

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Clear time

    const reservations = await Reservation.find()
      .populate('userId', 'name username pic phoneNumber email')
      .populate('property_id', 'title pics userId spaceTypeDetail address');
    const filtered = reservations.filter(
      (r) => (r.property_id as any)?.userId?.toString() === hostId
    );
    const result = {
      today: [],
      upcoming: [],
    };

    filtered.forEach((res: any) => {
      const checkInDate = new Date(res.offerState.checkIn);
      const category = checkInDate.toDateString() === today.toDateString() ? 'today' : 'upcoming';

      result[category].push({
        id: res._id,
        guestName: res.userId.username,
        pic: res.userId.pic,
        status:res.status,
        phone: res.userId.phoneNumber,
        email: res.userId.email,
        checkIn: res.offerState.checkIn,
        checkOut: res.offerState.checkOut,
        points:res.offerState.points,
        property: res.property_id.title,
        address: res.property_id.address,
        Guest: res.property_id.spaceTypeDetail.guests,
        image: res.property_id.pics || '',
        createdAt: res.timeStamp,
      });
    });

    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

export const Controller = {
  createReservation,
  getAllReservations,
  getReservationById,
  getGuestByHostId,
  gethostByGuestId,
  updateReservation,
  deleteReservation,
  getReservationsByUserId,
  refundReservation,
  refundApproval,
  reservationToday
}