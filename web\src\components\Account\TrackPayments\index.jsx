import React, { useEffect, useState } from 'react';
import { IoIosArrowForward, IoIosArrowDown } from 'react-icons/io';
import { localStorageData } from "services/auth/localStorageData";
import userService from 'services/httpService/userAuth/userServices';
import Breadcrumbs from 'components/BreadCrumbs/BreadCrumbs';
import Modal from "components/Modal/Modal";

export const TrackPayments = () => {
    const [allPayments, setAllPayments] = useState([]);
    const [filteredPayments, setFilteredPayments] = useState([]);
    const [filterStatus, setFilterStatus] = useState("All");
    const [subscriptionDetails, setSubscriptionDetails] = useState([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [modalType, setModalType] = useState(''); // 'subscription' or 'payment'

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatReservationId = (id) => {
        if (!id) return '';
        if (id.length <= 4) return id;
        return `...${id.slice(-4)}`;
    };

    const getPaymentDetails = async () => {
        try {
            const userId = localStorageData("_id");

            if (!userId) {
                console.error('No user ID found');
                return;
            }

            const response = await userService.getPaymentDetailsById(userId);

            if (response && response.data) {
                setSubscriptionDetails(response.data.stripeSubscription);
                setAllPayments(response.data.userData);
                setFilteredPayments(response.data.userData);
            }
        } catch (error) {
            console.error('Error fetching payment details:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
        }
    };

    useEffect(() => {
        getPaymentDetails();
    }, []);

    useEffect(() => {
        if (filterStatus === "All") {
            setFilteredPayments(allPayments);
        } else {
            setFilteredPayments(allPayments.filter(payment => {
                if (filterStatus === "Refund") {
                    return payment.refund_status === "refunded";
                } else if (filterStatus === "Paid") {
                    return payment.payment_status === "pending" || payment.payment_status === "paid";
                }
                return false;
            }));
        }
    }, [filterStatus, allPayments]);

    const handleFilterChange = (status) => {
        setFilterStatus(status);
    };

    const openSubscriptionModal = (subscription) => {
        setSelectedItem(subscription);
        setModalType('subscription');
        setIsModalOpen(true);
    };

    const openPaymentModal = (payment) => {
        setSelectedItem(payment);
        setModalType('payment');
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
        setSelectedItem(null);
    };

    const renderModalContent = () => {
        if (!selectedItem) return null;

        if (modalType === 'subscription') {
            return (
                <div className="p-8">
                    <h2 className="text-2xl font-medium mb-6">Subscription Details</h2>
                    <div className="space-y-4">
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Transaction ID:</span>
                            <span>{selectedItem.id || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Start Date:</span>
                            <span>{formatDate(selectedItem.start_date)}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">End Date:</span>
                            <span>{formatDate(selectedItem.current_period_end)}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Plan:</span>
                            <span>{selectedItem.plan}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Status:</span>
                            <span className={`p-1 px-5 rounded-md ${selectedItem.status === "active" ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"} font-medium`}>
                                {selectedItem.status}
                            </span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Amount:</span>
                            <span>${selectedItem.amount}</span>
                        </div>
                    </div>
                    <div className="mt-6 flex justify-center">
                        <button 
                            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md"
                            onClick={closeModal}
                        >
                            Close
                        </button>
                    </div>
                </div>
            );
        } else if (modalType === 'payment') {
            return (
                <div className="p-8">
                    <h2 className="text-2xl font-medium mb-6">Transaction Details</h2>
                    <div className="space-y-4">
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Transaction ID:</span>
                            <span>{selectedItem.reservation_id || 'N/A'}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Check-In:</span>
                            <span>{formatDate(selectedItem.check_in)}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Check-Out:</span>
                            <span>{formatDate(selectedItem.check_out)}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Location:</span>
                            <span>{selectedItem.property_location}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Points Used:</span>
                            <span>{selectedItem.total_cost} Points</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Service Fee:</span>
                            <span>${selectedItem.serviceFee}</span>
                        </div>
                        <div className="flex justify-between border-b pb-2">
                            <span className="font-medium">Status:</span>
                            <span className={`p-1 px-5 rounded-md ${
                                selectedItem.refund_status === 'refunded' 
                                    ? 'text-[#EC3434] bg-[#EC3434] bg-opacity-10'
                                    : 'text-[#2459BF] bg-[#58C0D0] bg-opacity-10'
                            } font-medium`}>
                                {selectedItem.refund_status === 'refunded'
                                    ? 'Refund'
                                    : selectedItem.refund_status === 'completed'
                                        ? 'Completed'
                                        : 'Paid'}
                            </span>
                        </div>
                    </div>
                    <div className="mt-6 flex justify-center">
                        <button 
                            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md"
                            onClick={closeModal}
                        >
                            Close
                        </button>
                    </div>
                </div>
            );
        }
    };

    return (
        <div className='md:my-10 md:mx-20 flex flex-col gap-4'>
            <Breadcrumbs />
            {/* Subscription Details Table */}
            <div className="rounded-2xl border border-[#C1E1C2] p-4 mb-6">
                <div className='p-5 flex justify-between'>
                    <p className="text-2xl capitalize font-medium">Subscription History</p>
                </div>
                <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
                    <table className="min-w-full table-fixed">
                        <thead className='bg-color-secondry bg-opacity-10'>
                            <tr>
                                <th className="py-4 px-4 text-left font-semibold">ID</th>
                                <th className="py-4 px-4 text-left font-semibold">Start Date</th>
                                <th className="py-4 px-4 text-left font-semibold">End Date</th>
                                <th className="py-4 px-4 text-left font-semibold">Plan</th>
                                <th className="py-4 px-4 text-left font-semibold">Status</th>
                                <th className="py-4 px-4 text-left font-semibold">Amount</th>
                                <th className="py-4 px-4 text-left font-semibold"></th>
                            </tr>
                        </thead>
                        <tbody>
                            {subscriptionDetails && subscriptionDetails.length > 0 ? (
                                subscriptionDetails.map((sub, index) => (
                                    <tr key={`sub-${index}`} className="border-b-1 border-gray-300 text-[#4B4B4B] text-[16px]">
                                        <td className="py-5 px-4 font-normal">{index + 1}</td>
                                        <td className="py-5 px-4 font-normal">{formatDate(sub.start_date)}</td>
                                        <td className="py-5 px-4 font-normal">{formatDate(sub.current_period_end)}</td>
                                        <td className="py-5 px-4 font-normal">{sub.plan}</td>
                                        <td className="py-5 px-4 font-normal">
                                            <span className={`p-1 px-5 rounded-md ${sub.status === "active" ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"} font-medium`}>
                                                {sub.status}
                                            </span>
                                        </td>
                                        <td className="py-5 px-4 font-normal">${sub.amount}</td>
                                        <td className="py-5 px-4">
                                            <IoIosArrowForward 
                                                className="w-6 h-6 cursor-pointer" 
                                                onClick={() => openSubscriptionModal(sub)}
                                            />
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan="7" className="py-5 px-4 text-center text-gray-500">No subscriptions found</td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
            <div className="rounded-2xl border border-[#C1E1C2] h-auto w-full p-4 mb-1">
                <div></div>
                <div className='p-5 flex justify-between'>
                    <p className="text-2xl capitalize font-medium">
                        Payments History
                    </p>
                    <div className="h-[40px] w-[100px] rounded-[50px] border border-[#AEAFB0] text-[#AEAFB0] bg-[#FFFFFF] flex justify-between px-4 py-2 relative cursor-pointer" onClick={() => document.getElementById('filterDropdown').classList.toggle('hidden')}>
                        <span>{filterStatus}</span>
                        <div className="my-auto">
                            <IoIosArrowDown />
                        </div>
                        <div id="filterDropdown" className="absolute top-full left-0 mt-1 w-full bg-white border border-[#AEAFB0] rounded-md shadow-lg hidden">
                            <div className="py-1">
                                <div className="px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleFilterChange("All")}>All</div>
                                <div className="px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleFilterChange("Refund")}>Refund</div>
                                <div className="px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleFilterChange("Paid")}>Paid</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
                    <table className="min-w-full table-fixed">
                        <thead className='bg-color-secondry bg-opacity-10'>
                            <tr className="space-x-3">
                                <th className="py-4 px-4 text-left font-semibold">Booking ID#</th>
                                <th className="py-4 px-4 text-left font-semibold">Check-In's & Out's</th>
                                <th className="py-5 px-4 text-left font-semibold">Location</th>
                                <th className="py-4 px-4 text-left font-semibold">Transactions</th>
                                <th className="py-4 px-4 text-left font-semibold">Status</th>
                                <th className="py-4 px-4 text-left font-semibold"></th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredPayments && filteredPayments.length > 0 ? (
                                filteredPayments.map((payment, index) => (
                                    <tr key={index} className="border-b-2 border-color-[#AFB5C1] text-[#4B4B4B] text-[16px]">
                                        <td className="py-5 px-4 font-normal">{formatReservationId(payment.reservation_id)}</td>
                                        <td className='py-5 px-4 font-normal'>
                                            {`${formatDate(payment.check_in)} - ${formatDate(payment.check_out)}`}
                                        </td>
                                        <td className='py-5 px-4 font-normal'>{payment.property_location}</td>
                                        <td className="py-5 px-4 font-normal">
                                            <div className="flex flex-col gap-2">
                                                <span
                                                    className={`p-1 px-5 rounded-md whitespace-nowrap ${payment.refund_status === 'refunded'
                                                            ? 'text-[#EC3434] bg-[#EC3434] bg-opacity-10'
                                                            : 'text-[#2459BF] bg-[#58C0D0] bg-opacity-10'
                                                        } font-medium`}
                                                >
                                                    {payment.total_cost} Points
                                                </span>
                                                <span
                                                    className={`p-1 px-5 rounded-md ${payment.refund_status === 'refunded'
                                                            ? 'text-[#EC3434] bg-[#EC3434] bg-opacity-10'
                                                            : 'text-[#2459BF] bg-[#58C0D0] bg-opacity-10'
                                                        } font-medium`}
                                                >
                                                    $ {payment.serviceFee} Fee
                                                </span>
                                            </div>
                                        </td>
                                        <td className='py-5 px-4 font-normal'>
                                            {payment.refund_status === 'refunded'
                                                ? 'Refund'
                                                : payment.refund_status === 'completed'
                                                    ? 'Completed'
                                                    : 'Paid'}
                                        </td>
                                        <td className="py-5 px-4">
                                            <IoIosArrowForward 
                                                className="w-6 h-6 cursor-pointer" 
                                                onClick={() => openPaymentModal(payment)}
                                            />
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan="6" className="py-5 px-4 text-center text-gray-500">
                                        No payments found
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Modal */}
            <Modal isOpen={isModalOpen} onClose={closeModal}>
                {renderModalContent()}
            </Modal>
        </div>
    );
};