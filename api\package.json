{"name": "bnbyond-api", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@sentry/node": "^9.11.0", "bcrypt": "^5.0.1", "dotenv": "^10.0.0", "express": "^5.1.0", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "moment": "^2.30.1", "mongoose": "^8.0.3", "multer": "^1.4.3", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "node-ical": "^0.20.1", "node-mailjet": "^3.3.4", "socket.io": "^4.7.4", "stripe": "^15.6.0", "twilio": "^3.69.0", "useragent": "^2.3.0", "uuid": "^8.3.2", "winston": "^3.8.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/bcrypt": "^5.0.2", "@types/dotenv": "^6.1.1", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/multer-s3": "^3.0.3", "@types/node": "^22.14.0", "@types/node-fetch": "^2.6.12", "@types/node-mailjet": "^3.3.12", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.6", "nodemon": "^3.1.9", "supertest": "^6.3.4", "ts-jest": "^29.3.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}