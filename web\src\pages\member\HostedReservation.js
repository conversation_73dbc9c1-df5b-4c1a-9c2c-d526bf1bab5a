import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { localStorageData } from "services/auth/localStorageData";
import userServices from "services/httpService/userAuth/userServices";
import ImageReservations from "assets/img/book.avif";

export default function ReservationsPage() {
  const Navigate = useNavigate();
  const hostId = localStorageData("_id");
  const [activeTab, setActiveTab] = useState("today");
  const [reservationsData, setReservationsData] = useState({
    today: [],
    upcoming: [],
  });

  const reservations = reservationsData[activeTab];

  const reser = async () => {
    const res = await userServices.commingreservation(`/reservation/getreservationbytoday?hostId=${hostId}`);
    setReservationsData({
      today: res.data?.today || [],
      upcoming: res.data?.upcoming || [],

    });
  }

  useEffect(() => {
    reser()
  }, [activeTab])

  return (
    <div className="min-h-screen px-4 py-6">
      {/* Sticky Tabs */}
      <div className="sticky top-20 z-20 py-2">
        <div className="flex justify-center gap-4">
          {["today", "upcoming"].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-7 py-3 rounded-full text-base font-semibold transition ${activeTab === tab
                  ? "bg-black text-white"
                  : "bg-gray-200 text-gray-800"
                }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </div>


      <div className="mt-10 flex flex-col items-center">
        {reservations.length === 0 ? (
          <div className="text-center">
            <img
              src={ImageReservations}
              alt="image"
              className="w-[35rem] mx-auto mb-4"
            />
            <h2 className="text-2xl font-semibold">You don’t have any reservations</h2>
            <p className="text-gray-600 mt-2 text-lg max-w-md mx-auto">
              Consider revisiting your property listing adding more photos or highlighting additional features can help make it more appealing to potential guests!
            </p>
            <button onClick={() => Navigate("/my-properties")} className="mt-6 bg-black text-white px-6 py-2 rounded-md text-sm hover:bg-gray-800">
              Go to listings
            </button>
          </div>
        ) : (
          <div className="w-full max-w-4xl grid grid-cols-1 md:grid-cols-2 gap-6">
            {reservations.map((res) => (
              <div
                key={res.id}
                onClick={() => Navigate(`/hosting/reservations/${res.id}`, { state: { reservation: res } })}
                className="bg-white shadow-md rounded-xl overflow-hidden hover:shadow-lg transition cursor-pointer"
              >
                <img src={res.image[0]} alt={res.property} className="w-full h-48 object-cover" />
                <div className="p-4">
                  <h3 className="font-bold text-lg">{res.property}</h3>
                  <p className="text-sm text-gray-600">
                    Guest: <span className="font-medium">{res.guestName}</span>
                  </p>
                  <p className="text-sm text-gray-600">
                    {res.checkIn} → {res.checkOut}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
