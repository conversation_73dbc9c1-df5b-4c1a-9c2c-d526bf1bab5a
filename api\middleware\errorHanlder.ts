import { logger } from "../config/logger";
import { NextFunction, Request, Response } from "express";

// Middleware to handle 404 Not Found errors
export const notFound = (req: Request, res: Response, next: NextFunction) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404); 
  next(error); 
};

export const errorMiddleware = (err: Error, req: Request, res: Response, next: NextFunction) => {

  // Log the error details with the logger
  logger.error(
    `[${req.method}] ${req.path} ===> StatusCode:: ${res.statusCode || 500}, Message:: ${err.message}, Stack: ${err.stack}`
  );

  //  defaulting to 500 if statusCode is not set
  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;

  res.status(statusCode).json({
    message: err.message || "Something went wrong", 
    stack: process.env.NODE_ENV === 'production' ? null : err.stack, 
  });
};
