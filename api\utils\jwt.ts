// utils/jwt.ts
import jwt from 'jsonwebtoken';
import { Types } from 'mongoose';

// JWT Configuration Constants
export const JWT_TOKEN_EXPIRATION = '7d';

// Types for JWT Payload
export interface JWTUserPayload {
  userId: Types.ObjectId | string;
  email: string;
  roles?: string[];
  isAdmin?: boolean;
  username?: string;
  loginAs?: string;
}

/**
 * Generate a JWT token for a user
 */
export const generateToken = (
  payload: JWTUserPayload, 
  secretKey: string = process.env.TOKEN_KEY
): string => {
  if (!secretKey) {
    throw new Error('JWT secret key is not defined');
  }
  
  return jwt.sign(
    payload,
    secretKey,
    { expiresIn: JWT_TOKEN_EXPIRATION }
  );
};

/**
 * Verify a JWT token
 */
export const verifyToken = (
  token: string, 
  secretKey: string = process.env.TOKEN_KEY
): JWTUserPayload => {
  if (!secretKey) {
    throw new Error('JWT secret key is not defined');
  }
  
  return jwt.verify(token, secretKey) as JWTUserPayload;
};