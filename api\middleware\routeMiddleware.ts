import { adminAuthMiddleware, roleCheckMiddleware, verifyToken } from './auth';

const checkRoleMiddleware = (allowedRoles = []) => {
    return (req, res, next) => {
      if (req.path.includes('/admin')) {
        return adminAuthMiddleware(req, res, () => {
          return roleCheckMiddleware(allowedRoles)(req, res, next);
        });
      } else {
        return verifyToken(req, res, next);
      }
    };
  };

export default checkRoleMiddleware;
