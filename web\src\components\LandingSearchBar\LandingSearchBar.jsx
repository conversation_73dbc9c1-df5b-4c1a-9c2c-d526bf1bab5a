import React, { useState, useEffect, useMemo } from "react";
import Container from "@mui/material/Container";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import AutoAddress from "components/AutoAddress";

const LandingSearchBar = ({
  notPedding,
  onSearchInputChange,
  checkindate,
  checkoutdate,
  initialGuest = "",
  initialRoom = "",
}) => {
  const navigate = useNavigate();
  const { state: locationData } = useLocation();
  const currentDate = dayjs();
  
  // Date state
  const [checkIn, setCheckIn] = useState(checkindate ? dayjs(checkindate) : null);
  const [checkOut, setCheckOut] = useState(checkoutdate ? dayjs(checkoutdate) : null);
  const [datePickerState, setDatePickerState] = useState({
    checkInOpen: false,
    checkOutOpen: false
  });
  
  // Guest and room state
  const [guest, setGuest] = useState(initialGuest );
  const [rooms, setRooms] = useState(initialRoom );
  
  // Location state
  const [fullAddress, setFullAddress] = useState({
    address: "South Carolina, USA",
    lat: 33.8361,
    long: -81.1637,
    city: "",
    state: "South Carolina",
    stateAbbreviation: "SC",
    country: "United States",
    countryAbbreviation: "USA",
  });

  // Handle location data from navigation state
  useEffect(() => {
    if (!locationData) return;
    
    const {
      checkIn: locCheckIn,
      checkOut: locCheckOut,
      fullAddress: locFullAddress,
      spaceTypeDetail,
      bedroomData,
    } = locationData;

    // Update address data if available
    if (locFullAddress) {
      setFullAddress(locFullAddress);
    }

    // Update guest and room data if available
    if (spaceTypeDetail) setGuest(spaceTypeDetail);
    if (bedroomData) setRooms(bedroomData);

    // Convert dates to dayjs objects
    const convertToDayjs = (rawDate) => {
      if (!rawDate) return null;
      if (typeof rawDate === 'object' && rawDate !== null) {
        if (typeof rawDate.format === "function") return dayjs(rawDate);
        if (rawDate instanceof Date) return dayjs(rawDate);
        if (rawDate.$d) return dayjs(rawDate.$d);
      }
      return dayjs(rawDate);
    };

    const newCheckIn = convertToDayjs(locCheckIn);
    const newCheckOut = convertToDayjs(locCheckOut);

    if (newCheckIn?.isValid()) setCheckIn(newCheckIn);
    if (newCheckOut?.isValid()) setCheckOut(newCheckOut);
  }, [locationData]);

  // Handle prop changes for dates
  useEffect(() => {
    if (checkindate && !checkIn) {
      setCheckIn(dayjs(checkindate));
    }
    if (checkoutdate && !checkOut) {
      setCheckOut(dayjs(checkoutdate));
    }
  }, [checkindate, checkoutdate, checkIn, checkOut]);

  // Handle address change from AutoAddress component
  const handleAddressChange = (
    address,
    lat,
    long,
    city,
    state,
    country,
    stateAbbreviation,
    countryAbbreviation
  ) => {
    const newAddress = {
      address,
      lat,
      long,
      city: city || "",
      state,
      stateAbbreviation,
      country,
      countryAbbreviation,
    };
    
    setFullAddress(newAddress);
  };

  // Handle number input changes with validation
  const handleNumericInput = (setter, field) => (e) => {
    const value = Math.max(0, parseInt(e.target.value) || 0);
    setter(value.toString());
    
    if (onSearchInputChange) {
      onSearchInputChange({
        field,
        value: value.toString()
      });
    }
  };

  // Handle date picker open/close
  const toggleDatePicker = (picker, isOpen) => {
    setDatePickerState(prev => ({
      ...prev,
      [picker]: isOpen
    }));
  };

  // Handle check-in date change
  const handleCheckInChange = (newValue) => {
    try {
      const date = newValue ? dayjs(newValue) : null;
      setCheckIn(date);
      
      // Open check-out picker after selecting check-in
      if (date) {
        toggleDatePicker('checkInOpen', false);
        setTimeout(() => toggleDatePicker('checkOutOpen', true), 200);
      }
    } catch (error) {
      console.error("Invalid date:", error);
      setCheckIn(null);
    }
  };

  // Handle search button click
  const handleSearch = () => {
    if (!fullAddress.lat || fullAddress.lat === 0) {
      toast.error("Location required for search");
      return;
    }
    
    // Set default dates if not selected
    const today = dayjs();
    const defaultCheckIn = checkIn || today.add(7, "day");
    const defaultCheckOut = checkOut || today.add(11, "day");
    
    // Prepare URL parameters
    const queryParams = new URLSearchParams();
    if (fullAddress.city && fullAddress.city.trim() !== "") {
      queryParams.append("city", fullAddress.city);
    }
    
    // Use the current state values or defaults
    const currentGuest = guest || "1";
    const currentRooms = rooms || "1";
    
    queryParams.append("bedroomData", currentRooms);
    queryParams.append("spaceTypeDetail", currentGuest);
    queryParams.append("search_mode", "regular_search");
    queryParams.append("state", fullAddress.state);
    queryParams.append("StateAbbr", fullAddress.stateAbbreviation);
    queryParams.append("country", fullAddress.country);
    queryParams.append("CountryAbbr", fullAddress.countryAbbreviation);
    queryParams.append("check_in", defaultCheckIn.format("YYYY-MM-DD"));
    queryParams.append("check_out", defaultCheckOut.format("YYYY-MM-DD"));

    // Navigate to search results with state
    navigate(`/rooms/search?${queryParams.toString()}`, {
      state: {
        spaceTypeDetail: currentGuest,
        bedroomData: currentRooms,
        checkIn: defaultCheckIn,
        checkOut: defaultCheckOut,
        fullAddress,
      },
    });
  };

  // DatePicker common props
  const datePickerProps = useMemo(() => ({
    inputClass: "w-full text-black outline-0 cursor-pointer",
    placeholderClass: "text-gray-400",
  }), []);

  return (
    <Container maxWidth="lg">
      <div className="flex justify-center items-center">
        <span className="text-white font-bold w-[90%] pt-40 lg:pt-24 md:w-[80%] lg:w-[60%] xl:w-[54%] leading-tight text-center text-xl md:text-4xl">
          The Exchange Community for Vacation Rental Property Owners
        </span>
      </div>

      <div className={`z-10 w-full ${notPedding === "notapply" ? "pt-0" : "md:pt-20 lg:pt-28"} center-styl`}>
        {notPedding !== "notapply" && (
          <div className="text-start pl-0 md:pl-0 md:text-start pt-12 md:pt-0">
            <span className="text-[#FFFFFF] text-[24px] md:text-[30px] font-extrabold">
              Find Your Destination
            </span>
          </div>
        )}

        <div className="block lg:flex pt-5">
          <div className="w-full py-2 flex md:justify-center lg:h-[100px] h-full bg-white shadow rounded-r-xl rounded-l-xl md:rounded-r-none md:rounded-l-xl">
            <div className="flex flex-col gap-2 lg:flex-row px-4 text-[#AFB5C1] w-full lg:w-auto items-center justify-between">
              {/* Destination Input */}
              <div className="lg:my-0 md:my-0 w-full lg:w-auto h-[50px]">
                <div className="flex items-center justify-between px-4 py-3 border border-gray-300 rounded-xl">
                  <AutoAddress
                    className="outline-none"
                    placeholder="Destination"
                    defaultAddress="South Carolina, USA"
                    onChangeAddress={handleAddressChange}
                  />
                  <div className="pr-3">
                    <i className="fa-solid fa-location-dot"></i>
                  </div>
                </div>
              </div>

              {/* Date Picker */}
              <div className="flex w-full lg:w-auto items-center justify-center px-4 py-2 border border-gray-300 rounded-xl h-[50px]">
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    open={datePickerState.checkInOpen}
                    onOpen={() => toggleDatePicker('checkInOpen', true)}
                    onClose={() => toggleDatePicker('checkInOpen', false)}
                    value={checkIn}
                    onChange={handleCheckInChange}
                    minDate={currentDate.toDate()}
                    renderInput={({ inputRef, inputProps, InputProps }) => (
                      <div
                        className="flex items-center"
                        onClick={() => toggleDatePicker('checkInOpen', true)}
                      >
                        <input
                          className={datePickerProps.inputClass}
                          ref={inputRef}
                          {...inputProps}
                          placeholder="Check-in"
                        />
                        {InputProps?.endAdornment}
                      </div>
                    )}
                  />
                </LocalizationProvider>

                <div className="w-px h-full mx-2 bg-gray-300"></div>

                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    open={datePickerState.checkOutOpen}
                    onOpen={() => toggleDatePicker('checkOutOpen', true)}
                    onClose={() => toggleDatePicker('checkOutOpen', false)}
                    value={checkOut}
                    onChange={(newValue) => {
                      try {
                        const date = newValue ? dayjs(newValue) : null;
                        setCheckOut(date);
                        toggleDatePicker('checkOutOpen', false);
                      } catch (error) {
                        console.error("Invalid date:", error);
                        setCheckOut(null);
                      }
                    }}
                    minDate={checkIn ? checkIn.toDate() : currentDate.toDate()}
                    renderInput={({ inputRef, inputProps, InputProps }) => (
                      <div
                        className="flex items-center"
                        onClick={() => toggleDatePicker('checkOutOpen', true)}
                      >
                        <input
                          className={datePickerProps.inputClass}
                          ref={inputRef}
                          {...inputProps}
                          placeholder="Check-out"
                        />
                        {InputProps?.endAdornment}
                      </div>
                    )}
                  />
                </LocalizationProvider>
              </div>

              {/* Guests Input */}
              <div className="flex items-center px-4 py-2 border border-gray-300 rounded-xl w-full lg:w-[170px] h-[50px]">
                <input
                  placeholder="Guests"
                  className="w-full outline-none"
                  type="number"
                  min={0}
                  value={guest === "0" ? "" : guest}
                  onChange={handleNumericInput(setGuest, 'guest')}
                />
              </div>

              {/* Rooms Input */}
              <div className="flex items-center px-4 py-2 border border-gray-300 rounded-xl w-full lg:w-[170px] h-[50px]">
                <input
                  placeholder="Rooms"
                  className="w-full outline-none"
                  type="number"
                  min={0}
                  value={rooms === "0" ? "" : rooms}
                  onChange={handleNumericInput(setRooms, 'rooms')}
                />
              </div>

              {/* Search Button */}
              <div className="flex w-full lg:w-auto h-[50px] justify-center">
                <button
                  onClick={handleSearch}
                  className="text-sm w-full lg:w-[100px] font-bold text-white transition-all duration-150 ease-linear rounded-full shadow bg-[#2459BF] hover:shadow-lg"
                >
                  Search
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default LandingSearchBar;