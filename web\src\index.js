import React, { Suspense, useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { createStore, applyMiddleware } from 'redux';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { composeWithDevTools } from 'redux-devtools-extension';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { master } from 'redux/store/reducers/combineReducer';
import { ToastContainer } from 'react-toastify';
import LanguageContextProvider from 'common/contexts/LanguageContext';
import { Elements } from '@stripe/react-stripe-js';
import config from './config';
import { initOptimizedCSS } from 'utils/cssLoader';
import { registerSW, swConfig, preloadCriticalResources } from 'utils/serviceWorkerRegistration';
import optimizedApiService from 'services/optimizedApiService';

// Lazy load heavy components
const Root = React.lazy(() => import('routes'));

// Initialize optimized CSS loading
initOptimizedCSS();

// Register service worker for caching and offline support
if (process.env.NODE_ENV === 'production') {
  registerSW(swConfig);

  // Preload critical resources
  preloadCriticalResources([
    '/assets/img/image1.png',
    '/assets/img/image2.png',
    '/assets/img/image3.png',
    '/api/property/getAllPropertyAvailable?page=1&limit=8&status=Active'
  ]);

  // Preload critical API data
  optimizedApiService.preloadCriticalData();
}

// Optimized loading component
const AppLoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
    <div className="ml-4 text-lg text-gray-600">Loading BnbYond...</div>
  </div>
);

// Stripe wrapper component with lazy loading
const StripeWrapper = ({ children }) => {
  const [stripePromise, setStripePromise] = useState(null);

  useEffect(() => {
    const loadStripe = async () => {
      try {
        const { loadStripe: stripeLoader } = await import('@stripe/stripe-js');
        const stripe = await stripeLoader(config.stripe_publishkey);
        setStripePromise(Promise.resolve(stripe));
      } catch (error) {
        console.error('Failed to load Stripe:', error);
      }
    };

    loadStripe();
  }, []);

  if (!stripePromise) {
    return <AppLoadingSpinner />;
  }

  return (
    <Elements stripe={stripePromise}>
      {children}
    </Elements>
  );
};

// Stripe configuration handled above with lazy loading

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const store = createStore(master, composeWithDevTools(applyMiddleware(thunk)));
const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <GoogleOAuthProvider clientId={config.googleapi}>
        <LanguageContextProvider>
          <StripeWrapper>
            <Suspense fallback={<AppLoadingSpinner />}>
              <Root />
            </Suspense>
          </StripeWrapper>
        </LanguageContextProvider>
      </GoogleOAuthProvider>
      <ToastContainer limit={1} />
    </QueryClientProvider>
  </Provider>
);
