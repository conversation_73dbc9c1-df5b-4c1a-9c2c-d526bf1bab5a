import React, { useState } from "react";
import paycardss from "assets/img/paycardss.png";
import { ButtonFilled } from "common/buttons/buttonFilled";
import { localStorageData } from "services/auth/localStorageData";
import {
  CountryDropdown,
} from "react-country-region-selector";
import { useNavigate } from "react-router-dom";

export const PayoutsStepOne = ({setViewStepTwo}) => {
    
  let loginAs = ("localStorageData", localStorageData("loginAs"))
  const [isChecked, setIsChecked] = useState(false);
  const [country, setCountry] = useState("");

  const navigate = useNavigate();

  const handleCheckboxClick = (card) => {
    setIsChecked(!isChecked);
    setIsChecked(card === isChecked ? null : card);
  };

  const payoutstep2 = () =>{
    navigate('/payoutsteptwo')
  }

  return (
    <div className="w-full  ">
    <div className="p-5 lg:ml-5 flex flex-col ">
      <p>Set up payouts</p>
      <div className="">
        <p className="text-black  text-[30px] font-medium leading-[43px] capitalize">
          Let's add a payout method
        </p>
        <p className="text-gray-700 font-Avenir text-base font-normal leading-[143.4%] capitalize">
          To start, let us know where you'd like us to send your money.
        </p>
      </div>

      <div className="mt-8">
        <p className="mb-4 text-black font-Avenir text-xl font-medium leading-[116.6%]">
          Billing country/region
        </p>
        <div className="border cursor-pointer flex justify-center flex-col p-4 border-[#AFB5C1] w-full md:w-[400px] h-[100px] rounded-lg">
          <p className="text-black font-Avenir text-base font-medium leading-[116.6%]">
            Billing country/region
          </p>
          <CountryDropdown
            value={country}
            onChange={(val) => setCountry(val)}
            className='py-1 text-[#4B4B4B] font-Lato text-base  cursor-pointer font-normal leading-[164%]'
          />
        </div>
        <p className="mt-2 text-[#4B4B4B] font-Lato text-base font-normal leading-[164%]">
          This is where you opened your financial account.&nbsp;
          <span className=" text-[#000] font-Lato text-base font-normal leading-[164%] underline">
        
            More info
          </span>
        </p>
      </div>
    </div>

    <div className="w-full border-t-[1px] border-[#C1E1C2] ">
      <div className=" p-5 my-8 rounded-2xl lg:ml-5">
        <p className="text-black font-Avenir text-xl font-medium leading-[116.6%]">
          How you'll get paid
        </p>
        <p className="text-gray-700 my-2 font-Avenir text-base font-normal leading-[143.4%] ">
          Payouts will be sent in USD.
        </p>

        <div className="md:flex w-full gap-6 my-4">
          <div className="w-full md:w-[400px]  cursor-pointer md:h-[140px] h-[160px] flex md:gap-4 border border-[#AFB5C1] p-4 rounded-2xl"
          onClick={() => handleCheckboxClick("fastPay")}
          >
            <div className="">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="67"
                height="68"
                viewBox="0 0 67 68"
                fill="none"
              >
                <ellipse
                  opacity="0.1"
                  cx="33.7012"
                  cy="33.7209"
                  rx="32.92"
                  ry="33.3303"
                  fill="#58C0D0"
                />
                <path
                  d="M53.2394 37.854C52.8599 37.2203 51.3018 35.3389 47.1895 37.7338L39.9384 41.9572C39.5748 42.1689 39.2052 42.3185 38.8193 42.4076C38.9623 42.1784 39.0744 41.919 39.1513 41.6319C39.5219 40.2484 38.6997 38.804 37.2793 38.3435C37.2517 38.3346 37.2236 38.3275 37.1951 38.3223C35.1111 37.9465 33.0542 37.2029 31.4016 36.6053C30.8273 36.3977 30.285 36.2017 29.8239 36.0522C27.9674 35.4497 26.2893 36.1955 24.8925 37.0042L19.4977 40.1277L18.9892 39.229C18.7647 38.8323 18.398 38.5464 17.9567 38.4241C17.5154 38.3018 17.0539 38.3581 16.6573 38.5826L14.6228 39.734C13.8017 40.1988 13.5119 41.2448 13.9765 42.0659L19.9578 52.6345C20.2723 53.19 20.8528 53.5024 21.4498 53.5024C21.7351 53.5024 22.0243 53.431 22.2897 53.2808L24.3242 52.1294C24.7209 51.9048 25.0067 51.5382 25.129 51.097C25.2514 50.6557 25.1951 50.1942 24.9705 49.7975L24.3538 48.7078L25.5835 47.9979L34.8707 50.4863C35.3953 50.6269 35.9101 50.6972 36.4139 50.6972C37.4183 50.6972 38.3786 50.4181 39.2822 49.8618L52.4412 41.7591C53.6407 41.0206 54.0212 39.1592 53.2394 37.854ZM23.889 50.7533C23.8744 50.807 23.8494 50.8573 23.8153 50.9013C23.7812 50.9453 23.7387 50.9821 23.6903 51.0095L21.6558 52.161C21.4556 52.2742 21.1909 52.2009 21.0777 52.0008L15.0963 41.4321C14.9831 41.232 15.0565 40.9673 15.2566 40.854L17.2911 39.7026C17.3539 39.667 17.4249 39.6483 17.4971 39.6483C17.5357 39.6483 17.5746 39.6535 17.613 39.6642C17.6667 39.6787 17.717 39.7037 17.7609 39.7378C17.8049 39.7719 17.8417 39.8143 17.8691 39.8627L17.8692 39.8628L23.8506 50.4314C23.8779 50.4799 23.8954 50.5333 23.902 50.5885C23.9085 50.6438 23.9041 50.6998 23.889 50.7533ZM51.7664 40.6633L38.6074 48.766C37.5639 49.4084 36.4189 49.5691 35.2038 49.2435L25.6591 46.686C25.4943 46.6419 25.3187 46.665 25.1709 46.7502L23.72 47.5879L20.1317 41.2476L25.5373 38.1179C26.7465 37.4177 28.055 36.8313 29.4269 37.2763C29.8676 37.4192 30.3757 37.6029 30.9639 37.8156C32.6502 38.4252 34.7458 39.1829 36.9202 39.5803C37.6779 39.8451 38.1016 40.5771 37.9082 41.2988C37.6538 42.2483 36.8631 42.3663 36.2337 42.2941C35.1654 42.0745 34.1504 41.7848 33.0763 41.4781C32.5671 41.3326 32.0405 41.1823 31.4993 41.0372C31.4177 41.0154 31.3325 41.0098 31.2488 41.0208C31.165 41.0319 31.0842 41.0593 31.011 41.1015C30.9379 41.1438 30.8737 41.2 30.8223 41.2671C30.7708 41.3341 30.7331 41.4106 30.7112 41.4922C30.6894 41.5739 30.6838 41.659 30.6948 41.7428C30.7058 41.8265 30.7333 41.9073 30.7755 41.9805C30.8178 42.0537 30.874 42.1178 30.9411 42.1692C31.0081 42.2207 31.0846 42.2584 31.1662 42.2803C31.6973 42.4226 32.2186 42.5715 32.7229 42.7155C33.8297 43.0316 34.8752 43.3301 36.0008 43.5602C36.0052 43.5611 36.0222 43.5643 36.0264 43.5649L36.9281 43.7115C38.277 43.9272 39.4734 43.7172 40.586 43.0691L47.8371 38.8458C49.9243 37.6301 51.5311 37.5064 52.1353 38.5151C52.5518 39.2104 52.3655 40.2945 51.7664 40.6633ZM17.8814 41.7437C17.8957 41.8383 17.8911 41.9348 17.8679 42.0276C17.8448 42.1204 17.8035 42.2077 17.7465 42.2846C17.6895 42.3614 17.6179 42.4262 17.5358 42.4753C17.4537 42.5244 17.3628 42.5567 17.2681 42.5706C17.1735 42.5844 17.077 42.5794 16.9843 42.5559C16.8916 42.5323 16.8044 42.4907 16.7279 42.4334C16.6513 42.3761 16.5868 42.3042 16.538 42.2219C16.4893 42.1396 16.4573 42.0485 16.4438 41.9538C16.4168 41.7636 16.4663 41.5704 16.5814 41.4166C16.6965 41.2627 16.8679 41.1608 17.058 41.133C17.2481 41.1052 17.4415 41.1539 17.5958 41.2683C17.7502 41.3828 17.8529 41.5537 17.8814 41.7437ZM27.9904 30.7282C28.5133 30.7282 28.9386 31.1705 28.9386 31.714C28.9386 31.8847 29.0064 32.0483 29.127 32.169C29.2477 32.2897 29.4114 32.3574 29.582 32.3574H49.12C49.2907 32.3574 49.4543 32.2897 49.575 32.169C49.6957 32.0483 49.7635 31.8847 49.7635 31.714C49.7635 31.1705 50.1888 30.7282 50.7116 30.7282C50.8822 30.7282 51.0459 30.6604 51.1666 30.5397C51.2872 30.4191 51.355 30.2554 51.355 30.0847V24.5662C51.355 24.3956 51.2872 24.2319 51.1666 24.1113C51.0459 23.9906 50.8822 23.9228 50.7116 23.9228C50.1888 23.9228 49.7635 23.4805 49.7635 22.937C49.7635 22.7663 49.6957 22.6027 49.575 22.482C49.4543 22.3613 49.2907 22.2935 49.12 22.2935H29.582C29.4114 22.2935 29.2477 22.3613 29.127 22.482C29.0064 22.6027 28.9386 22.7663 28.9386 22.937C28.9386 23.4805 28.5133 23.9228 27.9904 23.9228C27.8197 23.9228 27.6561 23.9906 27.5354 24.1113C27.4147 24.2319 27.3469 24.3956 27.3469 24.5662V30.0847C27.3469 30.2554 27.4147 30.4191 27.5354 30.5397C27.6561 30.6604 27.8197 30.7282 27.9904 30.7282ZM28.6338 25.1137C29.3533 24.8934 29.9211 24.3135 30.1342 23.5805H48.5677C48.7809 24.3136 49.3486 24.8935 50.0681 25.1137V29.5373C49.3486 29.7576 48.7809 30.3375 48.5677 31.0706H30.1342C29.9211 30.3375 29.3533 29.7576 28.6338 29.5373V25.1137ZM30.9539 27.3256C30.9539 27.155 31.0217 26.9913 31.1423 26.8706C31.263 26.75 31.4267 26.6822 31.5973 26.6822H33.9968C34.1674 26.6822 34.3311 26.75 34.4518 26.8706C34.5724 26.9913 34.6402 27.155 34.6402 27.3256C34.6402 27.4963 34.5724 27.6599 34.4518 27.7806C34.3311 27.9013 34.1674 27.969 33.9968 27.969H31.5973C31.4267 27.969 31.263 27.9013 31.1423 27.7806C31.0217 27.6599 30.9539 27.4963 30.9539 27.3256ZM44.0615 27.3256C44.0615 27.155 44.1293 26.9913 44.25 26.8706C44.3706 26.75 44.5343 26.6822 44.7049 26.6822H47.1044C47.275 26.6822 47.4387 26.75 47.5594 26.8706C47.68 26.9913 47.7478 27.155 47.7478 27.3256C47.7478 27.4963 47.68 27.6599 47.5594 27.7806C47.4387 27.9013 47.275 27.969 47.1044 27.969H44.7049C44.5343 27.969 44.3706 27.9013 44.25 27.7806C44.1293 27.6599 44.0615 27.4963 44.0615 27.3256ZM39.9865 28.4901C40.0086 28.4647 40.026 28.4372 40.0167 28.3602C40.006 28.272 39.9809 28.0656 39.2505 27.9457C37.8102 27.7093 37.5564 26.8726 37.5331 26.395C37.497 25.6518 37.9622 25.0224 38.7076 24.7775V24.7384C38.7076 24.5678 38.7754 24.4041 38.8961 24.2835C39.0168 24.1628 39.1804 24.095 39.3511 24.095C39.5217 24.095 39.6854 24.1628 39.806 24.2835C39.9267 24.4041 39.9945 24.5678 39.9945 24.7384V24.769C40.5053 24.9363 40.9279 25.3102 41.148 25.8344C41.1814 25.9125 41.1991 25.9963 41.1999 26.0812C41.2008 26.1661 41.1849 26.2503 41.153 26.329C41.1212 26.4077 41.0741 26.4793 41.0145 26.5397C40.9549 26.6001 40.8839 26.6481 40.8056 26.681C40.7274 26.7139 40.6434 26.7309 40.5585 26.7312C40.4736 26.7314 40.3895 26.7149 40.3111 26.6825C40.2326 26.6501 40.1613 26.6025 40.1013 26.5425C40.0413 26.4824 39.9938 26.4111 39.9615 26.3326C39.8012 25.951 39.3968 25.931 39.1834 25.9796C39.0947 25.9997 38.8064 26.0846 38.8185 26.3326C38.8263 26.4919 39.0416 26.6074 39.459 26.6759C40.5758 26.8593 41.1932 27.3737 41.2942 28.205C41.3458 28.6297 41.2301 29.0194 40.9599 29.3318C40.7298 29.598 40.3881 29.795 39.9945 29.8978V29.9127C39.9945 30.0834 39.9267 30.247 39.806 30.3677C39.6854 30.4884 39.5217 30.5561 39.3511 30.5561C39.1804 30.5561 39.0168 30.4884 38.8961 30.3677C38.7754 30.247 38.7076 30.0834 38.7076 29.9127V29.8862C38.0622 29.6954 37.5746 29.2209 37.4151 28.581C37.3739 28.4154 37.4 28.2402 37.4879 28.0939C37.5758 27.9477 37.7182 27.8423 37.8838 27.801C37.9658 27.7806 38.051 27.7764 38.1346 27.7889C38.2182 27.8014 38.2985 27.8302 38.3709 27.8738C38.4433 27.9173 38.5065 27.9746 38.5567 28.0426C38.607 28.1105 38.6434 28.1877 38.6638 28.2697C38.7441 28.5917 39.1024 28.6983 39.3891 28.6912C39.673 28.6844 39.9045 28.5848 39.9865 28.4901ZM13.7979 20.6161C13.7979 20.4454 13.8657 20.2818 13.9864 20.1611C14.1071 20.0404 14.2707 19.9727 14.4414 19.9727H18.2498C18.4204 19.9727 18.5841 20.0404 18.7048 20.1611C18.8254 20.2818 18.8932 20.4454 18.8932 20.6161C18.8932 20.7867 18.8254 20.9504 18.7048 21.0711C18.5841 21.1917 18.4204 21.2595 18.2498 21.2595H14.4414C14.2707 21.2595 14.1071 21.1917 13.9864 21.0711C13.8657 20.9504 13.7979 20.7867 13.7979 20.6161ZM17.2797 25.7325C17.1091 25.7325 16.9454 25.6647 16.8248 25.544C16.7041 25.4233 16.6363 25.2597 16.6363 25.089C16.6363 24.9184 16.7041 24.7547 16.8248 24.634C16.9454 24.5134 17.1091 24.4456 17.2797 24.4456H23.2451C23.4158 24.4456 23.5794 24.5134 23.7001 24.634C23.8208 24.7547 23.8886 24.9184 23.8886 25.089C23.8886 25.2597 23.8208 25.4233 23.7001 25.544C23.5794 25.6647 23.4158 25.7325 23.2451 25.7325H17.2797ZM20.8424 21.2595H25.0962V28.9186H19.0822C18.9115 28.9186 18.7479 28.9864 18.6272 29.1071C18.5065 29.2277 18.4387 29.3914 18.4387 29.562C18.4387 29.7327 18.5065 29.8963 18.6272 30.017C18.7479 30.1377 18.9115 30.2055 19.0822 30.2055H25.0961V33.3915H23.5942C23.4235 33.3915 23.2599 33.4593 23.1392 33.58C23.0185 33.7007 22.9507 33.8643 22.9507 34.035C22.9507 34.2056 23.0185 34.3693 23.1392 34.4899C23.2599 34.6106 23.4235 34.6784 23.5942 34.6784H52.9623C53.133 34.6784 53.2966 34.6106 53.4173 34.4899C53.538 34.3693 53.6058 34.2056 53.6058 34.035V20.6161C53.6058 20.4454 53.538 20.2818 53.4173 20.1611C53.2966 20.0404 53.133 19.9727 52.9623 19.9727H20.8424C20.6717 19.9727 20.5081 20.0404 20.3874 20.1611C20.2667 20.2818 20.1989 20.4454 20.1989 20.6161C20.1989 20.7867 20.2667 20.9504 20.3874 21.0711C20.5081 21.1917 20.6717 21.2595 20.8424 21.2595ZM52.3189 33.3914H26.383V21.2595H52.3189V33.3914ZM13.7979 29.562C13.7979 29.3913 13.8657 29.2276 13.9864 29.107C14.1071 28.9863 14.2707 28.9185 14.4414 28.9185H16.3456C16.5163 28.9185 16.6799 28.9863 16.8006 29.107C16.9213 29.2276 16.9891 29.3913 16.9891 29.562C16.9891 29.7326 16.9213 29.8963 16.8006 30.0169C16.6799 30.1376 16.5163 30.2054 16.3456 30.2054H14.4414C14.2707 30.2054 14.1071 30.1376 13.9864 30.0169C13.8657 29.8963 13.7979 29.7326 13.7979 29.562ZM17.0152 34.0349C17.0152 33.8642 17.083 33.7006 17.2037 33.5799C17.3243 33.4592 17.488 33.3914 17.6586 33.3914H20.9368C21.1074 33.3914 21.2711 33.4592 21.3918 33.5799C21.5124 33.7006 21.5802 33.8642 21.5802 34.0349C21.5802 34.2055 21.5124 34.3692 21.3918 34.4899C21.2711 34.6105 21.1074 34.6783 20.9368 34.6783H17.6586C17.488 34.6783 17.3243 34.6105 17.2037 34.4899C17.083 34.3692 17.0152 34.2055 17.0152 34.0349Z"
                  fill="#58C0D0"
                />
              </svg>
            </div>
            <div className="flex justify-between w-full items-center">
              <div className="">
                <div className="">
                  <p className="text-black font-Avenir text-base font-medium leading-[116.6%]">
                    Fast Pay
                  </p>
                  <div className="text-[#4B4B4B] mt-1 font-Lato  text-sm font-normal leading-[164%]">
                    <p className="flex md:items-center">
                      <span className="text-[24px] pr-1">•</span> Visa or
                      Mastercard debit required
                    </p>
                    <p className="flex items-center">
                      <span className="text-[24px] pr-1">•</span> 30
                      minutes or less
                    </p>
                    <p className="flex md:items-center">
                      <span className="text-[24px] pr-1">•</span> 1.5% fee
                      (maximum $15)
                    </p>
                  </div>
                </div>
              </div>
              <div
                className= {` mb-20 w-6 h-6 border-2 rounded-full bg-gray-400 border-[#AFB5C1] cursor-pointer 
                ${
                  isChecked === "fastPay" ?  " border-blue-400" : ""
                }
                  `}
              >
                <div
                  className={`rounded-full  w-5 h-5  border-[1px] border-white 
                  ${
                    isChecked === "fastPay" ? "bg-blue-400 w-5 h-5 border-[1px] border-white" : ""
                  }`
                }
                ></div>
              </div>
            </div>
          </div>
          <div className=" w-full md:w-[400px] cursor-pointer mt-4 md:mt-0 md:h-[140px] h-[160px] flex md:gap-4 border border-[#AFB5C1] p-4 rounded-2xl"
                onClick={() => handleCheckboxClick("bankccount")}
                >
            <div className="">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="67"
                height="68"
                viewBox="0 0 67 68"
                fill="none"
              >
                <ellipse
                  opacity="0.1"
                  cx="33.2393"
                  cy="33.7209"
                  rx="32.92"
                  ry="33.3303"
                  fill="#58C0D0"
                />
                <path
                  d="M51.5317 48.679C53.6762 44.9122 52.8037 40.1493 49.4632 37.3874V32.0985H50.2744C50.7224 32.0985 51.0856 31.7353 51.0856 31.2873V28.8538H51.8968C52.3448 28.8527 52.7071 28.4887 52.706 28.0407C52.7057 27.9104 52.6741 27.7821 52.6137 27.6666C52.5533 27.5512 52.466 27.452 52.3591 27.3775L33.7021 14.3986C33.5663 14.3044 33.405 14.2539 33.2397 14.2539C33.0744 14.2539 32.9131 14.3044 32.7773 14.3986L14.1203 27.3775C13.9789 27.4761 13.8726 27.6173 13.8169 27.7806C13.7611 27.9438 13.7589 28.1205 13.8104 28.2851C13.862 28.4497 13.9646 28.5935 14.1035 28.6958C14.2424 28.798 14.4102 28.8534 14.5827 28.8538H15.3939V31.2873C15.3939 31.7353 15.7571 32.0985 16.2051 32.0985H17.0163V45.0773H16.205C15.757 45.0773 15.3938 45.4405 15.3938 45.8885V48.4681C14.9205 48.6354 14.5105 48.9451 14.22 49.3546C13.9296 49.7641 13.7729 50.2535 13.7715 50.7555V52.3778C13.7715 52.8258 14.1347 53.189 14.5827 53.189H51.8968C52.3448 53.189 52.708 52.8258 52.708 52.3778V50.7555C52.7068 50.3375 52.5979 49.9269 52.3919 49.5631C52.1859 49.1994 51.8896 48.8949 51.5317 48.679ZM49.4632 30.4762H42.9738V28.8538H49.4632V30.4762ZM47.8409 32.0985V36.3247C46.8298 35.8054 45.7282 35.4859 44.5961 35.3838V32.0985H47.8409ZM33.2397 16.0534L49.3091 27.2314H17.1703L33.2397 16.0534ZM24.3168 45.0773H23.5056V32.0985H24.3168C24.7648 32.0985 25.128 31.7353 25.128 31.2873V28.8538H28.3727V31.2873C28.3727 31.7353 28.7359 32.0985 29.1839 32.0985H29.9951V45.0773H29.1839C28.7359 45.0773 28.3727 45.4405 28.3727 45.8885V48.3221H25.1279V45.8885C25.1279 45.4405 24.7648 45.0773 24.3168 45.0773ZM29.995 46.6997H35.2109C35.3705 47.2602 35.5852 47.8036 35.8517 48.322H29.9951V46.6997H29.995ZM31.6174 45.0773V32.0985H34.8621V45.0773H31.6174ZM29.995 30.4762V28.8538H36.4844V30.4762H29.995ZM36.4844 32.0985H37.2956C37.7436 32.0985 38.1068 31.7353 38.1068 31.2873V28.8538H41.3516V31.2873C41.3516 31.7353 41.7148 32.0985 42.1628 32.0985H42.9738V35.3838C41.6838 35.5024 40.435 35.9006 39.3144 36.5506C38.1937 37.2005 37.228 38.0867 36.4844 39.1476V32.0985ZM17.0162 28.8538H23.5056V30.4761H17.0162V28.8538ZM21.8833 32.0985V45.0773H18.6385V32.0985H21.8833ZM17.0162 46.6997H23.5056V48.322H17.0162V46.6997ZM15.3938 51.5667V50.7555C15.3938 50.3075 15.757 49.9443 16.205 49.9443H36.9062C37.4181 50.5611 38.0101 51.1067 38.6665 51.5667L15.3938 51.5667ZM51.0856 51.5667H48.8954C49.5273 51.1254 50.0998 50.6047 50.5989 50.0174C50.7427 50.0802 50.8653 50.1833 50.9517 50.3144C51.038 50.4454 51.0846 50.5987 51.0856 50.7556V51.5667ZM43.785 51.5667C39.753 51.5667 36.4844 48.2981 36.4844 44.2661C36.4844 40.2342 39.753 36.9656 43.785 36.9656C47.817 36.9656 51.0856 40.2342 51.0856 44.2661C51.0856 48.2981 47.817 51.5667 43.785 51.5667Z"
                  fill="#58C0D0"
                />
                <path
                  d="M42.6818 24.1409L33.7589 17.6515L32.7611 18.9656L39.6642 23.9868H25.1279V25.6091H42.1626C42.5283 25.627 42.8607 25.3977 42.9738 25.0494C43.0274 24.8865 43.028 24.7109 42.9756 24.5476C42.9231 24.3844 42.8203 24.242 42.6818 24.1409ZM45.4074 40.2103H44.5961V38.588H42.9738V40.2103H42.1626C41.2666 40.2103 40.5403 40.9366 40.5403 41.8326V43.4549C40.5403 44.3509 41.2666 45.0773 42.1626 45.0773H45.4074V46.6996H42.1626V45.8884H40.5403V46.6996C40.5403 47.5956 41.2666 48.3219 42.1626 48.3219H42.9738V49.9442H44.5961V48.3219H45.4074C46.3033 48.3219 47.0297 47.5956 47.0297 46.6996V45.0773C47.0297 44.1813 46.3033 43.4549 45.4074 43.4549H42.1626V41.8326H45.4074V42.6438H47.0297V41.8326C47.0297 40.9366 46.3033 40.2103 45.4074 40.2103Z"
                  fill="#58C0D0"
                />
              </svg>
            </div>
            <div className="flex justify-between w-full items-center">
              <div className="">
                <div className="">
                  <p className="text-black font-Avenir text-base font-medium leading-[116.6%]">
                    Bank account
                  </p>
                  <div className="text-[#4B4B4B] mt-1 font-Lato text-sm font-normal leading-[164%]">
                    <p className="md:flex block items-center">
                      <span className="text-[24px] pr-1">•</span> 3-5
                      business days <br/>
                      <span className="text-[24px] md:px-1">•</span> No fees
                      PayPal
                    </p>
                    <p className="flex ">
                      <span className="text-[24px] pr-1">•</span> 1
                      business day Paypal fees may apply
                    </p>
                  </div>
                </div>
              </div>
              <div
                className= {` mb-20 w-6 h-6 border-2 rounded-full bg-gray-400 border-[#AFB5C1] cursor-pointer 
                
                ${
                  isChecked === "bankccount" ?  " border-blue-400" : ""
                }
                  `}
              >
                <div
                  className={`rounded-full  w-5 h-5  border-[1px] border-white 
                  ${
                    isChecked === "bankccount"  ? "bg-blue-400 w-5 h-5 border-[1px] border-white" : ""
                  }`
                }
                
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div className="md:flex w-full gap-6 my-4">
          <div className=" w-full md:w-[400px]  cursor-pointer md:h-[140px] h-[160px] flex md:gap-4 border border-[#AFB5C1] p-4 rounded-2xl"
           onClick={() => handleCheckboxClick("paypal")}
                >
            <div className="">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="67"
                height="68"
                viewBox="0 0 67 68"
                fill="none"
              >
                <ellipse
                  opacity="0.1"
                  cx="33.7012"
                  cy="33.9241"
                  rx="32.92"
                  ry="33.3303"
                  fill="#58C0D0"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M31.6377 24.9961H41.1536C46.2627 24.9961 48.1861 27.5826 47.8888 31.3826C47.3976 37.6561 43.605 41.1269 38.5744 41.1269H36.0345C35.3443 41.1269 34.8801 41.5838 34.6934 42.8218L33.615 50.0188C33.5438 50.4855 33.2982 50.7557 32.9297 50.7926H26.951C26.3885 50.7926 26.1895 50.3627 26.3369 49.4318L29.9821 26.3594C30.1246 25.4358 30.6306 24.9961 31.6377 24.9961Z"
                  fill="#009EE3"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M25.6847 17.0547H35.2104C37.8927 17.0547 41.0762 17.1407 43.2034 19.0198C44.6256 20.2749 45.3723 22.272 45.2004 24.4237C44.6158 31.6969 40.2656 35.772 34.4293 35.772H29.7328C28.932 35.772 28.4039 36.3026 28.1779 37.7371L26.8662 46.0886C26.7803 46.629 26.5469 46.9483 26.1293 46.9876H20.2513C19.6004 46.9876 19.3695 46.4964 19.539 45.4107L23.7639 18.6415C23.9334 17.5656 24.5253 17.0547 25.6847 17.0547Z"
                  fill="#113984"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M28.3154 36.8882L29.9784 26.3579C30.1233 25.4343 30.6293 24.9922 31.6364 24.9922H41.1523C42.7268 24.9922 44.0016 25.2378 44.9989 25.6922C44.0434 32.1672 39.8553 35.7632 34.3728 35.7632H29.6836C29.0548 35.7657 28.593 36.0801 28.3154 36.8882Z"
                  fill="#172C70"
                />
              </svg>
            </div>
            <div className="flex justify-between w-full items-center">
              <div className="">
                <div className="">
                  <p className="text-black font-Avenir text-base font-medium leading-[116.6%]">
                    PayPal
                  </p>
                  <div className="text-[#4B4B4B] mt-1 font-Lato text-sm font-normal leading-[164%]">
                    <p className="flex items-center">
                      <span className="text-[24px] pr-1">•</span> 1
                      business day
                    </p>
                    <p className="flex md:items-center">
                      <span className="text-[24px] pr-1">•</span> Paypal
                      fees may apply
                    </p>
                  </div>
                </div>
              </div>
              <div
                className= {` mb-20 w-6 h-6 border-2 rounded-full bg-gray-400 border-[#AFB5C1] cursor-pointer 
                ${
                  isChecked === "paypal" ?  " border-blue-400" : ""
                }
                  `}
              >
                <div
                  className={`rounded-full  w-5 h-5  border-[1px] border-white 
                  ${
                    isChecked === "paypal"  ? "bg-blue-400 w-5 h-5 border-[1px] border-white" : ""
                  }`
                }
                
                ></div>
              </div>
            </div>
          </div>
          <div className=" w-full md:w-[400px]  cursor-pointer mt-4 md:mt-0 md:h-[140px] h-[160px] flex md:gap-4 border border-[#AFB5C1] p-4 rounded-2xl"
                onClick={() => handleCheckboxClick("payoneer")}
                >
            <div className="">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="67"
                height="68"
                viewBox="0 0 67 68"
                fill="none"
              >
                <ellipse
                  opacity="0.1"
                  cx="33.2393"
                  cy="33.7209"
                  rx="32.92"
                  ry="33.3303"
                  fill="#58C0D0"
                />
                <path
                  d="M51.5317 48.679C53.6762 44.9122 52.8037 40.1493 49.4632 37.3874V32.0985H50.2744C50.7224 32.0985 51.0856 31.7353 51.0856 31.2873V28.8538H51.8968C52.3448 28.8527 52.7071 28.4887 52.706 28.0407C52.7057 27.9104 52.6741 27.7821 52.6137 27.6666C52.5533 27.5512 52.466 27.452 52.3591 27.3775L33.7021 14.3986C33.5663 14.3044 33.405 14.2539 33.2397 14.2539C33.0744 14.2539 32.9131 14.3044 32.7773 14.3986L14.1203 27.3775C13.9789 27.4761 13.8726 27.6173 13.8169 27.7806C13.7611 27.9438 13.7589 28.1205 13.8104 28.2851C13.862 28.4497 13.9646 28.5935 14.1035 28.6958C14.2424 28.798 14.4102 28.8534 14.5827 28.8538H15.3939V31.2873C15.3939 31.7353 15.7571 32.0985 16.2051 32.0985H17.0163V45.0773H16.205C15.757 45.0773 15.3938 45.4405 15.3938 45.8885V48.4681C14.9205 48.6354 14.5105 48.9451 14.22 49.3546C13.9296 49.7641 13.7729 50.2535 13.7715 50.7555V52.3778C13.7715 52.8258 14.1347 53.189 14.5827 53.189H51.8968C52.3448 53.189 52.708 52.8258 52.708 52.3778V50.7555C52.7068 50.3375 52.5979 49.9269 52.3919 49.5631C52.1859 49.1994 51.8896 48.8949 51.5317 48.679ZM49.4632 30.4762H42.9738V28.8538H49.4632V30.4762ZM47.8409 32.0985V36.3247C46.8298 35.8054 45.7282 35.4859 44.5961 35.3838V32.0985H47.8409ZM33.2397 16.0534L49.3091 27.2314H17.1703L33.2397 16.0534ZM24.3168 45.0773H23.5056V32.0985H24.3168C24.7648 32.0985 25.128 31.7353 25.128 31.2873V28.8538H28.3727V31.2873C28.3727 31.7353 28.7359 32.0985 29.1839 32.0985H29.9951V45.0773H29.1839C28.7359 45.0773 28.3727 45.4405 28.3727 45.8885V48.3221H25.1279V45.8885C25.1279 45.4405 24.7648 45.0773 24.3168 45.0773ZM29.995 46.6997H35.2109C35.3705 47.2602 35.5852 47.8036 35.8517 48.322H29.9951V46.6997H29.995ZM31.6174 45.0773V32.0985H34.8621V45.0773H31.6174ZM29.995 30.4762V28.8538H36.4844V30.4762H29.995ZM36.4844 32.0985H37.2956C37.7436 32.0985 38.1068 31.7353 38.1068 31.2873V28.8538H41.3516V31.2873C41.3516 31.7353 41.7148 32.0985 42.1628 32.0985H42.9738V35.3838C41.6838 35.5024 40.435 35.9006 39.3144 36.5506C38.1937 37.2005 37.228 38.0867 36.4844 39.1476V32.0985ZM17.0162 28.8538H23.5056V30.4761H17.0162V28.8538ZM21.8833 32.0985V45.0773H18.6385V32.0985H21.8833ZM17.0162 46.6997H23.5056V48.322H17.0162V46.6997ZM15.3938 51.5667V50.7555C15.3938 50.3075 15.757 49.9443 16.205 49.9443H36.9062C37.4181 50.5611 38.0101 51.1067 38.6665 51.5667L15.3938 51.5667ZM51.0856 51.5667H48.8954C49.5273 51.1254 50.0998 50.6047 50.5989 50.0174C50.7427 50.0802 50.8653 50.1833 50.9517 50.3144C51.038 50.4454 51.0846 50.5987 51.0856 50.7556V51.5667ZM43.785 51.5667C39.753 51.5667 36.4844 48.2981 36.4844 44.2661C36.4844 40.2342 39.753 36.9656 43.785 36.9656C47.817 36.9656 51.0856 40.2342 51.0856 44.2661C51.0856 48.2981 47.817 51.5667 43.785 51.5667Z"
                  fill="#58C0D0"
                />
                <path
                  d="M42.6818 24.1409L33.7589 17.6515L32.7611 18.9656L39.6642 23.9868H25.1279V25.6091H42.1626C42.5283 25.627 42.8607 25.3977 42.9738 25.0494C43.0274 24.8865 43.028 24.7109 42.9756 24.5476C42.9231 24.3844 42.8203 24.242 42.6818 24.1409ZM45.4074 40.2103H44.5961V38.588H42.9738V40.2103H42.1626C41.2666 40.2103 40.5403 40.9366 40.5403 41.8326V43.4549C40.5403 44.3509 41.2666 45.0773 42.1626 45.0773H45.4074V46.6996H42.1626V45.8884H40.5403V46.6996C40.5403 47.5956 41.2666 48.3219 42.1626 48.3219H42.9738V49.9442H44.5961V48.3219H45.4074C46.3033 48.3219 47.0297 47.5956 47.0297 46.6996V45.0773C47.0297 44.1813 46.3033 43.4549 45.4074 43.4549H42.1626V41.8326H45.4074V42.6438H47.0297V41.8326C47.0297 40.9366 46.3033 40.2103 45.4074 40.2103Z"
                  fill="#58C0D0"
                />
              </svg>
            </div>
            <div className="flex justify-between w-full items-center">
              <div className="">
                <div className="">
                  <p className="text-black font-Avenir text-base font-medium leading-[116.6%]">
                    Payoneer
                  </p>
                  <div className="text-[#4B4B4B] mt-1 font-Lato text-sm font-normal leading-[164%]">
                    <p className="flex md:items-center">
                      <span className="text-[24px] pr-1">•</span> Prepaid
                      debit Mastercard
                    </p>
                    <p className="flex md:items-center">
                      <span className="text-[24px] pr-1">•</span> 24 hours
                      or less
                    </p>
                    <p className="flex md:items-center">
                      <span className="text-[24px] pr-1">•</span> Payoneer
                      fees may apply
                    </p>
                  </div>
                </div>
              </div>
              <div
                className= {` mb-20 w-6 h-6 border-2 rounded-full bg-gray-400 border-[#AFB5C1] cursor-pointer 
                ${
                  isChecked === "payoneer" ?  " border-blue-400" : ""
                }
                  `}
              >
                <div
                  className={`rounded-full  w-5 h-5  border-[1px] border-white 
                  ${
                    isChecked === "payoneer"  ? "bg-blue-400 w-5 h-5 border-[1px] border-white" : ""
                  }`
                }
                ></div>
              </div>
            </div>
          </div>
        </div>

        {loginAs === "Host" &&
        <div className="flex w-full md:w-1/2  gap-6 my-4">
          <div className=" w-full md:mr-2 md:w-[400px]  cursor-pointer md:h-[140px] h-[160px] flex gap-4 border border-[#AFB5C1] p-4 rounded-2xl"
                onClick={() => handleCheckboxClick("antonio")}
                >
            <div className="">
              <img src={paycardss} alt="img here"></img>
            </div>
            <div className="flex justify-between w-full items-center">
              <div className="">
                <div className="">
                  <p className="text-black font-Avenir text-base font-medium leading-[116.6%]">
                    Antonio Tony Points
                  </p>
                  <div className="text-[#4B4B4B] mt-1 font-Lato text-sm font-normal leading-[164%]">
                    <p className="flex items-center">Earning Points</p>

                    <p className="flex items-center mt-3 text-[#2459BF] font-Lato text-base font-normal leading-[164%]">
                      550 Points
                    </p>
                  </div>
                </div>
              </div>
              <div
                className= {` mb-20 w-6 h-6 border-2 rounded-full bg-gray-400 border-[#AFB5C1] cursor-pointer 
                ${
                  isChecked === "antonio" ?  " border-blue-400" : ""
                }
                  `}
              >
                <div
                  className={`rounded-full  w-5 h-5  border-[1px] border-white 
                  ${
                    isChecked === "antonio"  ? "bg-blue-400 w-5 h-5 border-[1px] border-white" : ""
                  }`
                }
                ></div>
              </div>
            </div>
          </div>
        </div>
        }
        <div className="w-full md:w-[20%] pt-4" onClick={()=>setViewStepTwo(true)}>
          <ButtonFilled onClick={payoutstep2} text="Continue" />
        </div>
      </div>
    </div>
  </div>
  )
}
