import React, { useEffect, useMemo, useCallback, useState } from "react";
import Container from "@mui/material/Container";
import { Link, useLocation } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import Rating from "react-rating";
import { FaStar, FaStarHalfAlt, FaRegStar } from "react-icons/fa";
import { localStorageData } from "services/auth/localStorageData";
import Grid from "@mui/material/Grid";
import { ButtonWithoutBackground } from "common/buttons/buttonWithoutBackgourd";
import userServices from "services/httpService/userAuth/userServices";
import { toast } from "react-toastify";
import { useMutation } from "react-query";
import ErrorService from "services/formatError/ErrorService";
import dayjs from "dayjs";
import GuestAndRoomSelector from "components/GuestAndRoomSelector/GuestAndRoomSelector";
import { PaymentCard } from "components/Cards/PaymentCard";
import visaIcon from "assets/img/cardTypes.jpg";
import moment from "moment";
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md";
import { FaSpinner } from "react-icons/fa";
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { TextField } from "@mui/material";


// Extract reusable components
const PaymentCardList = React.memo(({ savedCards, selectedCard, onCardSelect, onCardRemove }) => (
  <div className="w-full flex flex-col gap-4">
    {savedCards.length > 0 ? (
      savedCards.map((card, index) => (
        <label
          key={card._id}
          className={`relative flex flex-col w-full md:w-[600px] p-3 border-2 rounded-xl shadow-lg transition-all cursor-pointer 
            ${selectedCard?._id === card._id
              ? "bg-gradient-to-r from-blue-500 to-blue-600 border-blue-700 text-white shadow-xl"
              : "bg-white border-gray-300 text-black hover:border-blue-400 hover:shadow-md"
            }`}
        >
          <input
            type="radio"
            name="selectedCard"
            checked={selectedCard?._id === card._id}
            onChange={() => onCardSelect(card)}
            className="hidden"
          />
          <PaymentCard
            name={card.cardName || "Unknown Card"}
            icon={visaIcon}
            number={card.cardNumber || "**** XXXX"}
            verification={index === 0 ? "Primary" : "Secondary"}
            cardType={card.cardType || "Unknown"}
            funding={card.funding || "N/A"}
            country={card.country || "N/A"}
          />
          <button
            className={`mt-3 px-4 py-2 rounded-lg text-sm font-medium transition-all
              ${selectedCard?._id === card._id
                ? "bg-red-500 text-white hover:bg-red-600"
                : "bg-red-500 text-black hover:bg-red-600"
              }`}
            onClick={(e) => {
              e.stopPropagation();
              onCardRemove(card._id);
            }}
          >
            Remove
          </button>
        </label>
      ))
    ) : (
      <p className="text-gray-500">
        No cards saved. Add a card to manage payments.
      </p>
    )}
  </div>
));

const ReservationSummary = React.memo(({
  propertyDetail,
  offerState,
  isDropdownOpen,
  toggleDropdown,
  checkIn,
  checkOut,
  averageRating,
  selectedCard
}) => {
  // Get last 4 digits of the selected card number
  const lastFourDigits = selectedCard?.cardNumber
    ? selectedCard.cardNumber.replace(/\s+/g, "").slice(-4)
    : "----";

  return (
    <div className="flex mt-2 reserveDetailWrapper">
      <div className="h-full md:mt-20 mt-0 md:ml-4 ml-0 border border-[#C1E1C2] height-line rounded-xl">
        <div className="reserveImage">
          <img
            src={propertyDetail.pics?.[0] || require("assets/img/background.png")}
            className="object-cover w-full h-52 rounded-t-xl"
            alt="Property"
          />
        </div>

        <div className="items-center h-full">
          <div className="flex flex-col items-start justify-between propertyName">
            <p className="flex items-center pl-3 pt-3">
              {averageRating > 0 ? (
                <div className="flex items-center gap-2">
                  <Rating
                    initialRating={averageRating}
                    readonly
                    emptySymbol={<FaRegStar color="#d3d3d3" />}
                    halfSymbol={<FaStarHalfAlt color="#ffd700" />}
                    fullSymbol={<FaStar color="#ffd700" />}
                    fractions={2}
                  />
                  <span>({averageRating})</span>
                </div>
              ) : (
                <span className="text-[#AFB5C1]">Not Rated Yet.</span>
              )}
            </p>
            <p className="my-1 ml-3 text-2xl font-semibold">
              {propertyDetail.title}
            </p>
          </div>

          <div className="flex items-center justify-between mt-8 font-semibold propertyName">
            <div className="flex items-center gap-x-1">
              <p className="ml-3 font-md">
                {checkOut.diff(checkIn, 'day')} Nights
              </p>
              <button onClick={toggleDropdown}>
                {isDropdownOpen ? (
                  <MdKeyboardArrowUp className="ml-2 text-black text-xl" />
                ) : (
                  <MdKeyboardArrowDown className="ml-2 text-black text-xl" />
                )}
              </button>
            </div>
            <p className="flex items-center mr-3">
              {offerState.total || 0} points
            </p>
          </div>

          {isDropdownOpen && (
            <ul className="pl-4 mt-2">
              {offerState.pointsPerNight
                .filter((p) => {
                  const pointDate = new Date(p.date);
                  return (
                    pointDate >= new Date(checkIn) &&
                    pointDate <= new Date(checkOut)
                  );
                })
                .map((p, index) => (
                  <li key={index} className="text-sm text-gray-600">
                    {moment(new Date(p.date)).format("DD MMMM")} / {p.point} Points
                  </li>
                ))}
            </ul>
          )}

          {/* Total Points Section */}
          <div className="px-3 mt-6 text-green-700 font-semibold text-lg">
            Total Payment: {offerState.points || 0} Points
            <span className="ml-3 inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
              Paid with Points
            </span>
          </div>

          {/* Small print service fee */}
          {offerState.serviceFee > 0 && (
            <p className="px-3 text-xs text-gray-500 mt-1 italic">
              * A 5% service fee (${offerState.serviceFee.toFixed(2)}) will be charged separately to your card ending in •••• {lastFourDigits}
            </p>
          )}

          <p className="px-3 text-sm text-gray-500 mt-1 mb-2 italic">
            You are paying for this reservation entirely using your points.
          </p>
        </div>
      </div>
    </div>
  );
});

// Date Picker component with loading state
const DatePickerWithLoader = React.memo(({
  value,
  onChange,
  minDate,
  disabledDates,
  placeholder,
  isLoading,
  slotProps
}) => (
  <div className="relative">
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        value={value}
        onChange={onChange}
        minDate={minDate}
        shouldDisableDate={(date) => disabledDates.includes(date.toISOString())}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder={placeholder}
            disabled={isLoading}
            fullWidth
            sx={{
              minWidth: 220,
              maxWidth: 350,
              backgroundColor: "#fff",
              borderRadius: 2,
              fontSize: 18,
            }}
            InputProps={{
              ...params.InputProps,
              className: "text-lg font-semibold border-gray-300 focus:border-blue-400 transition px-3 py-2",
              style: { minHeight: 48 }
            }}
            inputProps={{
              ...params.inputProps,
              style: { fontSize: 18, letterSpacing: 1 }
            }}
          />
        )}
      />
    </LocalizationProvider>
    {isLoading && (
      <div className="absolute top-0 right-0 h-full flex items-center pr-2">
        <FaSpinner className="animate-spin text-blue-500" />
      </div>
    )}
  </div>
));

function parseDate(dateValue) {
  if (!dateValue) return null;
  // If it's already a Date object, return as is
  if (dateValue instanceof Date) return dateValue;
  // If it's a string, try to parse as ISO or YYYY-MM-DD
  if (typeof dateValue === 'string') {
    // If only date, add T00:00:00 to ensure it's parsed as local
    if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateValue)) {
      return new Date(dateValue + 'T00:00:00');
    }
    return new Date(dateValue);
  }
  // If it's an object with $d (from dayjs), use that
  if (dateValue.$d) return new Date(dateValue.$d);
  return null;
}

function formatDate(date) {
  if (!date || isNaN(date.getTime())) return 'Invalid Date';
  const options = { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' };
  return date.toLocaleDateString('en-US', options);
}

function formatTime(date) {
  if (!date || isNaN(date.getTime())) return 'Invalid Time';
  const hour = date.getHours();
  const minute = date.getMinutes();
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;
  const formattedMinute = `${minute < 10 ? '0' : ''}${minute}`;
  return `${formattedHour}:${formattedMinute}${ampm}`;
}

function ReserveDetails() {
  const currentDate = dayjs();

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const { state: locationState } = location;
  const [paymentType, setPaymentType] = useState("");
  console.log(locationState)
  // Fix the date initialization - properly use dayjs for dates
  const [checkIn, setCheckIn] = useState(() => {
    if (locationState?.offerState?.checkIn) {
      // Ensure date is properly parsed by dayjs
      return dayjs(locationState.offerState.checkIn.$d || locationState.offerState.checkIn);
    }
    return dayjs();
  });

  const [checkOut, setCheckOut] = useState(() => {
    if (locationState?.offerState?.checkOut) {
      // Ensure date is properly parsed by dayjs
      return dayjs(locationState.offerState.checkOut.$d || locationState.offerState.checkOut);
    }
    return dayjs().add(1, 'day');
  });

  const [points, setPoints] = useState(0);
  const [cardName, setCardName] = useState("");
  const [existCardNumber, setExistCardNumber] = useState("");
  const [loading, setLoading] = useState(false);
  const stripe = useStripe();
  const elements = useElements();
  const [guests, setGuests] = useState(locationState?.offerState?.guest || 1);
  const [rooms, setRooms] = useState(locationState?.offerState?.room || 1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const navigate = useNavigate();
  const [allPost, setallPost] = React.useState([]);

  // Helper function to convert object to query string
  const objectToQueryString = (obj) => {
    return Object.keys(obj)
      .map((key) => {
        const value = obj[key];
        if (typeof value === 'object' && value !== null) {
          // Recursively serialize nested objects if needed, or flatten them
          // For simplicity, this example will skip complex nested objects or stringify them
          // A more robust solution might involve a library like 'qs'
          return `${encodeURIComponent(key)}=${encodeURIComponent(JSON.stringify(value))}`;
        }
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      })
      .join("&");
  };

  // State for date loading indicators
  const [dateLoading, setDateLoading] = useState({
    checkIn: false,
    checkOut: false
  });

  const [savedCards, setSavedCards] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);

  const [isSelectVisible, setIsSelectVisible] = useState({
    selectData: false,
    selectGuest: false,
  });
  const [showAddCard, setShowAddCard] = useState(false);
  const [tripe, setTripe] = useState({
    message: "",
    phoneNumber: "",
    showTextarea: false,
    showPhoneField: false
  });

  // State for API payload, initialized with data from location state
  const [apiPayload, setApiPayload] = useState({
    offerState: {},
    paymentType: "",
    property_id: "",
    propertyUserId: "",
    state: locationState,
    tripe: {
      message: "",
      phoneNumber: "",
    },
    cardId: "",
    userId: localStorageData("_id"),
  });

  const [dynamicOfferState, setDynamicOfferState] = useState(locationState.offerState);

  const toggleDropdown = () => {
    setIsDropdownOpen((prev) => !prev);
  };

  const handleGuestChange = useCallback((action) => {
    if (action === "increment" && guests < locationState?.propertyDetail?.spaceTypeDetail?.guests) {
      setGuests(prev => prev + 1);
    } else if (action === "decrement" && guests > 1) {
      setGuests(prev => prev - 1);
    }
  }, [guests, locationState?.propertyDetail?.spaceTypeDetail?.guests]);

  const handleRoomChange = useCallback((action) => {
    if (action === "increment" && rooms < locationState?.propertyDetail?.spaceTypeDetail?.bedrooms) {
      setRooms(prev => prev + 1);
    } else if (action === "decrement" && rooms > 1) {
      setRooms(prev => prev - 1);
    }
  }, [rooms, locationState?.propertyDetail?.spaceTypeDetail?.bedrooms]);

  const { mutate: fetchCards } = useMutation(
    () => userServices.userById(`/userAuth/user/${localStorageData("_id")}`),
    {
      onError: (error) => toast.error(ErrorService.uniformError(error)),
      onSuccess: (data) => {
        const fetchedCards = data?.data?.cardInfo?.slice(0, 2) || [];
        setSavedCards(fetchedCards);
      },
    }
  );

  const { mutate: removeCard } = useMutation(
    (cardId) =>
      userServices.Reservation("/stripe/removeCard", {
        cardId,
        userId: localStorageData("_id"),
      }),
    {
      onError: (error) => {
        console.log("Failed to remove card", error)
        toast.error("Failed to remove card")
      },
      onSuccess: () => {
        toast.success("Card removed successfully!");
        fetchCards();
      },
    }
  );

  const { mutate, isLoading: LoadingReservationConfirm } = useMutation(
    (reservationData) =>
      userServices.Reservation("/reservation/reservation", reservationData),

    {
      onError: (error) => {
        const errorMessage =
          error?.response?.data?.error || ErrorService.uniformError(error);
        if (errorMessage === "Payment method ID is missing for this card") {
          toast.error("The selected card is invalid. Please add a new card.");
          setShowAddCard(true);
        } else {
          toast.error(errorMessage);
        }
      },
      onSuccess: (data) => {
        toast.success("Reservation created successfully");
        navigate(`/ConfirmReservation`, {
          state: {
            property_id: locationState.propertyDetail._id,
            propertyDetail: locationState.propertyDetail,
            offerState: {
              checkIn: checkIn.format("YYYY-MM-DD"),
              checkOut: checkOut.format("YYYY-MM-DD"),
              guest: guests,
              room: rooms,
              points: dynamicOfferState.points,
              serviceFee: dynamicOfferState.serviceFee,
              total: dynamicOfferState.total
            },
            reservationData: data.data // Include the response data from the reservation API
          }
        });
      },
    }
  );

  const { mutate: addOrUpdateCard } = useMutation(
    (reservationData) =>
      userServices.Reservation("/stripe/AddCardInfo", reservationData),
    {
      onError: (error) => {
        toast.error("Your card was declined.");
        setLoading(false);
      },
      onSuccess: () => {
        toast.success("Your card was added successfully!");
        setShowAddCard(false);
        passMutate();
      },
    }
  );

  const handleAddCard = async () => {
    setLoading(true);
    if (!stripe || !elements) {
      toast.error("Stripe has not loaded properly.");
      setLoading(false);
      return;
    }

    if (!cardName.trim()) {
      toast.error("Please enter the name on the card.");
      setLoading(false);
      return;
    }

    const cardNumberElement = elements.getElement(CardNumberElement);
    const cardExpiryElement = elements.getElement(CardExpiryElement);
    const cardCvcElement = elements.getElement(CardCvcElement);

    if (!cardNumberElement || !cardExpiryElement || !cardCvcElement) {
      toast.error("Invalid card details.");
      setLoading(false);
      return;
    }

    const { token, error } = await stripe.createToken(cardNumberElement);

    if (error) {
      toast.error(`Card error: ${error.message}`);
      setLoading(false);
      return;
    }

    try {
      await addOrUpdateCard({
        stripeToken: token.id,
        userId: localStorageData("_id"),
        cardName: cardName,
      });
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCardNameChange = (e) => {
    setCardName(e.target.value);
  };

  const { mutate: passMutate, isLoading: CardLoading } = useMutation(
    () => userServices.userById(`/userAuth/user/${localStorageData("_id")}`),
    {
      onError: (error) => {
        toast.error(ErrorService.uniformError(error));
      },
      onSuccess: (data) => {
        if (data?.data?.cardInfo?.length > 0) {
          const fetchedCards = data.data.cardInfo.slice(0, 2);
          setSavedCards(fetchedCards);

          setSelectedCard(fetchedCards[0]);
          setApiPayload((prev) => ({
            ...prev,
            cardId: fetchedCards[0]._id,
          }));
        } else {
          setSavedCards([]);
          setSelectedCard(null);
          setApiPayload((prev) => ({ ...prev, cardId: "" }));
        }
      },
    }
  );

  const onClickConfirm = () => {
    if (!selectedCard || savedCards.length === 0) {
      setShowAddCard(true)
      toast.error("Add your payment details now to confirm your stay.")
      return;
    }

    // Format dates properly for the API
    const formattedCheckIn = dayjs(checkIn).format("YYYY-MM-DD");
    const formattedCheckOut = dayjs(checkOut).format("YYYY-MM-DD");

    // Create the API payload with properly formatted dates
    const apiPayloadWithDates = {
      ...apiPayload,
      offerState: {
        ...apiPayload.offerState,
        checkIn: formattedCheckIn,
        checkOut: formattedCheckOut,
        guest: guests,
        room: rooms,
        count: checkOut.diff(checkIn, 'day'),
        total: dynamicOfferState.total,
        points: dynamicOfferState.points,
        serviceFee: dynamicOfferState.serviceFee
      },
      state: undefined
    };

    mutate(apiPayloadWithDates);
  };

  useEffect(() => {
    const sum = locationState?.offerState?.count * locationState?.propertyDetail?.points;
    setPoints(sum);
  }, []);

  const handleClick = () => {
    const formattedCheckIn = dayjs(checkIn).format("YYYY-MM-DD");
    const formattedCheckOut = dayjs(checkOut).format("YYYY-MM-DD");
    const queryString = `?check_in=${formattedCheckIn}&check_out=${formattedCheckOut}&guest=${guests}&room=${rooms}`;
    navigate(`/room/${locationState.propertyDetail._id}${queryString}`);
  };

  const handleAddButtonClick = (field) => {
    setTripe((prevTripe) => ({
      ...prevTripe,
      [field]: !prevTripe[field],
    }));
  };

  // Fix API payload to use proper date formats
  useEffect(() => {
    if (locationState) {
      setApiPayload((prevApiPayload) => ({
        ...prevApiPayload,
        offerState: {
          ...locationState.offerState,
          checkIn: checkIn.toDate(),
          checkOut: checkOut.toDate(),
        },
        property_id: locationState.propertyDetail._id,
        paymentType: paymentType,
        propertyUserId: locationState.propertyDetail.userId,
        tripe: { message: tripe.message, phoneNumber: tripe.phoneNumber },
      }));
    }
  }, [locationState, checkIn, checkOut, paymentType, tripe]);

  // Format the dates for display
  const formattedCheckInDate = checkIn.format("MMM D");
  const formattedCheckOutDate = checkOut.format("MMM D");

  useEffect(() => {
    if (savedCards.length > 0) {
      setSelectedCard(savedCards[0]);
    }
  }, [savedCards]);

  const handleButtonClick = (buttonName) => {
    setIsSelectVisible((prevVisibility) => ({
      ...prevVisibility,
      [buttonName]: !prevVisibility[buttonName],
    }));
  };

  // Fixed date change handlers
  const handleCheckInChange = (newValue) => {
    setDateLoading(prev => ({ ...prev, checkIn: true }));

    // Set the new value directly as a dayjs object
    setCheckIn(newValue);

    // Update URL with new date parameters
    setTimeout(() => {
      const formattedCheckIn = newValue.format("YYYY-MM-DD");

      // Calculate new URL parameters
      const newParams = new URLSearchParams(location.search);
      newParams.set("check_in", formattedCheckIn);

      // Replace URL without refreshing the page
      const newUrl = `${location.pathname}?${newParams.toString()}`;
      window.history.replaceState({ ...window.history.state }, "", newUrl);

      setDateLoading(prev => ({ ...prev, checkIn: false }));
    }, 800); // Simulating a network request time
  };

  const handleCheckOutChange = (newValue) => {
    setDateLoading(prev => ({ ...prev, checkOut: true }));

    // Set the new value directly as a dayjs object
    setCheckOut(newValue);

    // Update URL with new date parameters
    setTimeout(() => {
      const formattedCheckOut = newValue.format("YYYY-MM-DD");

      // Calculate new URL parameters
      const newParams = new URLSearchParams(location.search);
      newParams.set("check_out", formattedCheckOut);

      // Replace URL without refreshing the page
      const newUrl = `${location.pathname}?${newParams.toString()}`;
      window.history.replaceState({ ...window.history.state }, "", newUrl);

      setDateLoading(prev => ({ ...prev, checkOut: false }));
    }, 800); // Simulating a network request time
  };

  const handleUpdateClick = useCallback(() => {
    // Convert to JavaScript dates for calculations
    const checkInDate = checkIn.toDate();
    const checkOutDate = checkOut.toDate();
    const count = Math.ceil((checkOutDate - checkInDate) / (24 * 60 * 60 * 1000));

    const pointsArray = Array.isArray(locationState.offerState.pointsPerNight)
      ? locationState.offerState.pointsPerNight
      : [];

    const updatedTotalPoints = pointsArray.reduce((acc, p) => {
      const pointDate = new Date(p.date);
      if (pointDate >= checkInDate && pointDate <= checkOutDate) {
        return acc + p.point;
      }
      return acc;
    }, 0);

    const serviceFee = updatedTotalPoints * 0.05;

    const updatedState = {
      ...locationState.offerState,
      checkIn,
      checkOut,
      guest: guests,
      room: rooms,
      count,
      total: updatedTotalPoints,
      points: updatedTotalPoints,
      serviceFee,
    };

    setDynamicOfferState(updatedState);

    setApiPayload(prev => ({
      ...prev,
      offerState: updatedState,
    }));

    // Update URL with all new parameters
    const formattedCheckIn = checkIn.format("YYYY-MM-DD");
    const formattedCheckOut = checkOut.format("YYYY-MM-DD");

    const newParams = new URLSearchParams(location.search);
    newParams.set("check_in", formattedCheckIn);
    newParams.set("check_out", formattedCheckOut);
    newParams.set("guest", guests.toString());
    newParams.set("room", rooms.toString());

    const newUrl = `${location.pathname}?${newParams.toString()}`;
    window.history.replaceState({ ...window.history.state }, "", newUrl);

    setIsSelectVisible({
      selectData: false,
      selectGuest: false,
    });
  }, [checkIn, checkOut, guests, rooms, locationState?.offerState, location.pathname, location.search]);

  useEffect(() => {
    passMutate();
  }, []);

  const [averageRating, setAverageRating] = useState(0);

  // Calculating average rating and reviews
  useEffect(() => {
    if (
      locationState?.propertyDetail.reviews &&
      locationState.propertyDetail.reviews.length > 0
    ) {
      const ratings = locationState.propertyDetail.reviews.map(
        (review) => review.rating
      );
      const total = ratings.reduce((acc, rating) => acc + rating, 0);
      const average = total / ratings.length;
      setAverageRating(Number(average.toFixed(1)));
    } else {
      setAverageRating(0);
    }
  }, [locationState?.propertyDetail.reviews]);

  // Memoize expensive calculations
  const averageRatingMemo = useMemo(() => {
    if (!locationState?.propertyDetail?.reviews?.length) return 0;
    const ratings = locationState.propertyDetail.reviews.map(review => review.rating);
    const total = ratings.reduce((acc, rating) => acc + rating, 0);
    return Number((total / ratings.length).toFixed(1));
  }, [locationState?.propertyDetail?.reviews]);

  // Memoize handlers
  const handleCardSelection = useCallback((card) => {
    setSelectedCard(card);
    setApiPayload(prev => ({
      ...prev,
      cardId: card._id,
    }));
  }, []);



  return (
    <>
      <Container maxWidth="xl">
        <Container maxWidth="xl">
          <Grid container spacing={2}>
            <Grid item lg={8} xs={12}>
              <div className="w-full ">
                <div className="flex h-24 item-center">
                  <div className="flex items-center ">
                    <i
                      className="cursor-pointer fas fa-angle-left"
                      onClick={handleClick}
                    ></i>
                    <h4 className="ml-6 text-2xl font-semibold">
                      Confirm And Pay
                    </h4>
                  </div>
                </div>

                <div className="mt-4">
                  <h4 className="mb-2 ml-1 text-lg font-semibold">
                    Stay Duration
                  </h4>
                  <div className="flex items-center justify-between ">
                    <h4 className="ml-1 font-semibold text-md">Dates</h4>
                    {isSelectVisible.selectData ? (
                      <button
                        onClick={handleUpdateClick}
                        className="text-color-yellow"
                      >
                        Update
                      </button>
                    ) : (
                      <ButtonWithoutBackground
                        onClick={() => handleButtonClick("selectData")}
                        text="Edit"
                      />
                    )}
                  </div>

                  {isSelectVisible.selectData ? (
                    <div className="flex w-full bg-[#f8fafc] border border-gray-200 rounded-xl shadow-md p-4 gap-4">
                      <div className="flex-1">
                        <label className="block text-xs text-gray-500 mb-1">Check-in</label>
                        <DatePickerWithLoader
                          value={checkIn}
                          onChange={handleCheckInChange}
                          minDate={currentDate}
                          disabledDates={locationState.propertyDetail.bookDates || []}
                          placeholder="Check-in"
                          isLoading={dateLoading.checkIn}
                          slotProps={{
                            textField: {
                              placeholder: "Check-in",
                              disabled: dateLoading.checkIn,
                              InputProps: {
                                className: "w-full text-lg font-semibold rounded-lg border border-gray-300 focus:border-blue-400 transition",
                                style: { background: "#fff" }
                              }
                            }
                          }}
                        />
                      </div>
                      <div className="flex-1">
                        <label className="block text-xs text-gray-500 mb-1">Check-out</label>
                        <DatePickerWithLoader
                          value={checkOut}
                          onChange={handleCheckOutChange}
                          minDate={checkIn || currentDate}
                          disabledDates={locationState.propertyDetail.bookDates || []}
                          placeholder="Check-out"
                          isLoading={dateLoading.checkOut}
                          slotProps={{
                            textField: {
                              placeholder: "Check-out",
                              disabled: dateLoading.checkOut,
                              InputProps: {
                                className: "w-full text-lg font-semibold rounded-lg border border-gray-300 focus:border-blue-400 transition",
                                style: { background: "#fff" }
                              }
                            }
                          }}
                        />
                      </div>
                    </div>
                  ) : (
                    <p className="ml-1 text-sm">
                      {formattedCheckInDate} - {formattedCheckOutDate}
                    </p>
                  )}
                </div>
                <div className="mt-2">
                  <div className="flex items-center justify-between">
                    <h4 className="ml-1 font-semibold text-md">
                      Guests & Rooms
                    </h4>
                    {isSelectVisible.selectGuest ? (
                      <button
                        onClick={handleUpdateClick}
                        className="text-color-yellow"
                      >
                        Update
                      </button>
                    ) : (
                      <ButtonWithoutBackground
                        onClick={() => handleButtonClick("selectGuest")}
                        text="Edit"
                      />
                    )}
                  </div>

                  <p className="ml-1 text-sm">
                    {isSelectVisible.selectGuest
                      ? "Selected option"
                      : `${guests} guests & ${rooms} rooms`}
                  </p>
                  <div className="w-full my-3">
                    {isSelectVisible.selectGuest && (
                      <GuestAndRoomSelector
                        handleGuestChange={handleGuestChange}
                        handleRoomChange={handleRoomChange}
                        guests={guests}
                        rooms={rooms}
                        isGuestIncrementDisabled={guests >= (locationState?.propertyDetail?.spaceTypeDetail?.guests || 1)}
                        isRoomIncrementDisabled={rooms >= (locationState?.propertyDetail?.spaceTypeDetail?.bedrooms || 1)}
                      />
                    )}
                  </div>
                </div>

                <hr className="my-6 border-[#C1E1C2] " />

                <div className="">
                  <h4 className="mt-2 mb-2 text-lg font-semibold ">Payment</h4>
                  <PaymentCardList
                    savedCards={savedCards}
                    selectedCard={selectedCard}
                    onCardSelect={handleCardSelection}
                    onCardRemove={removeCard}
                  />

                  <div className="mt-4">
                    {!showAddCard && savedCards.length < 2 && (
                      <i
                        className="fa-solid fa-plus cursor-pointer text-blue-500 hover:text-blue-700 transition"
                        onClick={() => setShowAddCard(true)}
                      >
                        Add New Card
                      </i>
                    )}
                  </div>

                  {showAddCard && (
                    <div className="p-4 border rounded-lg border-[#C1E1C2] bg-white shadow-sm">
                      {/* Header with Close Icon */}
                      <div className="flex justify-between items-center">
                        <h4 className="text-lg font-semibold">
                          Credit/Debit Card
                        </h4>
                        <button
                          className="text-gray-500 hover:text-gray-700 transition"
                          onClick={() => setShowAddCard(false)}
                        >
                          <i className="fa-solid fa-x text-lg"></i>
                        </button>
                      </div>

                      <hr className="my-3 border-[#C1E1C2]" />

                      {/* Card Form */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Card Number */}
                        <div className="flex flex-col">
                          <label className="text-sm font-medium text-gray-700">
                            Card Number*
                          </label>
                          <CardNumberElement className="input-styl placeholder:text-gray-400" />
                        </div>

                        {/* Name on Card */}
                        <div className="flex flex-col">
                          <label className="text-sm font-medium text-gray-700">
                            Name on Card*
                          </label>
                          <input
                            type="text"
                            name="cardName"
                            placeholder="Name on card"
                            className="input-styl placeholder:text-gray-400"
                            value={cardName.toUpperCase()}
                            onChange={handleCardNameChange}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        {/* Expiration Date */}
                        <div className="flex flex-col">
                          <label className="text-sm font-medium text-gray-700">
                            Expiration Date*
                          </label>
                          <CardExpiryElement className="input-styl placeholder:text-gray-400" />
                        </div>

                        {/* CVC */}
                        <div className="flex flex-col">
                          <label className="text-sm font-medium text-gray-700">
                            CVC*
                          </label>
                          <CardCvcElement className="input-styl placeholder:text-gray-400" />
                        </div>
                      </div>

                      {/* Submit Button */}
                      <button
                        className="btn-styl-blue mt-4"
                        onClick={handleAddCard}
                        disabled={loading}
                      >
                        {loading ? "Adding Card..." : "Add Card"}
                      </button>
                    </div>
                  )}
                  <hr className="my-6 border-[#C1E1C2] " />
                  {/* <div>
                    <h4 className="mt-2 mb-2 text-lg font-semibold ">
                      Required for your trip
                    </h4>
                    <div className="flex items-center justify-between gap-5" >
                      <div>
                        <p className="text-md">Message the Host</p>
                        <p className="text-xs mb-2">
                          Let the Host know why you're traveling and when you'll
                          check in.
                        </p>
                        {tripe.showTextarea && ( // Render textarea only when showTextarea is true
                          <textarea
                            value={tripe.message}
                            onChange={(e) =>
                              setTripe({ ...tripe, message: e.target.value })
                            }
                            placeholder="Type something..."
                            className="w-full p-2 border border-color-grey rounded-xl"
                          />
                        )}
                      </div>
                      <button
                        className="px-4 py-1 text-sm border rounded-full text-color-yellow border-color-yellow"
                        onClick={() => handleAddButtonClick("showTextarea")}
                      >
                        {tripe.showTextarea ? "CLOSE" : "ADD"}
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-3">
                    <div>
                      <p className="text-md">Phone number</p>
                      <p className="text-xs mb-2">
                        Add and confirm your phone number to get trip updates.
                      </p>
                      {tripe.showPhoneField && (
                        <input
                          type="tel"
                          value={tripe.phoneNumber}
                          onChange={(e) =>
                            setTripe({ ...tripe, phoneNumber: e.target.value })
                          }
                          placeholder="Enter phone number..."
                          className="w-full p-2 border border-color-grey rounded-xl"
                        />
                      )}
                    </div>
                    <button
                      className="px-4 py-1 text-sm border rounded-full text-color-yellow border-color-yellow"
                      onClick={() => handleAddButtonClick("showPhoneField")}
                    >
                      {tripe.showPhoneField ? "CLOSE" : "ADD"}
                    </button>
                  </div> */}

                  <hr className="my-6 border-[#C1E1C2] " />
                  <div className="mt-6 rounded-xl border border-gray-200 bg-white shadow-sm p-5">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">Cancellation Policy</h4>

                    <p className="text-sm text-gray-600 mb-2">
                      Free cancellation within 48 hours. Cancel at least 15–30 days before your check-in date for a partial refund.
                    </p>
                    <Link to="/pricing" className="text-sm text-blue-600 underline hover:text-blue-800 mb-4 inline-block">
                      Read More
                    </Link>

                    {locationState?.testedProperty === true && (
                      <div className="bg-red-100 border border-red-300 text-red-700 text-sm rounded-md px-4 py-2 mb-4">
                        This property is for testing purposes only and is not available for booking.
                      </div>
                    )}

                    <p className="text-sm text-gray-700 mb-4">
                      By selecting the button below, I agree to the Host's House Rules, Ground Rules for Guests, and BnByond's refund and cancellation policies.
                    </p>

                    <button
                      className={`w-full py-3 rounded-md text-white font-semibold transition duration-300 ${locationState?.testedProperty === true || LoadingReservationConfirm
                          ? 'bg-gray-500 cursor-not-allowed'
                          : 'bg-[#1a73e8] hover:bg-[#155ab6]'
                        }`}
                      onClick={onClickConfirm}
                      disabled={locationState?.testedProperty === true || LoadingReservationConfirm}
                    >
                      {LoadingReservationConfirm ? "Reserving..." : "Reserve"}
                    </button>
                  </div>



                </div>
              </div>
            </Grid>
            <Grid item lg={4} xs={12}>
              <ReservationSummary
                savedCards={savedCards}
                propertyDetail={locationState.propertyDetail}
                offerState={dynamicOfferState}
                isDropdownOpen={isDropdownOpen}
                toggleDropdown={toggleDropdown}
                onCardSelect={handleCardSelection}
                checkIn={checkIn}
                checkOut={checkOut}
                averageRating={averageRatingMemo}
                selectedCard={selectedCard}
              />
            </Grid>
          </Grid>
        </Container>
      </Container>
      <br />
    </>
  );
}

export default React.memo(ReserveDetails);
