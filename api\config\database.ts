import mongoose from "mongoose";

if (!process.env.MONGO_URI) {
  console.error('MONGO_URI is not defined in environment variables!');
  process.exit(1);
}

export const connect = async () => {
    try {
      await mongoose.connect(process.env.MONGO_URI);
      console.log('MongoDB Connected Successfully');

      mongoose.connection.on('disconnected', () => {
        console.log('MongoDB disconnected');
      });
          
      mongoose.connection.on('error', (err) => {
        console.error('MongoDB connection error:', err);
      });
    } catch (error) {
      console.error('Error connecting to MongoDB:', error);
      throw error;
    }
};
