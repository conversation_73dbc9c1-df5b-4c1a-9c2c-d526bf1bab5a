import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { logPageView, logTimeOnPage } from "./Analytics";

const RouteChangeTracker = () => {
    const location = useLocation();
    const [startTime, setStartTime] = useState(Date.now());

    useEffect(() => {
        const handlePageChange = () => {
            const duration = Date.now() - startTime;
            logTimeOnPage(location.pathname, duration);

            logPageView(location.pathname);
            setStartTime(Date.now());
        };

        handlePageChange();
    }, [location]);

    return null;
};

export default RouteChangeTracker;
