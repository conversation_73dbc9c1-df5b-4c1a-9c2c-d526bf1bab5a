# Dependencies
**/node_modules
/.pnp
.pnp.js

# Testing
**/coverage
**/.nyc_output
**/jest-coverage

# Production builds
**/build/
**/dist/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE specific files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.DS_Store

# Temporary files
.tmp/
.temp/
.cache/
