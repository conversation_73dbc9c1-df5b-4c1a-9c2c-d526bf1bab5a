import React, { useEffect, useRef, useState, useCallback, memo } from 'react';
import { useLocation } from "react-router-dom";
import usePlacesAutocomplete, { getGeocode, getLatLng } from 'use-places-autocomplete';

const DEFAULT_ADDRESS = "South Carolina, USA";
const DEFAULT_STATE = "South Carolina";
const DEFAULT_STATE_ABBR = "SC";
const DEFAULT_COUNTRY = "United States";
const DEFAULT_COUNTRY_ABBR = "USA";

const AutoAddress = memo(({ 
  className, 
  placeholder, 
  onChangeAddress, 
  address,
  defaultAddress = DEFAULT_ADDRESS, 
  label, 
  ...rest 
}) => {
  const inputRef = useRef(null);
  const { state } = useLocation();
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [initialValueSet, setInitialValueSet] = useState(false);
  
  // Load Google Maps script
  useEffect(() => {
    // Skip if script is already loaded
    if (window.google?.maps) {
      setIsScriptLoaded(true);
      return;
    }

    const apiKey = process.env.REACT_APP_MAPKEY;
    if (!apiKey) {
      console.error("Google Maps API key not found in environment variables");
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    script.async = true;
    script.defer = true;

    const handleLoad = () => setIsScriptLoaded(true);
    const handleError = () => console.error('Failed to load Google Maps script');

    script.addEventListener('load', handleLoad);
    script.addEventListener('error', handleError);

    document.head.appendChild(script);

    return () => {
      script.removeEventListener('load', handleLoad);
      script.removeEventListener('error', handleError);
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  // Initialize Places Autocomplete
  const {
    ready,
    value,
    suggestions: { status, data },
    setValue,
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {},
    debounce: 300,
    initOnMount: isScriptLoaded,
    defaultValue: defaultAddress
  });

  // Parse address components
  const parseAddressComponents = useCallback((components) => {
    let city = '';
    let state = DEFAULT_STATE;
    let stateAbbreviation = DEFAULT_STATE_ABBR;
    let country = DEFAULT_COUNTRY;
    let countryAbbreviation = DEFAULT_COUNTRY_ABBR;

    if (components && Array.isArray(components)) {
      components.forEach(component => {
        const types = component.types;
        if (types.includes('locality')) {
          city = component.long_name;
        }
        if (types.includes('administrative_area_level_1')) {
          state = component.long_name;
          stateAbbreviation = component.short_name;
        }
        if (types.includes('country')) {
          country = component.long_name;
          countryAbbreviation = component.short_name;
        }
      });
    }

    return { city, state, stateAbbreviation, country, countryAbbreviation };
  }, []);

  // Geocode an address string to coordinates and address components
  const geocodeAddress = useCallback(async (addressStr) => {
    try {
      const results = await getGeocode({ address: addressStr });
      if (!results || results.length === 0) {
        throw new Error('No geocoding results found');
      }
      
      const { lat, lng } = await getLatLng(results[0]);
      const { 
        city, 
        state, 
        stateAbbreviation, 
        country, 
        countryAbbreviation 
      } = parseAddressComponents(results[0].address_components);
      
      return {
        description: addressStr,
        lat,
        lng,
        city,
        state,
        country,
        stateAbbreviation,
        countryAbbreviation
      };
    } catch (error) {
      console.error('Error during geocoding:', error);
      return null;
    }
  }, [parseAddressComponents]);

  // Initialize with default location if no address is provided
  useEffect(() => {
    if (ready && !initialValueSet) {
      const addressToUse = address || defaultAddress;
      
      const initializeLocation = async () => {
        setValue(addressToUse, false);
        setInitialValueSet(true);
        
        const locationData = await geocodeAddress(addressToUse);
        if (locationData && onChangeAddress) {
          onChangeAddress(
            locationData.description,
            locationData.lat,
            locationData.lng,
            locationData.city,
            locationData.state,
            locationData.country,
            locationData.stateAbbreviation,
            locationData.countryAbbreviation
          );
        }
      };
      
      initializeLocation();
    }
  }, [ready, initialValueSet, address, defaultAddress, setValue, geocodeAddress, onChangeAddress]);

  // Handle address selection from suggestions
  const handleSelect = useCallback(async (suggestion) => {
    if (!suggestion || !suggestion.description) return;
    
    setValue(suggestion.description, false);
    clearSuggestions();

    const locationData = await geocodeAddress(suggestion.description);
    if (locationData && onChangeAddress) {
      onChangeAddress(
        locationData.description,
        locationData.lat,
        locationData.lng,
        locationData.city,
        locationData.state,
        locationData.country,
        locationData.stateAbbreviation,
        locationData.countryAbbreviation
      );
    }
  }, [setValue, clearSuggestions, geocodeAddress, onChangeAddress]);

  // Update value when address prop changes
  useEffect(() => {
    if (address && ready) {
      setValue(address, false);
      setInitialValueSet(true);
    }
  }, [address, setValue, ready]);

  // Update value when state.fullAddress changes
  useEffect(() => {
    if (state?.fullAddress?.address && ready) {
      setValue(state.fullAddress.address, false);
      clearSuggestions();
    }
  }, [state, setValue, clearSuggestions, ready]);

  // Handle input changes
  const handleInput = (e) => {
    setValue(e.target.value);
  };

  // Render loading state
  if (!isScriptLoaded) {
    return <div>Loading Maps...</div>;
  }

  return (
    <>
      <div className='flex items-center bg-white pl-1 rounded-l-md'>
        <input
          ref={inputRef}
          className={className}
          placeholder={placeholder || label}
          value={value}
          onChange={handleInput}
          disabled={!ready}
          {...rest}
        />
      </div>

      {status === 'OK' && (
        <div className='absolute flex w-64 ml-3 center-styl'>
          <ul className='w-full relative z-50 p-2 bg-white shadow-lg'>
            {data.map((suggestion) => {
              const {
                place_id,
                structured_formatting: { main_text, secondary_text },
              } = suggestion;

              return (
                <li
                  className='text-center cursor-pointer hover:text-color-primary'
                  key={place_id}
                  onClick={() => handleSelect(suggestion)}
                >
                  <strong className='text-lg'>{main_text}</strong> {secondary_text}
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </>
  );
});

export default AutoAddress;