#!/usr/bin/env node

/**
 * Image optimization script for BnbYond
 * Compresses and converts images to WebP format
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🖼️  Starting image optimization...\n');

// Check if sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.log('📦 Installing sharp for image optimization...');
  execSync('npm install sharp --save-dev', { stdio: 'inherit' });
  sharp = require('sharp');
}

const assetsPath = path.join(__dirname, '../src/assets');
const publicPath = path.join(__dirname, '../public/assets');

// Image optimization settings
const optimizationSettings = {
  jpeg: { quality: 80, progressive: true },
  png: { quality: 80, compressionLevel: 8 },
  webp: { quality: 80, effort: 6 }
};

// Size breakpoints for responsive images
const breakpoints = [400, 800, 1200, 1600];

/**
 * Optimize a single image
 */
async function optimizeImage(inputPath, outputDir, filename) {
  try {
    const ext = path.extname(filename).toLowerCase();
    const baseName = path.basename(filename, ext);
    
    if (!['.jpg', '.jpeg', '.png'].includes(ext)) {
      console.log(`⏭️  Skipping ${filename} (unsupported format)`);
      return;
    }

    console.log(`🔄 Processing ${filename}...`);

    // Get image metadata
    const metadata = await sharp(inputPath).metadata();
    const originalSize = fs.statSync(inputPath).size;

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    let totalSaved = 0;

    // Generate responsive sizes
    for (const width of breakpoints) {
      if (width >= metadata.width) continue;

      // Original format
      const resizedPath = path.join(outputDir, `${baseName}_${width}w${ext}`);
      await sharp(inputPath)
        .resize(width, null, { withoutEnlargement: true })
        .jpeg(optimizationSettings.jpeg)
        .png(optimizationSettings.png)
        .toFile(resizedPath);

      // WebP format
      const webpPath = path.join(outputDir, `${baseName}_${width}w.webp`);
      await sharp(inputPath)
        .resize(width, null, { withoutEnlargement: true })
        .webp(optimizationSettings.webp)
        .toFile(webpPath);
    }

    // Optimize original size
    const optimizedPath = path.join(outputDir, filename);
    await sharp(inputPath)
      .jpeg(optimizationSettings.jpeg)
      .png(optimizationSettings.png)
      .toFile(optimizedPath);

    // Create WebP version of original
    const webpPath = path.join(outputDir, `${baseName}.webp`);
    await sharp(inputPath)
      .webp(optimizationSettings.webp)
      .toFile(webpPath);

    // Calculate savings
    const optimizedSize = fs.statSync(optimizedPath).size;
    const webpSize = fs.statSync(webpPath).size;
    const saved = originalSize - optimizedSize;
    const webpSaved = originalSize - webpSize;

    console.log(`  ✅ ${filename}:`);
    console.log(`     Original: ${formatBytes(originalSize)}`);
    console.log(`     Optimized: ${formatBytes(optimizedSize)} (${formatBytes(saved)} saved)`);
    console.log(`     WebP: ${formatBytes(webpSize)} (${formatBytes(webpSaved)} saved)`);

    return { originalSize, optimizedSize, webpSize, saved: Math.max(saved, webpSaved) };

  } catch (error) {
    console.error(`❌ Error processing ${filename}:`, error.message);
    return { originalSize: 0, optimizedSize: 0, webpSize: 0, saved: 0 };
  }
}

/**
 * Process all images in a directory
 */
async function processDirectory(inputDir, outputDir) {
  if (!fs.existsSync(inputDir)) {
    console.log(`⚠️  Directory ${inputDir} does not exist`);
    return { totalOriginal: 0, totalOptimized: 0, totalSaved: 0 };
  }

  const files = fs.readdirSync(inputDir);
  const imageFiles = files.filter(file => 
    /\.(jpg|jpeg|png)$/i.test(file)
  );

  if (imageFiles.length === 0) {
    console.log(`📁 No images found in ${inputDir}`);
    return { totalOriginal: 0, totalOptimized: 0, totalSaved: 0 };
  }

  console.log(`📁 Processing ${imageFiles.length} images in ${inputDir}...`);

  let totalOriginal = 0;
  let totalOptimized = 0;
  let totalSaved = 0;

  for (const file of imageFiles) {
    const inputPath = path.join(inputDir, file);
    const result = await optimizeImage(inputPath, outputDir, file);
    
    totalOriginal += result.originalSize;
    totalOptimized += result.optimizedSize;
    totalSaved += result.saved;
  }

  return { totalOriginal, totalOptimized, totalSaved };
}

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Main optimization function
 */
async function optimizeAllImages() {
  try {
    console.log('🚀 Starting image optimization process...\n');

    const directories = [
      { input: path.join(assetsPath, 'img'), output: path.join(publicPath, 'img') },
      { input: path.join(assetsPath, 'images'), output: path.join(publicPath, 'images') },
      { input: path.join(__dirname, '../public/assets/img'), output: path.join(__dirname, '../public/assets/img/optimized') }
    ];

    let grandTotalOriginal = 0;
    let grandTotalOptimized = 0;
    let grandTotalSaved = 0;

    for (const { input, output } of directories) {
      const result = await processDirectory(input, output);
      grandTotalOriginal += result.totalOriginal;
      grandTotalOptimized += result.totalOptimized;
      grandTotalSaved += result.totalSaved;
    }

    // Summary
    console.log('\n📊 Optimization Summary:');
    console.log('='.repeat(50));
    console.log(`Total Original Size: ${formatBytes(grandTotalOriginal)}`);
    console.log(`Total Optimized Size: ${formatBytes(grandTotalOptimized)}`);
    console.log(`Total Saved: ${formatBytes(grandTotalSaved)}`);
    
    if (grandTotalOriginal > 0) {
      const percentSaved = ((grandTotalSaved / grandTotalOriginal) * 100).toFixed(1);
      console.log(`Percentage Saved: ${percentSaved}%`);
    }

    console.log('\n✅ Image optimization completed!');
    console.log('\n📝 Next Steps:');
    console.log('1. Update your components to use OptimizedImage component');
    console.log('2. Use responsive image sizes with srcSet');
    console.log('3. Serve WebP images with fallbacks');
    console.log('4. Consider using a CDN for further optimization');

  } catch (error) {
    console.error('❌ Image optimization failed:', error);
    process.exit(1);
  }
}

// Run optimization
optimizeAllImages();
