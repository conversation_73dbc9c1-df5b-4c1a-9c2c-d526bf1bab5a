import { createServer } from "http";
import { socketConnection } from "./controllers/chat";
import { schedulingJob, IcalCronJob, subscriptionReminderJob } from "./cronjob";
import mongoose from "mongoose";
import { createApp } from "./app";


const port = process.env.API_PORT || 5000;

const startServer = async () => {
  try {
    const app = await createApp();
    const server = createServer(app);

    socketConnection(server);
    schedulingJob.start();
    IcalCronJob.start();
    subscriptionReminderJob.start();
    server.listen(port, () => {
      console.log(`Server running on port ${port}`);
    });

    server.on('error', (error) => {
      console.error('Server Error:', error);
    });

    process.on('SIGINT', async () => {
      console.log('Shutting down server...');
      await mongoose.connection.close();
      console.log('MongoDB connection closed through app termination');
      process.exit(0);
    });

    return server;
  } catch (error) {
    console.error('Server Startup Error:', error);
    throw error;
  }
};

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection:', reason);
  process.exit(1);
});

startServer().catch(err => {
  console.error('Failed to start server:', err);
  process.exit(1);
});
