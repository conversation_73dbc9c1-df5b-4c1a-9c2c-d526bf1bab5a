import bcrypt from 'bcrypt';
import { User, Employee } from '../model';
import { generateToken } from '../utils/jwt';

const fetchAllUser = async (req, res) => {
  try {
    let ALLUser = await User.find({}).select("-pass");
    return res.status(200).json({ data: ALLUser });
  } catch (err) {

    return res.status(400).json({ message: "something Went Wrong" });
  }
};

const adminLogin = async (req, res) => {
  try {
    const { email, pass } = req.body;

    if (!(email && pass)) {
      return res.status(400).json({ message: "Email and password are required" });
    }

    let user = await User.findOne({ email: email, isAdmin: true });

    if (!user) {
      user = await Employee.findOne({ email: email });

      if (!user) {
        return res.status(400).json({ message: "User not found" });
      }
    }

    const passwordMatch = await bcrypt.compare(pass, user.pass);

    if (!passwordMatch) {
      return res.status(400).json({ message: "Incorrect password" });
    }

    const isAdmin = user.isAdmin || false;
    const roles = user.roles || [];

    const token = generateToken({
      userId: user._id,
      email: user.email,
      roles: roles,
      isAdmin: isAdmin,
    });

    await User.findByIdAndUpdate(user._id, { token: token }, { new: true });

    return res.status(200).json({
      status: true,
      message: `${isAdmin ? "Admin" : "Employee"} login successful`,
      data: { ...user.toObject(), token },
    });

  } catch (err) {
    console.error(err);
    return res.status(500).json({ message: "Something went wrong" });
  }
};

const refferalNumberCount = async (req, res) => {
  try {
    const users = await User.find({});
    const totalUsers = users.length;
    let totalReferrals = 0;
    users.forEach(user => {
      totalReferrals += user.referrelUsers.length;
    });
    const avgReferralsPerUser = totalUsers === 0 ? 0 : totalReferrals / totalUsers;
    const userData = users.map(user => {
      return {
        email: user.email,
        fname: user.fname,
        lname: user.lname,
        pic: user.pic,
        referralCode: user.referralCode,
        refferdUserNumber: user.referrelUsers.length,
        referralUrl: `https://beta.bnbyond.com/auth/signup?referralCode=${user.referralCode}`,
      };
    });
    const sortedUserData = userData.sort((a, b) => b.refferdUserNumber - a.refferdUserNumber);
    return res.status(200).json({
      totalUsers,
      totalReferrals,
      avgReferralsPerUser,
      users: sortedUserData,
    });
  } catch (err) {
    console.error('Error fetching referral statistics:', err);
    return res.status(500).json({ message: "Something went wrong" });
  }
};

export const Controller = {
  fetchAllUser,
  adminLogin,
  refferalNumberCount,
};
