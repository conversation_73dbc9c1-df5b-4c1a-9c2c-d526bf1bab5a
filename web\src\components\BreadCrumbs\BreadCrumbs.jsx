import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { ChevronRightIcon } from "@heroicons/react/24/solid";

const Breadcrumbs = () => {
    const location = useLocation();
    const navigate = useNavigate();

    const pathnames = location.pathname.split("/").filter((x) => x);

    return (
        <nav className="text-gray-600 text-sm mb-4">
            <ol className="flex items-center space-x-2">
                <li className="cursor-pointer hover:underline" onClick={() => navigate("/profile")}>
                    Profile
                </li>
                {pathnames.map((value, index) => {
                    const to = `/${pathnames.slice(0, index + 1).join("/")}`;
                    const isLast = index === pathnames.length - 1;

                    return (
                        <React.Fragment key={to}>
                            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                            <li
                                className={`cursor-pointer ${isLast ? "text-gray-900 font-medium" : "hover:underline"}`}
                                onClick={() => !isLast && navigate(to)}
                            >
                                {value.charAt(0).toUpperCase() + value.slice(1).replace("-", " ")}
                            </li>
                        </React.Fragment>
                    );
                })}
            </ol>
        </nav>
    );
};

export default Breadcrumbs;