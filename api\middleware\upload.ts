import multer from 'multer';
import { S3Client, S3ClientConfig } from '@aws-sdk/client-s3';
import multerS3 from 'multer-s3';
import { Request } from 'express';
import path from 'path';

// Load environment variables with validation
const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY;
const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID;
const AWS_REGION = process.env.AWS_REGION;
// console.log(process.env.AWS_SECRET_ACCESS_KEY,process.env.AWS_ACCESS_KEY_ID,"reg",AWS_REGION,process.env.MONGO_URI)
const S3_BUCKET = process.env.S3_BUCKET || 'bnbpictures';

// Validate required AWS credentials
if (!AWS_SECRET_ACCESS_KEY || !AWS_ACCESS_KEY_ID || !AWS_REGION) {
  console.error('Missing AWS credentials in environment variables');
}

// Configure AWS
const s3ClientConfig: S3ClientConfig = {
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ACCESS_KEY_ID || '',
    secretAccessKey: AWS_SECRET_ACCESS_KEY || ''
  }
};

const s3Client = new S3Client(s3ClientConfig);

// Interface for Express request with file
export interface MulterS3Request extends Request {
  file?: Express.Multer.File & {
    location?: string;
  };
  files?: Array<Express.Multer.File & {
    location: string;
  }>;
}

// File filter function to validate file types
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Allow specified mime types only
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`));
  }
};

// Generate a unique filename with original extension
const generateUniqueFileName = (originalname: string): string => {
  // Simple timestamp-based approach
  const timestamp = Date.now();
  const extension = path.extname(originalname);
  
  // Get a simplified base name (optional)
  const simpleName = path.basename(originalname, extension)
    .replace(/[^a-zA-Z0-9]/g, '_')
    .substring(0, 20);
  
  // Just timestamp + name + extension
  return `${timestamp}-${simpleName}${extension}`;
};

// Create the multer S3 upload middleware
export const awsupload = multer({
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  storage: multerS3({
    acl: 'public-read',
    s3: s3Client,
    bucket: S3_BUCKET,
    contentType: multerS3.AUTO_CONTENT_TYPE,
    key: function(req: MulterS3Request, file: Express.Multer.File, cb: (error: Error | null, key?: string) => void) {
      const filename = generateUniqueFileName(file.originalname);
      cb(null, filename);
    }
  })
});
