import { Property, Referral, Reservation, User } from '../model';
import { sendEmail } from '../services/mailJetEmail';
import { ObjectId } from 'mongoose/node_modules/mongodb';
import { PipelineStage, Types } from 'mongoose';
import moment from 'moment';
import fetch from 'node-fetch';
import ical from 'node-ical';
import { Request, Response } from 'express';
import { MulterS3Request } from '../middleware/upload';
import mongoose from 'mongoose';

const uploadProperty = async (req: MulterS3Request, res: Response): Promise<void> => {

  // return console.log(req.body)
  try {
    let propertyPics = [];
    let spaceTypeDetail = {};

    const parseJSONField = (field, fieldName) => {
      if (typeof field === "string") {
        try {
          return JSON.parse(field);
        } catch (err) {
          console.error(`Error parsing field ${fieldName}:`, err);
          throw new Error(`Invalid JSON format in ${fieldName}`);
        }
      }
      return field;
    };

    try {
      req.body.points = parseJSONField(req.body.points, "points") || [];
      req.body.guestPrice = parseJSONField(req.body.guestPrice, "guestPrice") || [];
      req.body.amenities = parseJSONField(req.body.amenities, "amenities") || [];
      req.body.characteristics = parseJSONField(req.body.characteristics, "characteristics") || [];
      req.body.spaceTypeDetail = parseJSONField(req.body.spaceTypeDetail, "spaceTypeDetail") || {};
    } catch (error) {
      res.status(400).json({ message: error.message });
      return;
    }

    if (!Array.isArray(req.body.points) || !Array.isArray(req.body.guestPrice)) {
      res.status(400).json({ message: "Invalid data format for points or guestPrice" });
      return;
    }

    req.files.forEach((item) => {
      propertyPics.push(item.location);
    });

    try {
      req.body.userId = new ObjectId(req.body.userId);
    } catch (err) {
      res.status(400).json({ message: "Invalid userId format" });
      return;
    }

    req.body.pics = propertyPics.length > 0 ? propertyPics : req.body.pics;

    const address = req.body.address || "";
    const addressParts = address.split(",");
    let country = "", state = "", city = "", street = "", zip = "";

    if (addressParts.length > 1) {
      country = addressParts[addressParts.length - 1].trim();
      const secondLastPart = addressParts[addressParts.length - 2].trim();
      const stateZipParts = secondLastPart.split(" ");

      if (stateZipParts.length > 1) {
        state = stateZipParts[0].trim();
        zip = stateZipParts[1].trim();
      } else {
        state = secondLastPart.trim();
      }

      if (addressParts.length > 2) city = addressParts[addressParts.length - 3].trim();
      if (addressParts.length > 3) street = addressParts.slice(0, addressParts.length - 3).join(",").trim();
    }

    req.body.fullAddress = { street, country, city, state, zip };

    if (isNaN(parseFloat(req.body.lat)) || isNaN(parseFloat(req.body.long))) {
      res.status(400).json({ message: "Invalid latitude or longitude" });
      return;
    }

    req.body.loc = {
      type: "Point",
      coordinates: [parseFloat(req.body.long), parseFloat(req.body.lat)],
    };

    let points = req.body.points.map((item) => {
      if (!item.date || isNaN(new Date(item.date).getTime())) {
        throw new Error("Invalid date format in points");
      }
      return { date: new Date(item.date), point: parseInt(item.point, 10) || 0 };
    });

    let guestPrice = req.body.guestPrice.map((item) => {
      if (!item.date || isNaN(new Date(item.date).getTime())) {
        throw new Error("Invalid date format in guestPrice");
      }
      return { date: new Date(item.date), point: parseFloat(item.point) || 0 };
    });

    points.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    guestPrice.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Function to fill gaps
    const fillGaps = (arr) => {
      let filledArr = [];

      // Sort input to ensure it's in date order
      arr.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      if (arr.length === 1) {
        // Only one date, extend for 6 months
        let startDate = new Date(arr[0].date);
        let endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + 6);

        while (startDate <= endDate) {
          filledArr.push({ date: new Date(startDate), point: arr[0].point });
          startDate.setDate(startDate.getDate() + 1);
        }

      } else if (arr.length > 1) {
        // More than one date, fill gaps using previous value
        let startDate = new Date(arr[0].date);
        let endDate = new Date(arr[arr.length - 1].date);
        let index = 0;
        let lastValue = arr[0].point;

        while (startDate <= endDate) {
          const currentDateStr = startDate.toISOString().split("T")[0];
          const currentArrDateStr = new Date(arr[index]?.date).toISOString().split("T")[0];

          if (index < arr.length && currentDateStr === currentArrDateStr) {
            lastValue = arr[index].point;
            filledArr.push({ date: new Date(startDate), point: lastValue });
            index++;
          } else {
            filledArr.push({ date: new Date(startDate), point: lastValue });
          }

          startDate.setDate(startDate.getDate() + 1);
        }
      }

      return filledArr;
    };


    req.body.points = fillGaps(points);
    req.body.guestPrice = fillGaps(guestPrice);


    let newProperty = await Property.create(req.body);

    if (newProperty && req.body.userId) {
      const user = await User.findById(req.body.userId);
      if (user) {
        const emailData = {
          listerName: user.username || user.fname,
          propertyName: newProperty.title,
          listingId: newProperty._id,
          currentStatus: "Under Review",
          company: "Bnbyond.com",
          date_Time: newProperty.timeStamp,
        };
        await sendEmail(user.email, "Property Listing Received", emailData, "Property_Listing_Email_Body");
      }
    }

    res.status(200).json({ newProperty, status: true, data: "completed" });
  } catch (err) {
    console.error("Server Error:", err);
    res.status(500).json({ message: "Internal server error", error: err.message });
  }
};


const getAllProperty = async (req, res) => {
  const { city, state, country, StateAbbr, CountryAbbr, address } = req.query;

  try {
    if (!city && !state && !StateAbbr && !country && !CountryAbbr && !address) {
      return res.status(400).json({
        status: false,
        message: "No filters provided. Please provide at least one search criterion.",
      });
    }

    let matchConditions: PipelineStage[] = [
      {
        $match: {
          status: "Active"
        }
      }
    ];

    if (city) {
      matchConditions.push({
        $match: {
          $or: [
            { "fullAddress.city": { $regex: city, $options: "i" } },
            { "address": { $regex: city, $options: "i" } },
          ],
        },
      });
    }

    // Rest of the conditions remain the same...
    if (state || StateAbbr) {
      const stateConditions = [];
      if (state) {
        stateConditions.push({ "fullAddress.state": { $regex: state, $options: "i" } });
        stateConditions.push({ "address": { $regex: state, $options: "i" } });
      }
      if (StateAbbr) {
        stateConditions.push({ "fullAddress.state": { $regex: StateAbbr, $options: "i" } });
        stateConditions.push({ "address": { $regex: StateAbbr, $options: "i" } });
      }
      if (stateConditions.length) {
        matchConditions.push({
          $match: { $or: stateConditions },
        });
      }
    }
    if (country || CountryAbbr) {
      const countryConditions = [];
      if (Array.isArray(country)) {
        country.forEach((c) => {
          countryConditions.push({
            $or: [
              { "fullAddress.country": { $regex: c, $options: "i" } },
              { "address": { $regex: c, $options: "i" } },
            ],
          });
        });
      } else if (country) {
        countryConditions.push({
          $or: [
            { "fullAddress.country": { $regex: country, $options: "i" } },
            { "address": { $regex: country, $options: "i" } },
          ],
        });
      }
      if (CountryAbbr) {
        countryConditions.push({
          $or: [
            { "fullAddress.country": { $regex: CountryAbbr, $options: "i" } },
            { "address": { $regex: CountryAbbr, $options: "i" } },
          ],
        });
      }
      if (countryConditions.length) {
        matchConditions.push({
          $match: { $or: countryConditions },
        });
      }
    }

    if (address) {
      const addressParts = address.split(",").map((part) => part.trim());
      if (addressParts.length >= 3) {
        const countryFromAddress = addressParts[addressParts.length - 1];
        const stateFromAddress = addressParts[addressParts.length - 2];
        const cityFromAddress = addressParts[addressParts.length - 3];

        matchConditions.push({
          $match: {
            "fullAddress.country": { $regex: countryFromAddress, $options: "i" },
            "fullAddress.state": { $regex: stateFromAddress, $options: "i" },
            "fullAddress.city": { $regex: cityFromAddress, $options: "i" },
            "address": { $regex: address, $options: "i" }, // Full address match
          },
        });
      }
    }
    matchConditions.push({
      $addFields: {
        averageRating: {
          $cond: [
            { $gt: [{ $size: { $ifNull: ["$reviews", []] } }, 0] },
            {
              $let: {
                vars: {
                  rawRating: {
                    $min: [
                      5,
                      {
                        $max: [
                          1,
                          { $round: [{ $avg: "$reviews.rating" }, 2] }
                        ]
                      }
                    ]
                  }
                },
                in: {
                  $cond: [
                    { $eq: [{ $subtract: ["$$rawRating", { $floor: "$$rawRating" }] }, 0] },
                    { $toString: { $concat: [{ $toString: "$$rawRating" }, ".00"] } },
                    { $toString: "$$rawRating" }
                  ]
                }
              }
            },
            "No Review Yet"
          ]
        }
      }
    });

    const properties = await Property.aggregate(matchConditions);

    if (properties.length === 0) {
      return res.status(200).json({
        status: false,
        message: "No active properties found matching the criteria.",
        records: []
      });
    }
    // console.log(properties)
    return res.status(200).json({
      status: true,
      data: properties,
    });

  } catch (error) {
    console.error("Error fetching properties:", error);
    return res.status(500).json({
      status: false,
      message: "An error occurred while fetching properties.",
      error: error.message,
    });
  }
};


const countProperties = async (req, res) => {
  try {
    let propertiesCount = await Property.countDocuments();
    res.send({ count: propertiesCount });
  } catch (error) {
    res.status(500).send({ error: 'An error occurred while counting the properties' });
  }
};

const getPropertyByUserId = async (req, res) => {
  const { userId } = req.params;

  let Fetch = await Property.aggregate([
    {
      $match: { userId: new ObjectId(userId) },
    },

    {
      $lookup: {
        from: "users",
        localField: "userId",
        foreignField: "_id",
        as: "user",
      },
    },

    { $unwind: "$user" },

    { $unset: ["user.pass"] },
  ]);

  return res.status(200).json({ status: true, data: Fetch });
};

const getPropertyByPropertyId = async (req, res) => {
  const { Id } = req.params;

  // Validate if Id is a valid MongoDB ObjectId
  if (!Types.ObjectId.isValid(Id)) {
    return res.status(400).json({
      status: false,
      message: "Invalid Property ID format",
    });
  }

  try {
    const Fetch = await Property.aggregate([
      {
        $match: { _id: new ObjectId(Id) },
      },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user",
        },
      },
      { $unwind: "$user" },
      {
        $lookup: {
          from: "users",
          localField: "reviews.userId",
          foreignField: "_id",
          as: "reviewUsers",
        },
      },
      {
        $addFields: {
          reviews: {
            $map: {
              input: "$reviews",
              as: "review",
              in: {
                $mergeObjects: [
                  "$$review",
                  {
                    user: {
                      $arrayElemAt: [
                        "$reviewUsers",
                        { $indexOfArray: ["$reviewUsers._id", "$$review.userId"] }
                      ],
                    },
                  },
                ],
              },
            },
          },
        },
      },
      {
        $unset: ["user.pass", "reviews.user.pass"],
      },
    ]);

    // Check if the property exists
    if (!Fetch || Fetch.length === 0) {
      return res.status(404).json({
        status: false,
        message: "Property not found with the provided ID",
      });
    }

    return res.status(200).json({
      status: true,
      data: Fetch,
    });
  } catch (error) {
    // Log the error details for debugging
    console.error("Error in getPropertyByPropertyId:", error.message);
    console.error(error.stack); // Log stack trace for debugging

    return res.status(500).json({
      status: false,
      message: "An unexpected error occurred. Please try again later.",
      error: process.env.NODE_ENV === 'production' ? null : error.message, // Hide detailed error in production
      stack: process.env.NODE_ENV === 'production' ? null : error.stack, // Send stack trace in development
    });
  }
};

const getPropertyOnlyAvailable = async (req: Request, res: Response): Promise<void> => {
  const { role } = req.query;

  let matchStage: PipelineStage.Match = {
    $match: {}
  };

  if (role !== 'admin') {
    matchStage.$match.status = "Active";
    matchStage.$match.isAvailable = true;
  }

  try {
    let Fetch = await Property.aggregate([
      matchStage,
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user",
          pipeline: [
            {
              $project: { _id: 1, fname: 1, lname: 1, pic: 1 } // Include relevant fields
            }
          ]
        },
      },
      { $unwind: "$user" }, // Ensure single user object
      {
        $lookup: {
          from: "users",
          localField: "reviews.userId",
          foreignField: "_id",
          as: "reviewUsers",
          pipeline: [
            {
              $project: { _id: 1, fname: 1, lname: 1, pic: 1 } // Include user details in review
            }
          ]
        },
      },
      {
        $addFields: {
          reviews: {
            $map: {
              input: "$reviews",
              as: "review",
              in: {
                $mergeObjects: [
                  "$$review",
                  {
                    user: {
                      $arrayElemAt: [
                        "$reviewUsers",
                        { $indexOfArray: ["$reviewUsers._id", "$$review.userId"] }
                      ],
                    },
                  },
                ],
              },
            },
          },
        },
      },
      {
        $unset: ["user.pass", "reviews.user.pass"] // Remove sensitive fields
      },
    ]);

    res.status(200).json({ status: true, data: Fetch });

  } catch (error) {
    console.error("Error fetching properties:", error);
    res.status(500).json({ status: false, message: "Internal Server Error" });
  }
};



const deletePropertyById = async (req, res) => {

  const { _id } = req.params;
  await Property.findByIdAndDelete({ _id })
  return res.status(200).json({ status: true, message: "deleted successfully" });

};
const updatePropertyById = async (req, res) => {
  try {
    const { _id } = req.params;
    const parsedBody = req.body;
    const existingProperty = await Property.findById(_id);
    if (!existingProperty) {
      return res.status(404).json({ status: false, message: "Property not found" });
    }

    const safeParse = (value: any, defaultValue: any = null) => {
      try {
        if (typeof value === 'string') {
          return JSON.parse(value);
        }
        return value ?? defaultValue;
      } catch (error) {
        return value ?? defaultValue;
      }
    };

    parsedBody.points = safeParse(parsedBody.points, existingProperty.points);
    parsedBody.guestPrice = safeParse(parsedBody.guestPrice, existingProperty.guestPrice);

    if (Array.isArray(parsedBody.amenities)) {
      parsedBody.amenities = parsedBody.amenities.map((item) => safeParse(item));
    } else if (typeof parsedBody.amenities === 'string') {
      parsedBody.amenities = [safeParse(parsedBody.amenities)];
    }

    if (Array.isArray(parsedBody.characteristics)) {
      parsedBody.characteristics = parsedBody.characteristics.map((item) => safeParse(item));
    } else if (typeof parsedBody.characteristics === 'string') {
      parsedBody.characteristics = safeParse(parsedBody.characteristics);
    }

    if (parsedBody.spaceTypeDetail) {
      parsedBody.spaceTypeDetail = safeParse(parsedBody.spaceTypeDetail, existingProperty.spaceTypeDetail);
    }

    let newPicsFromBody = [];
    if (parsedBody.photos) {
      try {
        newPicsFromBody = JSON.parse(parsedBody.photos);
      } catch (err) {
        console.error("Error parsing photos:", err);
      }
    }

    const newImageUrls = req.files ? req.files.map((file) => file.location) : [];

    const allNewPics = [...newImageUrls, ...newPicsFromBody];

    const uniqueNewPics = [...new Set(allNewPics)];

    const existingPics = existingProperty.pics || [];

    const imagesToRemove = existingPics.filter((image) => !uniqueNewPics.includes(image));

    const imagesToAdd = uniqueNewPics.filter((image) => !existingPics.includes(image));

    const updatedPics = existingPics.filter((image) => !imagesToRemove.includes(image));
    updatedPics.push(...imagesToAdd);
    const updatedLoc = {
      type: "Point",
      coordinates: [parseFloat(parsedBody.long), parseFloat(parsedBody.lat)],
    };

    const updatedPoints = Array.isArray(parsedBody.points)
      ? parsedBody.points.map((item) => ({
        date: new Date(item.date),
        point: parseInt(item.point, 10) || 0,
      }))
      : [];

    const updatedGuestPrice = Array.isArray(parsedBody.guestPrice)
      ? parsedBody.guestPrice.map((item) => ({
        date: new Date(item.date),
        point: parseInt(item.point, 10) || 0,
      }))
      : [];

    const updatedData = {
      status: parsedBody.status ?? existingProperty.status,
      amenities: parsedBody.amenities,
      characteristics: parsedBody.characteristics,
      guestPrice: updatedGuestPrice,
      points: updatedPoints,
      pics: updatedPics, // Updated list of images
      isPropertyAvaibleOnOtherSites: parsedBody.isPropertyAvaibleOnOtherSites ?? existingProperty.isPropertyAvaibleOnOtherSites,
      address: parsedBody.address ?? existingProperty.address,
      price: parsedBody.price ?? existingProperty.price,
      cleaningFee: parsedBody.cleaningFee ?? existingProperty.cleaningFee,
      discountPrice: parsedBody.discountPrice ?? existingProperty.discountPrice,
      amenitiesFee: parsedBody.amenitiesFee ?? existingProperty.amenitiesFee,
      propertyType: parsedBody.propertyType ?? existingProperty.propertyType,
      spaceType: parsedBody.spaceType ?? existingProperty.spaceType,
      spaceTypeDetail: parsedBody.spaceTypeDetail,
      title: parsedBody.title ?? existingProperty.title,
      description: parsedBody.description ?? existingProperty.description,
      listing: parsedBody.listing ?? existingProperty.listing,
      userCurrency: parsedBody.userCurrency ?? existingProperty.userCurrency,
      isAvailable: parsedBody.isAvailable ?? existingProperty.isAvailable,
      favouriteList: parsedBody.favouriteList ?? existingProperty.favouriteList,
      timeStamp: parsedBody.timeStamp ?? existingProperty.timeStamp,
      reviews: parsedBody.reviews ?? existingProperty.reviews,
      loc: updatedLoc,
    };

    const updatedProperty = await Property.findByIdAndUpdate(_id, updatedData, { new: true });

    return res.status(200).json({
      status: true,
      message: "Property updated successfully",
      updatedProperty,
    });

  } catch (err) {
    console.error("Error updating property:", err);
    return res.status(500).json({
      status: false,
      message: "Server error while updating property",
    });
  }
};



const updatePropertyStatus = async (req, res) => {
  try {
    const { _id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        status: false,
        message: "Status is required to update the property",
      });
    }

    const existingProperty = await Property.findById(_id).populate("userId");
    const existingAddress = existingProperty.address;
    const user = (existingProperty.userId as any)
    const userEmail = user.email;
    const userName = user.username;
    let subscriptionAmount = 88
    if (user.referredBy) {
      const referral = await Referral.findById(user.referredBy);
      const refUser = await User.findById(referral?.userId);
      const isInfluencer = refUser?.roles.includes("Influencer");

      subscriptionAmount = isInfluencer
        ? 48
        : 88
    }

    if ((status === "active" || status === "Active") && existingProperty && existingProperty.status !== "Active") {
      const userActiveProperties = await Property.countDocuments({
        userId: existingProperty.userId._id || existingProperty.userId,
        status: { $in: ["Active", "active"] },
        _id: { $ne: existingProperty._id }
      });

      if (userActiveProperties === 0 && existingProperty.hasBeenActivatedBefore === false) {

        await User.findByIdAndUpdate(
          existingProperty.userId._id || existingProperty.userId,
          { $inc: { points: 3000 } }
        );
        await sendEmail(
          userEmail,
          "Congratulations!",
          { userName, subscriptionAmount },
          "after_Listing_Notification"
        );
      }
    }
    if (!existingProperty) {
      return res.status(404).json({
        status: false,
        message: "Property not found",
      });
    }

    const updatedData: any = { status };
    if (status.toLowerCase() === "active") {
      updatedData.hasBeenActivatedBefore = true;
    }

    await Property.findByIdAndUpdate(_id, updatedData, { new: true });
    const emailData = {
      userName,
      property_address: existingAddress,
      property_id: existingProperty._id,
    };
    if (status == "active" || status == "Active") {
      await sendEmail(
        userEmail,
        `Congratulations! Your Property at ${existingAddress} is Now Active`,
        emailData,
        "property_listed"
      );


    } else if (status == "rejected" || status == "Rejected") {
      const emailData = {
        userName,
        property_address: existingAddress,
        property_id: existingProperty._id,
      };
      await sendEmail(
        userEmail,
        `Your Property Listing at ${existingAddress} Has Been Rejected`,
        emailData,
        "property_rejected"
      );
    }
    return res.status(200).json({
      status: true,
      message: "Property status updated successfully",
    });
  } catch (err) {
    console.error("Error updating property status:", err);
    return res.status(500).json({
      status: false,
      message: "Server error while updating property status",
    });
  }
};




const addReviewToProperty = async (req, res) => {
  const { userId, rating, comment, reservationId } = req.body;

  try {
    const reservation = await Reservation.findById(reservationId).populate("property_id");
    if (!reservation) {
      return res.status(404).json({ status: false, message: "Reservation not found" });
    }

    const property = reservation.property_id as any;
    if (!property) {
      return res.status(404).json({ status: false, message: "Property not found" });
    }

    if (!Array.isArray(property.reviews)) {
      property.reviews = [];
    }

    const existingReview = property.reviews.some(
      (review) => review.reservationId?.toString() === reservationId
    );

    if (existingReview) {
      return res.status(400).json({ status: false, message: "User has already reviewed this reservation" });
    }

    property.reviews.push({
      userId: new ObjectId(userId),
      reservationId: new ObjectId(reservationId),
      rating,
      comment,
      date: new Date(),
    });

    await property.save();

    return res.status(200).json({ status: true, message: "Review added successfully", data: property });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ status: false, message: "Internal Server Error" });
  }
};


const addToFavourite = async (req, res) => {
  try {
    const { propertyId, userId } = req.body;

    const propertyData = await Property.findById(propertyId);
    if (!propertyData) {
      return res.status(404).json({ status: false, error: 'property not found' });
    }

    const existingLike = propertyData.favouriteList.some((favourite) => favourite.userId.toString() === userId);
    if (existingLike) {
      propertyData.favouriteList.pull({ userId: userId });
      await propertyData.save();
    } else {
      propertyData.favouriteList.push({ userId });
      await propertyData.save();
    }
    const newData = await Property.findById(propertyId)
    res.status(200).json({ status: true, data: newData });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
// iCal functionality

function generateICal(property) {
  let ical = `BEGIN:VCALENDAR\nVERSION:2.0\nPRODID:-//Bnbyond//EN\nCALSCALE:GREGORIAN\nMETHOD:PUBLISH\n`;

  property.bookDates.forEach(date => {
    const startDate = moment(date).utc().format('YYYYMMDD'); // Full-day event
    const endDate = moment(date).add(1, 'days').utc().format('YYYYMMDD'); // Block full day

    ical += `BEGIN:VEVENT\n`;
    ical += `UID:${property._id}-${startDate}@bnbyond.com\n`;
    ical += `SUMMARY:Booked - ${property.title}\n`;
    ical += `DTSTART;VALUE=DATE:${startDate}\n`;
    ical += `DTEND;VALUE=DATE:${endDate}\n`;
    ical += `DESCRIPTION:This property is booked on this date.\n`;
    ical += `STATUS:CONFIRMED\n`;
    ical += `END:VEVENT\n`;
  });

  ical += 'END:VCALENDAR';
  return ical;
}
const iClaFuntion = async (req, res) => {
  try {
    const property = await Property.findById(req.params.propertyId);
    if (!property) {
      return res.status(404).send('Property not found');
    }

    const icalData = generateICal(property);
    res.setHeader('Content-Type', 'text/calendar');
    res.setHeader('Content-Disposition', `attachment; filename="${property._id}.ics"`);
    res.send(icalData);
  } catch (error) {
    console.error(error);
    res.status(500).send('Internal Server Error');
  }
}


async function fetchBookedDates(icalUrl) {
  try {
    const response = await fetch(icalUrl, { timeout: 10000 })
    if (!response.ok) throw new Error('Failed to fetch iCal file');
    const icalData = await response.text();

    const events = ical.parseICS(icalData);

    let bookedDates = [];
    for (let key in events) {
      if (events[key].type === 'VEVENT') {
        const startDate = moment(events[key].start).format('YYYY-MM-DD');
        bookedDates.push(startDate);
      }
    }

    return bookedDates;
  } catch (error) {
    console.error("Error fetching iCal:", error);
    return null;
  }
}

const fetchBookedDatesFromOtherWebsite = async (req, res) => {
  const { propertyId, url } = req.body;
  if (!propertyId || !url) {
    return res.status(400).json({ error: "Property ID and iCal URL are required" });
  }

  try {
    const bookedDates = await fetchBookedDates(url);
    if (!bookedDates.length) {
      return res.status(500).json({ error: "No bookings found in external calendar" });
    }

    const property = await Property.findById(propertyId);

    if (!property) {
      return res.status(404).json({ error: "Property not found" });
    }

    const mergedDates = [
      ...new Set([
        ...property.bookDates.map(d => new Date(d).toISOString()),
        ...bookedDates.map(d => new Date(d).toISOString())
      ])
    ];

    property.bookDates = mergedDates.map(d => new Date(d));
    if (!property.externalSources.includes(url)) {
      property.externalSources.push(url);
    }

    await property.save();
    res.json({ message: "Booking dates updated successfully", property });
  } catch (error) {
    console.error("Error updating bookings:", error);
    res.status(500).json({ error: "Failed to update property bookings" });
  }
};


const updatePropertyDates = async (req: Request, res: Response): Promise<void> => {
  try {
    const propertyId = req.params.id;
    const startDate = new Date(req.body.date);

    if (!propertyId || !req.body.date || isNaN(startDate.getTime())) {
      res.status(400).json({ message: "Invalid property ID or date" });
      return;
    }

    const property = await Property.findById(propertyId);
    if (!property) {
      res.status(404).json({ message: "Property not found" });
      return;
    }

    // Calculate the end date (6 months from the given start date)
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + 6);

    const fillFutureDates = (arr: any[], endDate: Date) => {
      const filledArr = [...arr];
      filledArr.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      const lastEntry = filledArr[filledArr.length - 1];
      let currentDate = new Date(lastEntry.date);
      currentDate.setDate(currentDate.getDate() + 1);

      while (currentDate <= endDate) {
        filledArr.push({
          date: new Date(currentDate),
          point: lastEntry.point,
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return filledArr;
    };

    // Extend and update property fields
    const extendedPoints = fillFutureDates(property.points, endDate);
    const extendedGuestPrice = fillFutureDates(property.guestPrice, endDate);

    property.set("points", extendedPoints);
    property.set("guestPrice", extendedGuestPrice);

    await property.save();

    res.status(200).json({
      message: "Property points and guestPrice successfully extended for 6 months",
      points: property.points,
      guestPrice: property.guestPrice,
    });

  } catch (err) {
    console.error("Error extending property data:", err);
    res.status(500).json({ message: "Internal server error", error: err.message });
  }
};


export const Controller = {
  uploadProperty,
  getAllProperty,
  getPropertyByUserId,
  getPropertyByPropertyId,
  updatePropertyStatus,
  deletePropertyById,
  updatePropertyById,
  getPropertyOnlyAvailable,
  addToFavourite,
  addReviewToProperty,
  countProperties,
  iClaFuntion,
  fetchBookedDatesFromOtherWebsite,
  updatePropertyDates
};
