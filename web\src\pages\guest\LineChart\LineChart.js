import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

const StackedBarLineChart = () => {
  const chartRef = useRef(null); // Create a reference to the canvas element

  useEffect(() => {
    const ctx = chartRef.current.getContext('2d'); // Get the 2D context of the canvas

    // Define the data for the chart
    const data = {
      labels: ['January', 'February', 'March', 'April', 'May'], //x-axis
      datasets: [
        {
          label: 'Dataset 1',
          data: [10, 20, 30, 40, 50],
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
          type: 'bar', // 'bar' for bar chart, 'line' for line chart
        },
        {
          label: 'Dataset 2',
          data: [5, 15, 25, 35, 45],
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
          type: 'bar',
        },
        {
          label: 'Dataset 3',
          data: [30, 25, 20, 15, 10],
          borderColor: 'rgba(255, 206, 86, 1)',
          borderWidth: 1,
          type: 'line',
          fill: false,
        },
      ],
    };

    //chart options
    const options = {
      scales: {
        x: {
          stacked: true,
        },
        y: {
          stacked: true,
        },
      },
    };

    // Create a new chart instance
    new Chart(ctx, {
      type: 'bar', // Set the default type as 'bar'
      data: data, // Provide the data defined above
      options: options, // Provide the options defined above
    });
  }, []);

  return <canvas ref={chartRef} />; // Render the canvas element for the chart
};

export default StackedBarLineChart;
