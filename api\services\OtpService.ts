import dotenv from 'dotenv';
import twilio from 'twilio';

dotenv.config();

const accountId = process.env.ACCOUNTID_TWILIO;
const authToken = process.env.AUTHTOKEN_TWILIO;
const testNum= process.env.TESTNO_TWILIO;

const twilioClient = twilio(accountId, authToken);

export const otpService = async (number, text) => {
    try {
        await twilioClient.messages.create({
           body:`OTP : ${text} verification otp from BnByond app`,
           from:testNum,
           to:number
        })
        console.log("Send Sms Success");
    } catch (error) {
        console.log(error,"sms not sent");
    }
}
