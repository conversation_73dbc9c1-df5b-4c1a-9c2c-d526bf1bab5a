import React from 'react';
import { Route, Routes } from 'react-router-dom';
import PropertyListing from 'pages/member/PropertyListing';
import Sidebar from 'components/Sidebar/Sidebar';
import Nofound from 'pages/StaticPages/Nofound';

function Member() {
  return (
    <>
      <Sidebar />
      <div className='relative md:ml-64'>
        <main>
          <section className=' w-full h-full  py-10 '>
            <Routes>
              <Route path='/propertylisting' element={<PropertyListing />} />
              <Route path='*' element={<Nofound />} />
            </Routes>
          </section>
        </main>
      </div>
    </>
  );
}

export default Member;
