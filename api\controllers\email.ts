import { ObjectId } from 'mongoose/node_modules/mongodb';
import { Request, Response } from 'express';
import crypto from 'crypto';
import bcrypt from 'bcrypt'; 
import { sendEmail } from '../services/mailJetEmail';
import { User, ForgetPassword, EmailVerify } from '../model/index';
import { ThanksEmailBody } from '../services/EmailTemplates';

const applyForgotPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.body;

    let userEmail = email.toLowerCase();
    const user = await User.findOne({
      $or: [
        { email: userEmail },
        { recoveryEmail: userEmail }
      ],
    });
    if (user) {
      const resetToken = crypto.randomBytes(32).toString('hex');
      const hashedToken = bcrypt.hashSync(resetToken, 10);
      const expirationTime = new Date(Date.now() + 15 * 60 * 1000);
      await ForgetPassword.create({
        email: userEmail,
        token: hashedToken,
        expiresAt: expirationTime,
      });
      let uniqueLink = `${process.env.frontendLink}/auth/updatepass/${encodeURIComponent(userEmail)}/${resetToken}`;

      var emailParameters = {
        userEmail,
        userName: user.fname,
        uniqueLink: uniqueLink,
      };

      await sendEmail(
        userEmail,
        "Reset your Password",
        emailParameters,
        "ForgetPass_Email_Body"
      );
      res.status(200).json("Email Has been sent");
    } else {
      res.status(400).json({ message: "Email not found! Please check." });
    }
  } catch (err) {
    res.status(400).json({ message: "Something Went Wrong" });
  }
};

const verifyEmail = async (req: Request, res: Response): Promise<void> => {
  const { email, uniqueId } = req.params;

  try {
    const Record_Exist = await EmailVerify.findOne({
      $and: [
        {
          email: email,
        },
        {
          _id: new ObjectId(uniqueId),
        },
      ],
    });

    if (!Record_Exist) {
      res.status(400).json({ message: "No Record found." });
      return;
    }

    if (Date.now() > Record_Exist.expiresAt.getTime()) {
      res.status(400).json({ message: "Verification link has expired. Please request a new one." });
      return;
    }

    const filter = { email: email };
    const update = { verify: "yes" };
    const doc = await User.findOneAndUpdate(filter, update, { new: true });

    if (!doc) {
      res.status(400).json({ message: "User not found." });
      return;
    }

    const emailParameters = {
      email,
      fname: doc.fname,
    };

    const { success } = await sendEmail(
      email,
      "Welcome to BnByond",
      emailParameters,
      "Welcome_Email_Body"
    );

    if (success) {
      console.log("Email sent successfully.");
      res.status(200).send(ThanksEmailBody(emailParameters))
    } else {
      console.log("Failed to send email.");
      res.status(400).json({ message: "Failed to send email" });
    }
  } catch (err) {
    res.status(400).json({ message: "Something went wrong." });
  }
};

const sendTransactionDetails = async (req: Request, res: Response): Promise<void> => {

  try {
    const { email, transactionData } = req.body;

    if (!email || !transactionData) {
      console.warn("Missing required fields:", { email, transactionData });
      res.status(400).json({ message: "Email and transaction data are required" });
      return;
    }

    const { success } = await sendEmail(
      email,
      "Your Booking Confirmation - BnByond",
      transactionData,
      "transaction_details"
    );

    if (success) {
      res.status(200).json({ message: "Transaction details sent successfully" });
    } else {
      console.warn("Email sending failed.");
      res.status(400).json({ message: "Failed to send transaction details" });
    }
  } catch (error) {
    console.error("Transaction details email error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

const contactHost = async (req: Request, res: Response): Promise<void> => {
  try {

    const {
      guestEmail,
      guestName,
      hostEmail,
      hostName,
      propertyTitle,
      checkInDate,
      checkOutDate,
      guestMessage
    } = req.body;

    const emailData = {
      guestName,
      hostName,
      propertyTitle,
      checkInDate,
      checkOutDate,
      guestMessage
    };

    const { success } = await sendEmail(
      hostEmail,
      `New Property Inquiry`,
      emailData,
      "contact_host_template"
    );

    if (success) {
      console.log("Email sent successfully.");
      res.status(200).json({ message: "Email sent successfully" });
    } else {
      console.log("Failed to send email.");
      res.status(400).json({ message: "Failed to send email" });
    }
  } catch (error) {
    console.error("Contact host email error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export const Controller = {
  applyForgotPassword,
  verifyEmail,
  sendTransactionDetails,
  contactHost
};