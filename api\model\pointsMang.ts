import mongoose from "mongoose";

const pointsMang = new mongoose.Schema({
  pointsCount: { type: Number, default: 0 },
  sendBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Users' },
  recievedBy: {  type: mongoose.Schema.Types.ObjectId, ref: 'Users' },
  checkIn: {  type: String },
  checkOut: {  type: String },
  reservationId:{ type: mongoose.Schema.Types.ObjectId, ref: 'reservation' },
  isActive:{type:Boolean,default:true},
  isRefund: {type:Boolean,default:false},
  refundDetails: {
    pointsToGuest: { type: Number, default: 0 }, // Points refunded to Guest
    pointsToHost: { type: Number, default: 0 },  // Points transferred to Host
    refundProcessedAt: { type: Date },           // Refund timestamp
  }
});

export const PointsMang = mongoose.model("pointsMang", pointsMang);
