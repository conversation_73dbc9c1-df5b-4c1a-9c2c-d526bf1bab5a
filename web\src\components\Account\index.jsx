import React from "react";
import { useNavigate } from "react-router-dom"; // Import useNavigate
import { UserIcon, CreditCardIcon, ShieldCheckIcon, DocumentTextIcon, CurrencyDollarIcon } from "@heroicons/react/24/solid";
import { ArrowLeftIcon } from "@heroicons/react/24/solid"; // Back icon

export const Account = () => {
  const navigate = useNavigate(); // Initialize navigation

  const sections = [
    { id: "personalInfo", label: "Personal Info", icon: <UserIcon className="w-14 h-16 text-green-400" />, description: "Update your name, email, and profile details.", path: "/account/personal-info" },
    // { id: "paymentsAndPayouts", label: "Payments", icon: <CreditCardIcon className="w-14 h-16 text-green-400" />, description: "Add a new card or manage existing payment methods.", path: "/account/payments" },
    { id: "trackPayments", label: "Payment History", icon: <CurrencyDollarIcon className="w-14 h-16 text-green-400" />, description: "View your payment history and track pending payments.", path: "/account/payments-history" },
    { id: "loginPrivacy", label: "Login & Privacy", icon: <ShieldCheckIcon className="w-14 h-16 text-green-400" />, description: "Manage your password and security settings.", path: "/account/login-privacy" },
    // { id: "taxes", label: "Taxes", icon: <DocumentTextIcon className="w-14 h-16 text-green-400" />, description: "Access tax documents and manage tax settings.", path: "/account/taxes" },
  ];

  return (
    <div className="md:my-10 md:mx-20">
      {/* Back Button */}
      <button
        onClick={() => navigate(-1)}
        className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
      >
        <ArrowLeftIcon className="w-5 h-5 mr-2" />
        Back
      </button>

      <p className="text-xl md:text-2xl lg:text-4xl font-Normal pt-4 pb-4">Account Settings</p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sections.map((section) => (
          <div
            key={section.id}
            className="p-6 border rounded-lg shadow-md cursor-pointer transition-all flex gap-4 items-center bg-white hover:bg-gray-100"
            onClick={() => navigate(section.path)}
          >
            <div className="flex">{section.icon}</div>
            <div>
              <p className="text-lg font-medium mt-2">{section.label}</p>
              <p className="text-sm text-gray-500">{section.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
