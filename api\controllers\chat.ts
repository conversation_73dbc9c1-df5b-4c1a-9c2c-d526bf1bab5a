import { Server } from 'socket.io';
import { Server as HttpServer } from 'http';
import { Blog, Conversation, Message, User } from '../model';
import { MulterS3Request } from '../middleware/upload';
import { Request, Response } from 'express';

// chat apis 
export const socketConnection = (http: HttpServer): void => {
    let users = [];

    const io = new Server(http, {
        cors: {
            // added deploy link
            origin: 'https://beta.bnbyond.com',
        }
    });

    io.on('connection', socket => {
        socket.on('addUser', userId => {
            const isUserExist = users.filter(user => user.userId !== userId);
            users = isUserExist
            const user = { userId, socketId: socket.id };
            users.push(user);
            io.emit('getUsers', users);
        });
        // Emit like/dislike updates
        socket.on('likePost', async (postId, userId) => {
            console.log("hell like")
            if (!postId || !userId) return socket.emit('error', { message: 'Invalid data' });
            try {
                const blog = await Blog.findById(postId);
                if (!blog) {
                    return socket.emit('error', { message: 'Blog not found' });
                }
                if (!Array.isArray(blog.likes)) blog.likes = [];
                if (!Array.isArray(blog.dislikes)) blog.dislikes = [];
                const isLiked = blog.likes.includes(userId);
                if (isLiked) {
                    blog.likes.splice(blog.likes.indexOf(userId), 1);
                } else {
                    blog.likes.push(userId);
                }
                await blog.save();
                io.emit('updateLikes', { postId, likeCount: blog.likes.length });
            } catch (error) {
                console.error(error);
                socket.emit('error', { message: 'Error updating likes' });
            }
        });

        socket.on('dislikePost', async (postId, userId) => {
            console.log("hell dislike")
            if (!postId || !userId) return socket.emit('error', { message: 'Invalid data' });
            try {
                const blog = await Blog.findById(postId);
                if (!blog) {
                    return socket.emit('error', { message: 'Blog not found' });
                }
                if (!Array.isArray(blog.likes)) blog.likes = [];
                if (!Array.isArray(blog.dislikes)) blog.dislikes = [];
                const isDisliked = blog.dislikes.includes(userId);
                if (isDisliked) {
                    blog.dislikes.splice(blog.dislikes.indexOf(userId), 1);
                } else {
                    blog.dislikes.push(userId);
                }

                await blog.save();
                io.emit('updateDislikes', { postId, dislikeCount: blog.dislikes.length });
            } catch (error) {
                console.error(error);

                socket.emit('error', { message: 'Error updating dislikes' });
            }
        });
        socket.on('sendMessage', async ({ senderId, receiverId, fileUrl, message, conversationId }) => {
            const receiver = users.find(user => user.userId === receiverId);
            const sender = users.find(user => user.userId === senderId);
            const user = await User.findById(senderId);
            io.emit('updateUnreadMessageCount', { conversationId: conversationId });
            if (receiver) {
                io.to(receiver.socketId).to(sender.socketId).emit('getMessage', {
                    senderId,
                    message,
                    conversationId,
                    receiverId,
                    fileUrl,
                    user: { id: user._id, fullName: user.fname + ' ' + user.lname, email: user.email, pic: user.pic }
                });
            }
            else {
                io.to(sender.socketId).emit('getMessage', {
                    senderId,
                    message,
                    conversationId,
                    receiverId,
                    fileUrl,
                    user: { id: user._id, fullName: user.fname + ' ' + user.lname, email: user.email, pic: user.pic }
                });
            }
            io.emit('updateUnreadMessageCount', { conversationId: conversationId });
        });

        socket.on('disconnect', () => {
            users = users.filter(user => user.socketId !== socket.id);
            io.emit('getUsers', users);
        });

    })
}

const conversationpost = async (req: Request, res: Response): Promise<void> => {
    try {
        const { senderId, receiverId } = req.body;

        const existingConversation = await Conversation.findOne({
            members: { $all: [senderId, receiverId] },
        });

        if (existingConversation) {
            res.status(409).json({
                success: false,
                message: 'Conversation already exists',
            });
            return;
        }
        const newConversation = new Conversation({ members: [senderId, receiverId] });
        await newConversation.save();

        res.status(201).json({
            success: true,
            message: 'Conversation created successfully',
        });
    } catch (error) {
        console.error('Error creating conversation:', error);
        res.status(500).json({
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
};


const getconversationpost = async (req: Request, res: Response): Promise<void> => {
    try {
        const userId = req.params.userId;
        const conversations = await Conversation.find({ members: { $in: [userId] } });
        const conversationUserData = Promise.all(conversations.map(async (conversation) => {
            const receiverId = conversation.members.find((member) => member !== userId);
            const user = await User.findById(receiverId);
            if (user !== null) {
                let fullName = null;
                if (user.fname && user.lname) {
                    fullName = `${user.fname} ${user.lname}`;
                } else if (user.fname) {
                    fullName = user.fname;
                } else if (user.lname) {
                    fullName = user.lname;
                }

                const unreadMessageCount = await Message.countDocuments({ conversationId: conversation._id, read: false });
                const lastMessage = await Message.findOne({ conversationId: conversation._id }).sort({ createdAt: -1 });
                return {
                    user: {
                        receiverId: user._id,
                        email: user.email,
                        fullName: fullName,
                        pic: user.pic
                    },
                    conversationId: conversation._id,
                    unreadMessageCount: unreadMessageCount,
                    lastMessage: lastMessage?.message,
                    time: lastMessage?.createdAt
                };
            }
        }));

        res.status(200).json(await conversationUserData);
    } catch (error) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
}


const messagepost = async (req: Request, res: Response): Promise<void> => {
    try {
        const { conversationId, senderId, message, fileUrl, receiverId = '' } = req.body;
        if (!senderId) {
            res.status(400).json({ success: false, error: 'Please fill all required fields' });
            return;
        }
        if (conversationId === 'new' && receiverId) {
            const newCoversation = new Conversation({ members: [senderId, receiverId] });
            await newCoversation.save();
            const newMessage = new Message({ conversationId: newCoversation._id, senderId, message, fileUrl });
            await newMessage.save();
            res.status(200).json({ success: true, message: 'Message sent successfully' });
            return;
        } else if (!conversationId && !receiverId) {
            res.status(400).json({ success: false, error: 'Please fill all required fields' });
            return;
        }
        const newMessage = new Message({ conversationId, senderId, message, fileUrl });
        await newMessage.save();
        res.status(200).json({ success: true, message: 'Message sent successfully' });
    } catch (error) {
        console.log(error, 'Error')
        res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
}

const getmessageById = async (req: Request, res: Response): Promise<void> => {
    try {
        const checkMessages = async (conversationId) => {
            const messages = await Message.find({ conversationId });

            await Promise.all(messages.map(async (message) => {
                message.read = true;
                await message.save();
            }));

            const messageUserData = await Promise.all(messages.map(async (message) => {
                const user = await User.findById(message.senderId);
                let fullName = null;

                if (user.fname && user.lname) {
                    fullName = `${user.fname} ${user.lname}`;
                } else if (user.fname) {
                    fullName = user.fname;
                } else if (user.lname) {
                    fullName = user.lname;
                }

                return { user: { id: user._id, email: user.email, fullName: fullName, pic: user.pic }, message: message.message, time: message.createdAt, fileUrl: message.fileUrl };
            }));

            // Send the updated message data
            res.status(200).json(messageUserData);
        };

        const conversationId = req.params.conversationId;
        if (conversationId === 'new') {
            const checkConversation = await Conversation.find({ members: { $all: [req.query.senderId, req.query.receiverId] } });
            if (checkConversation.length > 0) {
                await checkMessages(checkConversation[0]._id);
            } else {
                res.status(200).json([]);
                return;
            }
        } else {
            await checkMessages(conversationId);
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal Server Error',
            error: error.message,
        });
    }
};

const uploadImage = async (req: MulterS3Request, res: Response): Promise<void> => {
    try {
        const fileUrls = req.files ? req.files.map(file => file.location) : [];

        if (fileUrls.length === 0) {
            res.status(400).json({ success: false, error: 'Please upload at least one image' });
            return;
        }

        res.status(200).json({ success: true, fileUrls });
    } catch (error) {
        res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const Controller = {
    conversationpost,
    getconversationpost,
    messagepost,
    getmessageById,
    uploadImage
}