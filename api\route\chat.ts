import { Router } from "express";
import { awsupload } from "../middleware/upload";
import { Controller as Chat<PERSON>ontroller } from "../controllers/chat";

const router = Router();

// Conversation routes
router.post("/conversation", ChatController.conversationpost);
router.get("/conversation/:userId", ChatController.getconversationpost);

// Message routes (note: removed duplicate route)
router.post("/message", awsupload.array('fileUrl', 5), ChatController.messagepost);
router.get("/message/:conversationId", ChatController.getmessageById);

// File upload routes
router.post("/upload-image", awsupload.array('fileUrl', 5), ChatController.uploadImage);

export default router;
