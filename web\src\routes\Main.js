import React, { Suspense } from "react";
import { Route, Routes, useLocation } from "react-router-dom";
import BnbNav from "components/NavBar/BnbNav";
import Footerbn from "pages/Footer/Footerbn";
import Navbar from "components/Navbars/AuthNavbar";

// Lazy load heavy components
const PropertyListing = React.lazy(() => import("pages/member/PropertyListing"));
const Nofound = React.lazy(() => import("pages/StaticPages/Nofound"));
const VerificationPage = React.lazy(() => import("pages/customer/VerificationPage"));
const SearchListings = React.lazy(() => import("pages/customer/SearchListings"));
const PropertyDetails = React.lazy(() => import("pages/customer/PropertyDetails"));
const ReserveDetails = React.lazy(() => import("pages/customer/PropertyDetails/ReserveDetails"));
const ConfirmReservation = React.lazy(() => import("pages/customer/PropertyDetails/ConfirmReservation"));
const BookingConfirmation = React.lazy(() => import("pages/customer/BookingConfirmation"));
const Payment = React.lazy(() => import("components/Payment/Payment"));
const SendVerificationCode = React.lazy(() => import("components/PhoneVerification/PhoneVerification"));
const MyTrip = React.lazy(() => import("pages/customer/MyTrip/MyTrip"));
const WishList = React.lazy(() => import("pages/customer/MyTrip/WishList"));
const StartListing = React.lazy(() => import("pages/member/PropertyListing/StartListing"));
const Chat = React.lazy(() => import("pages/Chat"));
const Referrals = React.lazy(() => import("pages/guest/Referrals/Referrals"));
const HostProfile = React.lazy(() => import("pages/HostProfile/HostProfile").then(module => ({ default: module.HostProfile })));
const TermsAndConditions = React.lazy(() => import("pages/guest/TermsAndConditions"));
const PrivacyAndPolicy = React.lazy(() => import("pages/guest/PrivacyAndPolicy"));
const Community = React.lazy(() => import("pages/Community"));
const Host = React.lazy(() => import("pages/Host").then(module => ({ default: module.Host })));
const GuestAccount = React.lazy(() => import("pages/guest/guestAccount").then(module => ({ default: module.GuestAccount })));
const HostAccount = React.lazy(() => import("pages/Host/HostAccount").then(module => ({ default: module.HostAccount })));
const ProfileV2 = React.lazy(() => import("components/profile").then(module => ({ default: module.ProfileV2 })));
const CommunityReply = React.lazy(() => import("pages/Community/CommunityReply"));
const ReferralPrograms = React.lazy(() => import("pages/admin/ReferralProgram"));
const InfluencerDetails = React.lazy(() => import("pages/admin/InfluencerDetails"));
const Refund = React.lazy(() => import("pages/refund/Index"));
const Review = React.lazy(() => import("pages/review/Index"));
const Pricing = React.lazy(() => import("components/Pricing/Pricing"));
const ChangeReservation = React.lazy(() => import("pages/customer/PropertyDetails/ChangeReservation"));
const RateandReview = React.lazy(() => import("components/ReviewComponent/RateandReview"));
const AdminDashboard = React.lazy(() => import("pages/admin/MainDashboard"));
const FinancialTransactions = React.lazy(() => import("pages/admin/FinancialTransactions/FinancialTransactions"));
const SupportTicketing = React.lazy(() => import("pages/admin/SupportTicketing/SupportTicketing"));
const UserManagement = React.lazy(() => import("pages/admin/UserManagement/userManagement"));
const Permissions = React.lazy(() => import("pages/admin/Roles_Permissions"));
const ReviewsFeedback = React.lazy(() => import("pages/admin/Reviews/ReviewsFeedback"));
const HostDetail = React.lazy(() => import("pages/admin/HostDetail/HostDetail"));
const PropertyManagement = React.lazy(() => import("pages/admin/PropertyManagement/PropertyManagement"));
const AdminSettings = React.lazy(() => import("pages/admin/AdminSettings/AdminSettings"));
const ReviewResponse = React.lazy(() => import("pages/admin/ReviewResponse/ReviewResponse"));
const AdminMessages = React.lazy(() => import("pages/admin/AdminMessages"));
const UserManagement2 = React.lazy(() => import("pages/admin/UserManagement/UserManagement2"));
const AdminPropertyDetail = React.lazy(() => import("pages/admin/PropertyManagement/AdminPropertyDetail"));
const ManageProperty = React.lazy(() => import("pages/admin/PropertyManagement/PropertyManagement"));
const Properties = React.lazy(() => import("pages/admin/PropertyManagement/Properties"));
// Continue lazy loading remaining components
const AdminReviews = React.lazy(() => import("pages/admin/Reviews/AdminReviews"));
const MyProperties = React.lazy(() => import("pages/guest/MyProperties/MyProperties"));
const AdminModal = React.lazy(() => import("pages/admin/AdminModal/AdminModal"));
const ReservationManagement = React.lazy(() => import("pages/admin/ReservationManagement/ReservationManagement"));
const ReservationCrad = React.lazy(() => import("components/Cards/ReservationCrad"));
const UpdatePassword = React.lazy(() => import("pages/auth/UpdatePassword"));
const Account = React.lazy(() => import("components/Account").then(module => ({ default: module.Account })));
const PayoutsStepOne = React.lazy(() => import("components/Account/PaymentsAndPayouts/payouts/payoutsStepOne").then(module => ({ default: module.PayoutsStepOne })));
const PayoutsStepTwo = React.lazy(() => import("components/Account/PaymentsAndPayouts/payouts/payoutsStepTwo").then(module => ({ default: module.PayoutsStepTwo })));
const GuestProperty = React.lazy(() => import("pages/customer/PropertyList/GuestProperty"));
const PropertyTab = React.lazy(() => import("pages/admin/PropertyManagement/PropertyTab"));
const SuccessStripe = React.lazy(() => import("pages/Stripe/SuccessStripe"));
const ErrorStripe = React.lazy(() => import("pages/Stripe/ErrorStripe"));
const CommingSoon = React.lazy(() => import("pages/CommingSoon/CommingSoon"));
const PreviewDetail = React.lazy(() => import("pages/member/PropertyListing/PreviewDetail"));
const Careers = React.lazy(() => import("components/Careers"));
const PersonalInfo = React.lazy(() => import("components/Account/PersonalInfo").then(module => ({ default: module.PersonalInfo })));
const PaymentsAndPayouts = React.lazy(() => import("components/Account/PaymentsAndPayouts").then(module => ({ default: module.PaymentsAndPayouts })));
const TrackPayments = React.lazy(() => import("components/Account/TrackPayments").then(module => ({ default: module.TrackPayments })));
const LoginPrivacy = React.lazy(() => import("components/Account/LoginAndPrivacy").then(module => ({ default: module.LoginPrivacy })));
const Taxes = React.lazy(() => import("components/Account/Taxes").then(module => ({ default: module.Taxes })));
const ManageSubscription = React.lazy(() => import("components/ManageSubscription/ManageSubscription").then(module => ({ default: module.ManageSubscription })));
const HostedReservation = React.lazy(() => import("pages/member/HostedReservation"));
const HostedReservationDetail = React.lazy(() => import("pages/member/HostedReservationDetail"));

// Keep lightweight components as regular imports

// Loading component for Main routes
const MainLoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);


function Main() {
  let location = useLocation();
  return (
    <>
      {/* {location.pathname !== "/propertylisting" ? <Navbar /> : ""} */}
      {location.pathname !== "/propertylisting" && (
        <>
          <BnbNav />
          <Navbar />
        </>
      )}

      <main>
        <section className="relative w-full h-full min-h-screen ">
          <Suspense fallback={<MainLoadingSpinner />}>
            <Routes>
            <Route path="/profile" element={<ProfileV2 />} />
            <Route path="/mytrip" element={<MyTrip />} />
            <Route path="/admindashboard" element={<AdminDashboard />} />
            <Route path="/financialtransactions" element={<FinancialTransactions />} />
            <Route path="/supportticketing" element={<SupportTicketing />} />
            <Route path="/hostdetail" element={<HostDetail />} />
            <Route path="/adminpropertydetail" element={<AdminPropertyDetail />} />
            <Route path="/myproperties" element={<MyProperties />} />
            <Route path="/adminmodal" element={<AdminModal />} />
            <Route path="/reservationcrad" element={<ReservationCrad />} />

            <Route path="/updatepassword" element={<UpdatePassword />} />
            <Route path="/account" element={<Account />} />
            <Route path="/payoutstepone" element={<PayoutsStepOne />} />
            <Route path="/payoutsteptwo" element={<PayoutsStepTwo />} />

            <Route path="/wishlist" element={<WishList />} />
            <Route path="/listing" element={<StartListing />} />
            <Route path="/community" element={<Community />} />
            <Route path="/refund" element={<Refund />} />
            <Route path="/refund/:id" element={<Refund />} />
            <Route path="/review" element={<Review />} />
            <Route path="/rateandreview" element={<RateandReview />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/changereservation" element={<ChangeReservation />} />
            <Route path="/communityreply/:id" element={<CommunityReply />} />
            {/* <Routes> */}
            <Route path="/propertylisting" element={<PropertyListing />} />
            <Route path="/hosting/reservations" element={<> <HostedReservation /> <Footerbn /></>} />
            <Route path="/hosting/reservations/:id" element={<> <HostedReservationDetail /> <Footerbn /></>} />
            <Route path="/guestAccount" element={<GuestAccount />} />
            <Route path="/hostAccount" element={<HostAccount />} />
            <Route path="/hostprofile/:id" element={<HostProfile />} />
            <Route path="/chat" element={<Chat />} />
            <Route path="/terms" element={<TermsAndConditions />} />
            <Route path="/privacy" element={<PrivacyAndPolicy />} />
            <Route path="/previewdetail" element={<PreviewDetail />} />
            <Route path="/careers" element={<Careers />} />

            {/* Host root */}
            {/* <Route path="/host" element={<Host />} /> */}
            <Route path="/dashboard" element={<Host />} />
            <Route path="/usermanagement" element={<UserManagement />} />
            <Route path="/propertyTab" element={<PropertyTab />} />

            <Route path="/reservationmanagement" element={<ReservationManagement />} />
            <Route path="/permissions" element={<Permissions />} />
            <Route path="/reviewsfeedback" element={<ReviewsFeedback />} />
            <Route path="/propertymanagement" element={<PropertyManagement />} />
            <Route path="/adminsettings" element={<AdminSettings />} />
            <Route path="/reviewresponse" element={<ReviewResponse />} />
            <Route path="/usermanagement2" element={<UserManagement2 />} />
            <Route path="/propertiessteps" element={<Properties />} />
            <Route path="/adminreviews" element={<AdminReviews />} />
            <Route path="/verification" element={<VerificationPage />} />
            <Route path="/search/:lat/:long" element={<SearchListings />} />
            <Route path="/BnbNav" element={<BnbNav />} />
            <Route path="/footer" element={<Footerbn />} />
            <Route path="/my-properties" element={<GuestProperty />} />

            <Route path="/room/:id" element={<PropertyDetails />} />
            <Route
              path="/bookingconfirmation"
              element={<BookingConfirmation />}
            />

            <Route path="/ReserveDetails" element={<ReserveDetails />} />
            <Route
              path="/ConfirmReservation"
              element={<ConfirmReservation />}
            />
            <Route path="/payment" element={<Payment />} />
            <Route
              path="/phoneverification"
              element={<SendVerificationCode />}
            />
            <Route path="/payment" element={<Payment />} />
            <Route path="/paymentsuccess" element={<SuccessStripe />} />
            <Route path="/paymenterror" element={<ErrorStripe />} />
            <Route path="/referrals" element={<Referrals />} />
            <Route path="/referrals/:id" element={<Referrals />} />
            <Route
              path="/phoneverification"
              element={<SendVerificationCode />}
            />

            <Route path='/chat'
              element={<Chat />}
            />
            <Route path='/property_management'
              element={<ManageProperty />}
            />

            <Route path='/referral_programs'
              element={<ReferralPrograms />}
            />
            <Route path='/guest_details'
              element={<InfluencerDetails />}
            />
            <Route path='/adminmessages'
              element={<AdminMessages />}
            />

            <Route path="/account" element={<Account />} />
            <Route path="/account/personal-info" element={<PersonalInfo />} />
            <Route path="/account/payments" element={<PaymentsAndPayouts />} />
            <Route path="/account/payments-history" element={<TrackPayments />} />
            <Route path="/account/login-privacy" element={<LoginPrivacy />} />
            <Route path="/account/taxes" element={<Taxes />} />
            <Route path="/manage-subscription" element={<ManageSubscription />} />

            <Route path="/chat" element={<Chat />} />

            <Route path="*" element={<Nofound />} />
            <Route path="/comingsoon" element={<CommingSoon />} />
            </Routes>
          </Suspense>
        </section>
      </main>
    </>
  );
}

export default Main;
