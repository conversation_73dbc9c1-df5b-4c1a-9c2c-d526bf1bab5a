import { Router } from "express";

import { awsupload } from "../middleware/upload";
import checkRoleMiddleware from "../middleware/routeMiddleware";
import { Controller as PostController } from "../controllers/post";

const router = Router();

// Common middleware for protected routes
const postAccessControl = checkRoleMiddleware(['subadmin', 'Blogger', 'Financial']);

// Community post routes
router.post("/communityPost", postAccessControl, PostController.uploadCommunityPost);
router.get("/communityPost", postAccessControl, PostController.getCommunityPost);
router.get("/communityPost/:postId", postAccessControl, PostController.getCommunityPostById);
router.post("/likePost", postAccessControl, PostController.CommunityLikePost);
router.post("/replyPost", postAccessControl, PostController.CommunityReplyPost);

// Blog management routes (admin side)
router.post("/blog", postAccessControl, awsupload.single('pic'), PostController.createBlogs);
router.put("/blogupdate/:_id", postAccessControl, awsupload.single('pic'), PostController.updateBlog);
router.put("/admin/blogdetail/:_id", postAccessControl, awsupload.single('pic'), PostController.updateBlog);
router.delete("/blog/:_id", postAccessControl, PostController.deleteBlog);

// Blog retrieval routes (public)
router.get("/blogs", PostController.getAllBlogs);
router.get("/blog/:_id", PostController.getBlogById);
// innovator data /first500 and on innovator page
router.post("/innovator",  PostController.postInnovatorData);
router.get("/innovator", PostController.getInnovatorData);

// Blog comment routes
router.post("/blogComment", postAccessControl, PostController.blogComment);
router.put("/blogCommentupdate/:blogId/commentid/:commentId", postAccessControl, PostController.blogCommentupdate);
router.put("/blogCommentdelete/:blogId/commentid/:commentId", postAccessControl, PostController.deleteComment);
// router.post("/blogs/:blogId/toggle-like-dislike", PostController.toggleLikeDislike);

export default router;
