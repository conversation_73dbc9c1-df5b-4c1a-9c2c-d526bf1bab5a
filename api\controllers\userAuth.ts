import crypto from 'crypto';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import useragent from 'useragent';
import { v4 as uuidv4 } from 'uuid';
import { ObjectId } from 'mongoose/node_modules/mongodb';

import { sendEmail } from '../services/mailJetEmail';
import { otpService } from '../services/OtpService';
import { User, Deviceinfo, ForgetPassword, EmailVerify, PointsMang, Referral, Subscription, Property } from '../model';
import { generateToken } from '../utils/jwt';
import { Request, Response, NextFunction } from 'express';
import { MulterS3Request } from '../middleware/upload';

const generateDeviceFingerprint = (userAgent: string, ipAddress: string): string => {
  const hash = crypto.createHash('sha256');
  hash.update(userAgent + ipAddress);
  return hash.digest('hex');
};

const login = async (req: Request, res: Response): Promise<void> => {
  const MAX_FAILED_ATTEMPTS = 8;
  const LOCK_TIME = 10 * 60 * 1000;

  const { username, pass, loginAs } = req.body;
  const ipAddress = req.ip;
  // Validate user input
  if (
    !(username && pass) ||
    typeof username !== "string" ||
    typeof pass !== "string"
  ) {
    res.status(400).json({ message: "All input is required" });
    return;
  }

  // Validate if user exists in our database
  try {


    const user = await User.findOne({
      $or: [
        { email: username.toLowerCase() },
        { username: username.toLowerCase() },
      ],
    });

    // Check if user exists and if the account is locked
    if (user) {
      const isLocked = user.lockUntil && user.lockUntil.getTime() > Date.now();
      if (isLocked) {
        res.status(403).json({ message: "Account is locked. Please try again later." });
        return;
      }

      // Check if user is enabled
      if (user.isEnable !== "yes") {
        res.status(400).json({ message: "User has been deactivated" });
        return;
      }

      // Check if the password is correct
      const passwordMatch = await bcrypt.compare(pass, user.pass);
      if (passwordMatch) {
        // Reset failed login attempts on successful login
        user.failedLoginAttempts = 0;
        user.lockUntil = undefined;

        // Check influencer status
        if (
          user.roles.includes("Influencer") &&
          user.isInfluencer !== "Active"
        ) {
          res.status(400).json({ message: "Not approved by the admin." });
          return;
        }

        // Parse device info from user agent
        const agent = useragent.parse(req.headers['user-agent']);
        const deviceInfo = {
          deviceType: "Unknown",
          browser: `${agent.family} ${agent.major}.${agent.minor}.${agent.patch}`,
          os: agent.os.toString(),
          deviceInfo: agent
        };
        if (agent.os.toString().includes("Windows") || agent.os.toString().includes("Macintosh")) {
          deviceInfo.deviceType = "PC/Laptop";
        } else if (agent.os.toString().includes("Android") || agent.os.toString().includes("iPhone") || agent.os.toString().includes("iPad")) {
          deviceInfo.deviceType = "Mobile";
        } else if (agent.os.toString().includes("Linux") && agent.source.includes("Android")) {
          deviceInfo.deviceType = "Mobile";
        } else if (agent.os.toString().includes("Tablet") || agent.os.toString().includes("iPad")) {
          deviceInfo.deviceType = "Tablet";
        }
        // Generate device fingerprint
        const deviceFingerprint = generateDeviceFingerprint(agent, ipAddress);

        // Check if the device with the same fingerprint already exists for this user
        const existingDevice = await Deviceinfo.findOne({ userId: user._id, deviceFingerprint });

        if (existingDevice) {
          existingDevice.loginTimestamp = new Date();
          await existingDevice.save();
        } else {
          const newDevice = new Deviceinfo({
            userId: user._id,
            deviceType: deviceInfo.deviceType,
            browser: deviceInfo.browser,
            os: deviceInfo.os,
            deviceInfo: deviceInfo.deviceInfo,
            deviceFingerprint: deviceFingerprint,
            loginTimestamp: new Date(),
          });
          await newDevice.save();
        }
        const token = generateToken({
          userId: user._id,
          email: user.email,
          roles: user.roles,
          isAdmin: user.isAdmin,
          username: user.username,
          loginAs: loginAs,
        });

        // Save user token and login role
        user.token = token;
        if (!user.roles.includes(loginAs)) {
          res.status(400).json({ message: "Invalid role: unauthorized user!" });
          return;
        }
        await User.findByIdAndUpdate(user._id, { loginAs });
        user.loginAs = loginAs;
        user.lastLogin = new Date();
        await user.save(); // Save user state
        res.status(200).json({ status: true, data: user });
      } else {
        // Increment failed login attempts
        user.failedLoginAttempts += 1;

        // Lock account if failed attempts exceed max
        if (user.failedLoginAttempts >= MAX_FAILED_ATTEMPTS) {
          user.lockUntil = new Date(Date.now() + LOCK_TIME);
        }
        user.lastLogin = new Date();
        await user.save();
        res.status(401).json({ message: "Incorrect username or password" });
      }
    } else {
      res.status(401).json({ message: "Incorrect username or password" });
    }
  } catch (err) {
    console.error("Error in login function:", err);
    res.status(500).json({ message: "Internal server error" });
    return;
  }
};


// get all devices of single user login
const getuserDevicesLogin = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.userId;

    // Validate that the userId is provided
    if (!userId) {
      res.status(400).json({ message: 'User ID is required' });
      return;
    }

    // Find all devices associated with the user
    const userDevices = await Deviceinfo.find({ userId }).sort({ loginTimestamp: -1 });

    // If no devices found
    if (!userDevices || userDevices.length === 0) {
      res.status(404).json({ message: 'No devices found for this user' });
      return;
    }

    // Optionally, you can fetch additional user details if needed (e.g., username, email)
    const user = await User.findById(userId);

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Return the user and their device details
    res.status(200).json({
      status: true,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
      },
      devices: userDevices,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error', error });
  }
}


const deleteAllUsers = async (req: Request, res: Response): Promise<void> => {
  let del = await User.deleteMany();
  res.send(`done ${del}`);
};

const AllUsers = async (req: Request, res: Response): Promise<void> => {
  let users = await User.find({ isAdmin: false });
  users.forEach(user => {
    user.pass = undefined;
  });
  res.status(200).send(users);
};

const SingleUser = async (req: Request, res: Response): Promise<void> => {
  const userId = req.params.id;
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({ error: "Invalid user ID" });
      return;
    }
    let user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    let userPendingPoints = await PointsMang.find({
      recievedBy: user._id,
      isActive: true,
    });

    let allPendingPoints =
      userPendingPoints.length > 0
        ? userPendingPoints.reduce((accu, item) => accu + item.pointsCount, 0)
        : 0;
    user.pendingPoints = allPendingPoints;
    await user.save();
    const {
  username,
  email,
  fname,
  lname,
  pic,
  points,
  roles,
  isEnable,
  subscription,
  cardInfo,
  reservationCount,
  membershipReminderCount,
  lastReminderSentAt,
  autoCharged,
  autoChargeDate,
} = user;

const sanitizedUser = {
  username,
  email,
  fname,
  lname,
  pic,
  points,
  roles,
  isEnable,
  pendingPoints: allPendingPoints,
  subscription,
  cardInfo: cardInfo || undefined,
  reservationCount,
  membershipReminderCount,
  lastReminderSentAt,
  autoCharged,
  autoChargeDate,
};

    user.pass = undefined;
    res.json(sanitizedUser);
  } catch (error) {
    console.error("Error fetching user data:", error);
    res.status(500).json({ error: "Server Error" });
  }
};


function generateUniqueReferralCode(): string {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

const registerByGoogle = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      isReferralCode,
      email,
      id, // Google ID
      fname,
      lname,
      pic,
      referralCode,
      phoneNumber,
      country,
      town,
      roles,
      isAdminRegistration,
    } = req.body;



    if (!(email && id && fname && pic)) {
      res.status(400).json({ message: "All required fields must be provided" });
      return;
    }

    const normalizedEmail = email.toLowerCase();

    // Check if the user already exists
    let existingUser = await User.findOne({
      $or: [
        { email: normalizedEmail },
        { googleId: id },
      ],
    });

    if (existingUser) {
      // Update Google ID if needed
      existingUser.googleId = id;
      existingUser.verify = "yes";
      existingUser.registeredBy = "google";

      // Generate new JWT token
      const token = generateToken({
        userId: existingUser._id,
        email: existingUser.email,
      });

      existingUser.token = token;
      await existingUser.save();

      res.status(200).json({ status: true, data: existingUser });
      return;
    }

    // Check referral code validity
    let checkReferralCode = null;
    let referredBy = null;

    if (isReferralCode && referralCode) {
      checkReferralCode = await Referral.findOne({
        referralCode,
        isActive: true,
      });

      if (!checkReferralCode) {
        res.status(400).json({ message: "Your invitation has expired!" });
        return;
      }

      referredBy = checkReferralCode._id;
    }

    // Generate unique username
    const generatedUsername = `${fname.toLowerCase()}_${Math.floor(1000 + Math.random() * 9000)}`;

    // Generate unique referral code
    const userReferralCode = generateUniqueReferralCode();

    // Generate counter ID
    const lastRecord = await User.findOne().sort({ _id: -1 }).limit(1);
    let counterId = lastRecord ? lastRecord.counterId + 1 : parseInt(process.env.MONGO_Counter || "1", 10);

    // Create new user
    const newUser = await User.create({
      username: generatedUsername,
      email: normalizedEmail,
      fname,
      lname,
      googleId: id,
      profilePic: pic,
      registeredBy: "google",
      phoneNumber,
      country,
      address: town,
      isAdmin: isAdminRegistration || false,
      counterId,
      roles,
      referralCode: userReferralCode,
      referredBy,
      verify: "yes",
    });

    // Create referral entry
    const referral = new Referral({
      userId: newUser._id,
      referralCode: userReferralCode,
      isActive: true,
      referredUser: [],
    });

    await referral.save();

    // Update referrer's record if applicable
    if (checkReferralCode && isReferralCode) {
      checkReferralCode.referredUser.push(newUser._id);
      await checkReferralCode.save();

      await User.findByIdAndUpdate(
        checkReferralCode.userId,
        { $push: { referrelUsers: newUser._id } },
        { new: true, useFindAndModify: false }
      );
    }

    // Generate JWT token
    const token = generateToken({
      userId: newUser._id,
      email: newUser.email,
    });

    newUser.token = token;

    // Generate referral link
    const referralLink = `https://beta.bnbyond.com/auth/signup?referralCode=${userReferralCode}`;

    res.status(200).json({ status: true, data: newUser, referralLink });
  } catch (err) {
    console.error("Google Registration Error:", err);
    res.status(500).json({
      message: "Something went wrong",
      error: err.message,
    });
  }
};


const registerByEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      isReferralCode,
      email,
      pass,
      username,
      fname,
      lname,
      referralCode,
      phoneNumber,
      country,
      town,
      roles,
      isAdminRegistration,
      referralSource,
    } = req.body;

    console.log("req.body", req.body);


    let checkReferralCode = null;

    if (isReferralCode) {
      checkReferralCode = await Referral.findOne({
        referralCode: referralCode,
        isActive: true,
      });

      if (!checkReferralCode) {
        res.status(400).json({ message: "Your invitation has been expired!" });
        return;
      }
    }

    if (!(email && pass && username && fname)) {
      res.status(400).json({ message: "All inputs are required" });
      return;
    }

    let encryptedpass = await bcrypt.hash(pass, 10);

    // Modified to only check email
    const oldUser = await User.findOne({
      email: email.toLowerCase()
    });

    let referredBy = null;
    if (referralCode) {
      const referral = await Referral.findOne({ referralCode: referralCode });
      if (referral) {
        referredBy = referral._id;
      }
    }

    if (oldUser) {
      res.status(400).json({
        status: false,
        data: "An account with this email already exists",
        message: "An account with this email already exists",
      });
      return;
    } else {
      const userReferralCode = generateUniqueReferralCode();
      const lastRecord = await User.findOne().sort({ _id: -1 }).limit(1);
      let counterId = lastRecord == null ? process.env.MONGO_Counter : lastRecord.counterId + 1;

      const user = await User.create({
        username: username.toLowerCase(),
        email: email.toLowerCase(),
        fname: fname,
        lname: lname,
        registeredBy: "email",
        phoneNumber: phoneNumber,
        country: country,
        address: town,
        isAdmin: isAdminRegistration || false,
        pass: encryptedpass,
        counterId: counterId,
        roles: roles,
        referredBy: checkReferralCode && isReferralCode ? checkReferralCode._id : undefined,
        referralCode: userReferralCode,
        verify: Array.isArray(roles) && roles.includes("Influencer") ? "yes" : "no",
      });

      const referral = new Referral({
        userId: user._id,
        referralCode: userReferralCode,
        isActive: true,
        referredUser: [],
        referralSource: referralSource,
      });

      await referral.save();
      if (checkReferralCode && isReferralCode) {
        checkReferralCode.referredUser.push(user?._id);
        await checkReferralCode.save();

        await User.findByIdAndUpdate(
          checkReferralCode.userId,
          { $push: { referrelUsers: user?._id } },
          { new: true, useFindAndModify: false }
        );
      }
      const token = generateToken({
        userId: user._id,
        email: user.email,
      });

      user.token = token;

      const VerifiedEmail = await EmailVerify.create({
        email: email.toLowerCase(),
        expiresAt: Date.now() + 15 * 60 * 1000,
      });
      const referralLink = `https://beta.bnbyond.com/auth/signup?referralCode=${userReferralCode}`;

      const emailParameters = {
        email,
        uniquelink: process.env.backendLink + "/email/verify/" + email.toLowerCase() + "/uniqueid/" + VerifiedEmail._id,
        referralLink: referralLink,
        UserName: username,
        userReferralCode: userReferralCode,
      };


      // Send different email if role includes Influencer
      const isInfluencer = Array.isArray(roles) && roles.includes("Influencer");
      const emailTemplate = isInfluencer ? "influencer_Email_Body" : "veerify_Email_Body";
      const emailsubject = isInfluencer ? "Welcome to the BnByond STR Innovator Program!" : "Welcome to BnByond";

      await sendEmail(
        email,
        emailsubject,
        emailParameters,
        emailTemplate
      );


      res.status(200).json({ status: true, data: user, referralLink: referralLink, });
    }
  } catch (err) {
    console.error("Registration error:", err);
    res.status(500).json({
      message: "Something went wrong",
      error: err.message
    });
  }
};

const CheckEmailOrUsername = async (req: Request, res: Response): Promise<void> => {
  try {
    const { username } = req.body;

    // Validate user input
    if (!username) {
      res.status(400).json({ message: "All input are required" });
      return;
    }

    // check if user already exist
    // Validate if user exist in our database
    const oldUser = await User.findOne({
      $or: [
        {
          email: username,
        },
        {
          username: username,
        },
      ],
    });
    if (oldUser) {
      res.status(400).json({
        status: false,
        message: "userName/Email Exist",
        data: { userExist: true },
      });
    } else {
      res.status(200).json({ status: true, data: { userExist: false } });
    }
  } catch (err) {
    res.status(500).json({ message: "something Went Wrong" });
  }
};

const updateUserInfo = async (req: MulterS3Request, res: Response): Promise<void> => {
  try {
    // Destructure the necessary fields from req.body
    const { userId, isEnable = "yes" } = req.body;
    let pic: string | undefined;

    // Handle file upload if it exists
    if (req.files?.length > 0) {
      pic = req.files[0]?.location;
    }

    // Prepare update object based on the presence of pic
    const update = { ...req.body, pic };

    // Check if the user exists
    const existingUser = await User.findById(userId);
    if (!existingUser) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    // Perform the update and get the updated user
    const updatedUser = await User.findOneAndUpdate(
      { _id: new ObjectId(userId) }, // Use `new ObjectId(userId)`
      update,
      { new: true }
    );

    // Return the updated user info
    res.status(200).json({ status: true, data: updatedUser });
  } catch (err) {
    // Enhanced error handling

    // Database specific error handling
    if (err.name === "MongoError") {
      console.error("Database error: ", err);
      res.status(500).json({
        status: false,
        message: "Database error occurred. Please try again later.",
        error: err.message,
      });
      return;
    }

    // Validation error handling
    if (err.name === "ValidationError") {
      console.error("Validation error: ", err);
      res.status(400).json({
        status: false,
        message: "Validation error: " + err.message,
        error: err.details || err.message,
      });
      return;
    }

    // File upload specific error handling
    if (err.code === "LIMIT_FILE_SIZE") {
      res.status(400).json({
        status: false,
        message: "File size is too large. Please upload a smaller file.",
      });
      return;
    }

    // Generic error handling for other unknown errors
    console.error("Unexpected error: ", err);
    res.status(500).json({
      status: false,
      message: "Something went wrong. Please try again later.",
      error: err.message,
      stack: err.stack,
    });
  }
};


const updatePassFromProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { oldPass, newPass, userId } = req.body;
    if (!(oldPass && newPass && userId)) {
      res.status(400).json({ message: "All input fields are required" });
      return;
    }
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ message: "User not found" });
      return;
    }
    if (!(await bcrypt.compare(oldPass, user.pass))) {
      res.status(400).json({ message: "Current password does not match" });
      return;
    }
    const encryptedPass = await bcrypt.hash(newPass, 10);
    user.pass = encryptedPass;
    await user.save();
    const updatedUser = user.toObject();
    delete updatedUser.pass;
    res.status(200).json({ status: true, data: updatedUser });
  } catch (err) {
    console.error("Error updating password:", err);

    res.status(500).json({
      message: "Something went wrong. Please try again later.",
      error: err.message,
    });
  }
};
const updateUserPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, pass, uniqueId } = req.body;
    if (!email || !pass || !uniqueId) {
      res.status(400).json({ message: "All input fields are required." });
      return;
    }
    const user = await User.findOne({
      $or: [{ email }, { recoveryEmail: email }],
    });

    if (!user) {
      res.status(404).json({ message: "User not found." });
      return;
    }

    const record = await ForgetPassword.findOne({ email }).sort({ expiresAt: -1 });

    if (!record) {
      res.status(410).json({ message: "Invalid or expired reset link." });
      return;
    }

    const currentUTC = new Date();
    const recordExpiry = new Date(record.expiresAt);


    if (currentUTC > recordExpiry) {
      await ForgetPassword.deleteOne({ email });
      res.status(410).json({ message: "Reset link has expired. Please request a new one." });
      return;
    }

    const tokenIsValid = await bcrypt.compare(uniqueId, record.token);
    if (!tokenIsValid || Date.now() > record.expiresAt.getTime()) {
      res.status(410).json({ message: "Invalid or expired reset link." });
      return;
    }
    const encryptedPass = await bcrypt.hash(pass, 10);
    await User.updateOne(
      { $or: [{ email }, { recoveryEmail: email }] },
      {
        $set: {
          pass: encryptedPass,
          failedLoginAttempts: 0,
          lockUntil: null,
        }
      }
    );
    await ForgetPassword.deleteMany({ email });

    res.status(200).json({
      message: "Password has been successfully updated. Please log in.",
      status: true,
    });
  } catch (err) {
    console.error("Error updating password:", err);
    res.status(500).json({ message: "Internal server error." });
  }
};

function generateOTP(): string {
  const otpLength = 6;

  let otp = "";
  for (let i = 0; i < otpLength; i++) {
    otp += Math.floor(Math.random() * 10);
  }

  return otp;
}

// Phone no verification

const phoneVerificationsendotp = async (req: Request, res: Response): Promise<void> => {
  try {
    const { phoneNumber, userId } = req.body;

    if (!(phoneNumber && userId)) {
      res.status(400).json({ message: "All input is required" });
      return;
    }
    const user = await User.findOne({ _id: userId });

    if (!user) {
      res.status(400).json({ message: "User is not registered" });
      return;
    }
    const randomOTP = generateOTP();
    //  otp service function
    await otpService(phoneNumber, randomOTP);
    await User.findByIdAndUpdate(
      { _id: userId },
      { otp: randomOTP, phoneNumber }
    );
    res.status(200).json({ message: "otp send to your phone no" });
  } catch (err) {
    res.status(500).json({ message: "something Went Wrong" });
  }
};

//  otp check api

const userDataById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { _id } = req.params;
    const user = await User.findById({ _id }).select("-password");

    if (!user) {
      res.status(400).json({ status: false, message: "User is not found" });
      return;
    }

    res.status(200).json({ status: true, data: user });
  } catch (err) {
    res.status(500).json({ message: "something Went Wrong" });
  }
};

const userConnected = async (req: Request, res: Response): Promise<void> => {
  let userId = req.params._id;
  try {
    if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({ error: "Invalid user ID" });
      return;
    }
    const connectUser = await User.findOne({ _id: userId });
    if (!connectUser) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    if (connectUser.verify == "yes") {
      await User.findByIdAndUpdate({ _id: userId }, { $set: { verify: "no" } });
      res.status(200).json({ message: "Account has been disconnected" });
      return;
    } else if (connectUser.verify == "no") {
      const VerifiedEmial = await EmailVerify.create({
        email: connectUser.email.toLowerCase(),
      });
      const emailParameters = {
        email: connectUser.email,
        uniquelink:
          process.env.backendLink +
          "/email/verify/" + connectUser.email.toLowerCase() +
          "/uniqueid/" +
          VerifiedEmial._id,
      };

      await sendEmail(
        connectUser.email,
        "Verification Email",
        emailParameters,
        "veerify_Email_Body"
      );
      res.status(200).json({ message: "Verification email has been send" });
    }
  } catch (err) {
    res.status(500).json({ error: "Internal Server Error" });
  }
};

const referralCode = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      res.status(404).json({ error: "User not found." });
      return;
    }

    const referralExist = await Referral.findOne(
      { userId: userId } // Search by ID
    );

    if (referralExist) {
      const updatedReferral = await Referral.findOneAndUpdate(
        { userId }, // Find the document by userId
        { $set: { referralCode: generateUniqueReferralCode() } }, // Set the fields to update
        { new: true } // Return the modified document
      );
      res.status(200).json({ referralCode: updatedReferral.referralCode });
    } else {
      const referral = new Referral({
        userId: userId,
        referralCode: generateUniqueReferralCode(),
      });
      await referral.save();
      res.status(200).json({ referralCode: referral.referralCode });
    }
  } catch (error) {
    console.error("Error generating referral code:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

const updateUserRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { loginAs, userId } = req.body;

    if (!userId || !loginAs) {
      res.status(400).json({ error: "userId and loginAs are required fields" });
      return;
    }

    const user = await User.findByIdAndUpdate(
      userId,
      { loginAs: loginAs },
      { new: true }
    );

    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    res.status(200).json({ message: "User role changed successfully", user });
  } catch (error) {
    console.error("Error in changeRole:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

const userUpdateById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params; // Get user ID from URL parameters
  const updateFields = req.body; // Get the updated fields from the request body

  try {
    // Find the user by ID and update only the fields provided
    const updatedUser = await User.findByIdAndUpdate(
      id,
      { $set: updateFields },
      { new: true, runValidators: true } // Return the updated document and run validators
    );

    if (!updatedUser) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    // Return the updated user data
    res.status(200).json({
      message: "User information updated successfully",
      data: updatedUser,
    });
  } catch (error) {
    console.error("Error updating user information:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

const addRecoveryEmail = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;
  const { recoveryEmail } = req.body;

  if (!recoveryEmail || !/\S+@\S+\.\S+/.test(recoveryEmail)) {
    res.status(400).json({ message: "Invalid recovery email format" });
    return;
  }

  try {
    const existingUser = await User.findById(id);
    if (!existingUser) {
      res.status(404).json({ message: "User not found" });
      return;
    }

    if (existingUser.email === recoveryEmail) {
      res.status(400).json({ message: "Recovery email cannot be your primary email" });
      return;
    }

    const emailInUse = await User.findOne({
      $or: [{ email: recoveryEmail }, { recoveryEmail }],
    });

    if (emailInUse) {
      res.status(409).json({ message: "Recovery email already in use" });
      return;
    }

    existingUser.recoveryEmail = recoveryEmail;
    await existingUser.save();

    res.status(200).json({ message: "Recovery email added successfully", user: existingUser });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error updating recovery email", error });
  }
};



const subscriberEmail = async (req: Request, res: Response): Promise<void> => {
  const { email } = req.body;  // User's email from frontend

  if (!email) {
    res.status(400).json({ error: 'Email is required' });
    return;
  }

  const ipAddress = req.ip;
  const userAgentString = req.headers['user-agent'];
  const agent = useragent.parse(userAgentString);

  const deviceFingerprint = generateDeviceFingerprint(agent, ipAddress);
  const browser = agent.toAgent();
  const os = agent.os.toString();
  try {
    const newSubscription = new Subscription({
      email,
      ipAddress: deviceFingerprint,
      userAgent: userAgentString,
      browser,
      os,
    });

    await newSubscription.save();

    const username = email.split('@')[0]
    var emailParameters = {
      email: username,
    };
    await sendEmail(
      email,
      "Subscription Confirmation",
      emailParameters,
      "email_subscription"
    );
    res.status(200).json({ message: 'Subscription successful' });
  } catch (error) {
    console.error('Error saving subscription:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deleteUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;

    // Validate if userId is provided
    if (!userId) {
      res.status(400).json({
        status: false,
        message: "User ID is required"
      });
      return;
    }

    // Check if userId is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        status: false,
        message: "Invalid user ID format"
      });
      return;
    }

    // Find user first to check if exists
    const user = await User.findById(userId);

    if (!user) {
      res.status(404).json({
        status: false,
        message: "User not found"
      });
      return;
    }

    // Delete related records first
    await Promise.all([
      // Delete user's device info
      Deviceinfo.deleteMany({ userId: userId }),

      // Delete user's points management records
      PointsMang.deleteMany({ recievedBy: userId }),

      // Delete user's referral records
      Referral.deleteMany({ userId: userId }),

      // Delete user's email verification records
      EmailVerify.deleteMany({ email: user.email }),

      // Delete user's forget password records
      ForgetPassword.deleteMany({ email: user.email }),

      // Delete user's subscriber records if any
      Subscription.deleteMany({ email: user.email })
    ]);

    // Finally delete the user
    await User.findByIdAndDelete(userId);

    res.status(200).json({
      status: true,
      message: "User and associated data deleted successfully"
    });

  } catch (error) {
    console.error("Error in deleteUser:", error);
    res.status(500).json({
      status: false,
      message: "An error occurred while deleting the user",
      error: error.message
    });
  }
};

const getRefferalUser = async (req: Request, res: Response): Promise<void> => {
  const userId = req.query.id as string;
  const page = parseInt(req.query.page as string) || 1;
  const limit = 6;

  if (!userId) {
    res.status(400).json({ message: "User ID is required" });
    return;
  }

  try {
    const referrals = await Referral.find({ userId })
      .populate("userId", "username email fname lname pic") // who referred
      .populate("referredUser", "fname lname email pic");    // referred users

    if (!referrals || referrals.length === 0) {
      res.status(404).json({ message: "No referred users found." });
      return;
    }

    // Flatten and filter valid referred users
    const allReferredUsers = referrals.flatMap(ref => ref.referredUser).filter(Boolean);
    const totalReferred = allReferredUsers.length;

    // Pagination logic
    const totalPages = Math.ceil(totalReferred / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = allReferredUsers.slice(startIndex, endIndex);

    // Add hasListedProperty info
    const enrichedUsers = await Promise.all(
      paginatedUsers.map(async (user: any) => {
        const hasProperty = await Property.exists({ userId: user._id,status: "Active" });
        return {
          ...user._doc,
          hasListedProperty: !!hasProperty,
        };
      })
    );

    res.status(200).json({
      totalReferred,
      currentPage: page,
      totalPages,
      users: enrichedUsers,
    });

  } catch (error) {
    console.error("Error fetching referred users:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};



export const Controller = {
  login,
  deleteAllUsers,
  registerByGoogle,
  registerByEmail,
  updateUserInfo,
  CheckEmailOrUsername,
  updateUserPassword,
  updatePassFromProfile,
  phoneVerificationsendotp,
  getuserDevicesLogin,
  userDataById,
  userConnected,
  referralCode,
  updateUserRole,
  AllUsers,
  SingleUser,
  userUpdateById,
  addRecoveryEmail,
  subscriberEmail,
  deleteUser,
  getRefferalUser
};
