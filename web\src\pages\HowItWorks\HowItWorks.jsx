import { useEffect, useState } from "react";
import Logo from "assets/img/RedImage.jpeg";
import aboutPic1 from "assets/img/1.jpg";
import aboutPic2 from "assets/img/2.jpg";
import aboutPic3 from "assets/img/3.jpg";
import aboutPic4 from "assets/img/4.jpg";
import TopImage from "assets/img/top.jpeg";
import BottomImage from "assets/img/BottomHIW.jpeg";
import BnbyondLogo from "assets/img/whitelogo.png";
import { Link } from "react-router-dom";
import { FaFileDownload } from "react-icons/fa";

export default function HowItWorks() {
  const [showPdfButton, setShowPdfButton] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const windowHeight = window.innerHeight;
      const fullHeight = document.documentElement.scrollHeight;

      if (scrollTop + windowHeight >= fullHeight - 100) {
        setShowPdfButton(true);
      } else {
        setShowPdfButton(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      <div className="relative min-h-screen">
        <div className="w-full relative text-center">
          {/* Top Image */}
          <div className="w-full">
            <Link>
              <img
                src={TopImage}
                alt="Top Image"
                className="w-full h-auto object-cover"
              />
            </Link>
          </div>

          {/* Logo Overlay */}
          <div className="absolute top-[2.3rem] sm:top-[3.9rem] md:top-[5.1rem] left-1/2 transform -translate-x-1/2 z-20">
            <img
              src={BnbyondLogo}
              alt="Centered BnByond Logo"
              className="w-[15.75rem] sm:w-[18rem] md:w-[20.5rem] lg:w-[23.5rem] xl:w-[29.5rem] h-auto mx-auto"
            />
          </div>

          {/* Content Area */}
          <div className="relative w-full h-[480px] sm:h-[500px] md:h-[550px] lg:h-[600px]">
            <img
              src={Logo}
              alt="Background Logo"
              className="absolute inset-0 w-full h-full object-cover z-0"
            />

            <div className="absolute inset-0 z-10 flex flex-col justify-center items-center text-white px-4 sm:px-8 md:px-16 lg:px-32 text-center">
              <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 leading-tight">
                The Vacation Rental Property Owners Exchange Community
              </h2>
              <p className="text-sm sm:text-base md:text-lg lg:text-xl mt-2 max-w-4xl text-left">
                BnByond is a revolutionary platform designed to dramatically improve
                the STR ownership experience. We're building a community where
                members host other vacation rental property owners, earn points, and
                use those points to stay at other member properties around the
                world.
              </p>
            </div>
          </div>
        </div>
      </div>

      <SectionCard
        title="How BnByond Works"
        image={aboutPic1}
        items={[
          { label: "List Your Property:", text: "Create a listing for your vacation rental property on the BnByond platform." },
          { label: "Make it Available:", text: "On BnByond for four or five months on your calendar." },
          { label: "Use your Credited Points:", text: "To book stays at other Member properties." },
          { label: "Earn Points:", text: "Host other BnByond members at your property and earn points based on the value of the stays. 1 point = $1US. We suggest two or three weeks annually for BnByond bookings." },
          { label: "Enjoy the Community:", text: "Connect with fellow vacation rental owners, share experiences, and build lasting relationships." },
        ]}
      />

      <SectionCard
        title="Key Benefits for Members:"
        image={aboutPic2}
        items={[
          { label: "Unbooked Time = Travel:", text: "Convert unused rental time into valuable travel opportunities." },
          { label: "Points-Based System:", text: "Earn points hosting, then use them to stay at properties worldwide." },
          { label: "Exclusive Community:", text: "Verified owner community promotes trust and respect." },
          { label: "Complementary Service:", text: "Enhances, rather than competes with, existing rental platforms like Airbnb and VRBO." },
          { label: "For Owner-Operators:", text: "Primarily serves owner-operated properties." },
          { label: "No Cleaning Fees:", text: "Fosters community and mutual respect." },
          { label: "Fair Point Rates:", text: "Point rates are consistent with advertised rates to keep a level playing field." },
        ]}
      />

      <SectionCard
        title="Membership Rates and Fees"
        image={aboutPic3}
        items={[
          { label: "Pay Nothing until", text: "your first booking as a Guest or Host" },
          { label: "Standard Membership", text: "$88 US annually. You are credited points for immediate use." },
          { label: "Use your credited points", text: "to book. Some bonus credited points expire so use them asap." },
          { label: "Fees for the Guest:", text: "5% of value of the stay." },
          { label: "Fees for Hosts:", text: "Nothing. Zip. Zero." },
        ]}
      />

      <SectionCard
        title="BnByond is Committed to:"
        image={aboutPic4}
        items={[
          { label: "Optimizing", text: "the short-term rental ownership experience." },
          { label: "Putting members", text: "at the heart of everything we do." },
          { label: "Fostering meaningful connections,", text: "valuable hands-on experiences to help you succeed as an owner-operator." },
        ]}
      />

      <div className="w-full mx-auto md:mt-10 mt-5 text-start text-md text-gray-700">
        <div className="px-4 lg:px-48">
          <p className="md:text-2xl text-xl">
            BnByond is more than just a platform; it's a community that values
            active listening, open communication, and a deep understanding of
            members' needs and aspirations. We want you to get the most out of
            your STR property.{" "}
            <span className="font-bold">Free travel is a pretty great perk!</span>
          </p>
          <p className="md:mt-4 mt-2 md:text-2xl text-xl">
            Join BnByond today. Get the most out of your short-term rental. Make
            the World Your Happy Place! -
          </p>
          <h2 className="md:my-8 my-4 md:text-5xl text-lg font-normal text-cyan-400 text-center">
            Questions? <EMAIL>
          </h2>
        </div>
        <div className="w-full">
          <Link>
            <img
              src={BottomImage}
              alt="Bottom Image"
              className="w-full h-auto object-cover"
            />
          </Link>
        </div>
      </div>

      {/* PDF Button – Only shows when at bottom */}
      {showPdfButton && (
        <div className="fixed right-8 bottom-[11.5rem] z-50">
          <a
            href="/HowItWorksPdf.pdf"
            className="bg-color-red text-white md:p-4 p-2 border-2 border-white rounded-full flex items-center justify-center shadow-lg"
            title="Download as PDF"
            download={"HowItWorksPdf.pdf"}
          >
            <FaFileDownload size={20} className="mr-2" />
            Download as Pdf
          </a>
        </div>
      )}
    </>
  );
}

function SectionCard({ title, image, items }) {
  return (
    <div className="flex items-start bg-white relative mt-10 max-w-[98%] lg:max-w-[74%] mx-auto">
      <div className="flex flex-col w-full px-6">
        <h2 className="text-3xl font-bold mb-8">{title}</h2>
        <div className="flex flex-col lg:flex-row">
          <div className="w-full lg:w-1/4 mr-2 flex-shrink-0">
            <img
              src={image}
              alt={title}
              className="w-[200px] h-[150px] lg:w-[190px] lg:h-[190px] object-cover rounded-lg shadow-lg"
            />
          </div>
          <div className="w-full lg:w-3/4">
            <ul className="list-disc mt-3 lg:mt-0 lg:pl-5 text-gray-700 space-y-1">
              {items.map((item, index) => (
                <li key={index} className="text-black">
                  <span className="font-bold">{item.label}</span>
                  <span className="font-thin pl-2">{item.text}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
