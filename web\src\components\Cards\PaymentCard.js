import React from 'react';

export const PaymentCard = ({ name, icon, number, verification, funding, country, cardType }) => {
    return (
        <div className="flex flex-col md:flex-row gap-2 border border-[#AFB5C1] p-4 rounded-2xl shadow-md bg-white transition-shadow duration-300 ease-in-out">
            {/* Card Icon */}
            <div className="flex items-center justify-center md:justify-start mb-2 md:mb-0">
                <img className="w-[160px] " src={icon} alt={`${cardType} card`} />
            </div>

            {/* Card Details */}
            <div className="flex flex-col px-6 justify-between w-full">
                <div>
                    <p className="text-xl font-medium capitalize text-gray-900">{name}</p>
                    <p className="text-base font-normal text-gray-700">Bank <span>{number}</span></p>
                </div>

                {/* Additional Card Info */}
                <div className="my-1 grid grid-cols-3 gap-2 text-sm text-gray-600 border-t pt-3">
                    <div className="flex flex-col items-start">
                        <span className="text-xs text-gray-500">Card Type</span>
                        <span className="font-medium text-gray-800">{cardType ? cardType.toUpperCase() : "Unknown"}</span>
                    </div>

                    <div className="flex flex-col items-start">
                        <span className="text-xs text-gray-500">Funding</span>
                        <span className="font-medium text-gray-800">{funding ? funding.charAt(0).toUpperCase() + funding.slice(1) : "N/A"}</span>
                    </div>

                    <div className="flex flex-col items-start">
                        <span className="text-xs text-gray-500">Country</span>
                        <span className="font-medium text-gray-800">{country || "N/A"}</span>
                    </div>
                </div>


                {/* Verification Status */}
                <p className="text-base font-normal text-[#2459BF] pt-2 capitalize">{verification}</p>
            </div>
        </div>
    );
};
