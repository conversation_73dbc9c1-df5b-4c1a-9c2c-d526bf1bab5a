import mongoose from "mongoose";

const propertySchema = new mongoose.Schema({
  amenities: { type: Array },

  characteristics: { type: Array },
  pics: { type: Array },

  points: [
    {
      date: { type: Date, required: true },
      point: { type: Number, required: true },
    },
  ],
  price: { type: Number },
  guestPrice: [
    {
      date: { type: Date, required: true },
      point: { type: Number, required: true },
    },
  ],
  cleaningFee: { type: Number },
  discountPrice: { type: Number },
  amenitiesFee: { type: Number },

  userId: { type: mongoose.Schema.Types.ObjectId, ref: "Users" },

  address: { type: String },
  fullAddress:{
    street:{type:String},
    country: { type: String },
    city: { type: String },
    state: { type: String },
    zip: { type: String },
  },

  propertyType: { type: String },

  spaceType: { type: String },
  title: { type: String },
  userCurrency: { type: String },
  loc: {
    type: Object,
  },
  spaceTypeDetail: {
    type: Object,
  },
  description: {
    type: String,
  },
  listing: {
    type: String,
  },
  isPropertyAvaibleOnOtherSites: { type: []},
  status: { type: String, default: "Pending" },
  hasBeenActivatedBefore: { type: Boolean, default: false },
  isAvailable: {
    type: Boolean,
    defult: true,
  },
  bookDates: { type: [] },
  IsTestedProperty: { type: Boolean, default: false },
  externalSources: [String], // Store external iCal URLs for reference
  favouriteList: {
    type: [
      {
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Users",
        },
      },
    ],
  },
  timeStamp: {
    type: Date,
    default: Date.now,
  },
  reviews: [
    {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Users",
        required: true,
      },
      reservationId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "reservation",
      },
      rating: { type: Number, required: true, min: 1, max: 5 },
      comment: { type: String, required: true },
      date: { type: Date, default: Date.now },
    },
  ],
});

propertySchema.index({ loc: "2dsphere" });

export const Property = mongoose.model("property", propertySchema);
