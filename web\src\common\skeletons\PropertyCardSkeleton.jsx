import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const PropertyCardSkeleton = ({ cards = 1 }) => {
  return Array(cards)
    .fill(0)
    .map((_, index) => (
      <div 
        key={index} 
        className="mx-1 my-1 xs:w-full sm:w-[265px] md:w-[265px] w-full"
      >
        <div className="rounded-lg overflow-hidden bg-white shadow-md h-full">
          {/* Image skeleton */}
          <Skeleton height={180} width="100%" />
          
          {/* Rating and heart icon */}
          <div className="p-4">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center">
                <Skeleton width={100} height={20} />
              </div>
              <Skeleton circle width={24} height={24} />
            </div>
            
            {/* Property name */}
            <Skeleton width="60%" height={24} className="mb-2" />
            
            {/* Location */}
            <Skeleton width="80%" height={20} className="mb-3" />
            
            {/* Details */}
            <div className="flex gap-2 mb-3">
              <Skeleton width={100} height={16} />
              <Skeleton width={100} height={16} />
            </div>
            
            {/* Price */}
            <div className="flex items-center justify-between mt-4">
              <Skeleton width={60} height={16} />
              <Skeleton width={100} height={24} />
            </div>
          </div>
        </div>
      </div>
    ));
};

export default PropertyCardSkeleton;