import mongoose from 'mongoose';


const InnovatorSchema = new mongoose.Schema({
  fname: { type: String, required: true },
  lname: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  state1: { type: String, required: true },
  state2: { type: String, required: false },
  state3: { type: String, required: false },
  state4: { type: String, required: false },
  state5: { type: String, required: false },
}, { timestamps: true });

export const innovator = mongoose.model('Innovatorstate', InnovatorSchema);
