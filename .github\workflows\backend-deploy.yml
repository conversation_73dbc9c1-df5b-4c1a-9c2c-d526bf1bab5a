name: Backend AWS Deploy Action

on:
  workflow_call:
    secrets:
      sentry_dsn:
        required: true
      aws_access_key_id:
        required: true
      aws_secret_access_key:
        required: true
      s3_aws_access_key_id:
        required: true
      s3_aws_secret_access_key:
        required: true
      mongo_uri:
        required: true
      token_key:
        required: true
      twilio_account_id:
        required: true
      twilio_auth_token:
        required: true
      twilio_test_number:
        required: true
      mailjet_key1:
        required: true
      mailjet_key2:
        required: true
      stripe_secret_key:
        required: true
      stripe_public_key:
        required: true
      secret_encryption_key:
        required: true
      subscription_withaffiliated:
        required: true
      subscription_withoutaffiliated:
        required: true
      stripe_webhook_secret:
        required: true
    inputs:
      environment:
        required: true
        type: string
      ecr_repository:
        required: true
        type: string
      ecs_cluster:
        required: true
        type: string
      ecs_service:
        required: true
        type: string

env:
  AWS_REGION: us-east-1

jobs:
  publish:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.aws_access_key_id }}
          aws-secret-access-key: ${{ secrets.aws_secret_access_key }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Get Git commit SHA
        id: git-sha
        run: echo "SHA=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
      
      - name: Build and push
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          docker build \
            --build-arg NODE_ENV="production" \
            --build-arg SENTRY_DSN="${{ secrets.sentry_dsn }}" \
            --build-arg MONGO_URI="${{ secrets.mongo_uri }}" \
            --build-arg TOKEN_KEY="${{ secrets.token_key }}" \
            --build-arg ACCOUNTID_TWILIO="${{ secrets.twilio_account_id }}" \
            --build-arg AUTHTOKEN_TWILIO="${{ secrets.twilio_auth_token }}" \
            --build-arg TESTNO_TWILIO="${{ secrets.twilio_test_number }}" \
            --build-arg MAIL_JET_KEY1="${{ secrets.mailjet_key1 }}" \
            --build-arg MAIL_JET_KEY2="${{ secrets.mailjet_key2 }}" \
            --build-arg STRIPE_SECRET_KEY="${{ secrets.stripe_secret_key }}" \
            --build-arg STRIPE_PUBLIC_KEY="${{ secrets.stripe_public_key }}" \
            --build-arg SECRET_ENCRYPTION_KEY="${{ secrets.secret_encryption_key }}" \
            --build-arg SUBSCRIPTION_WITHAFFILIATED="${{ secrets.subscription_withaffiliated }}" \
            --build-arg SUBSCRIPTION_WITHOUTAFFLIATED="${{ secrets.subscription_withoutaffiliated }}" \
            --build-arg STRIPE_WEBHOOK_SECRET="${{ secrets.stripe_webhook_secret }}" \
            --build-arg WEBSITE_LINK="https://beta.bnbyond.com" \
            --build-arg FRONTEND_LINK="https://beta.bnbyond.com" \
            --build-arg BACKEND_LINK="https://api.bnbyond.com" \
            --build-arg GIT_COMMIT_SHA="${{ steps.git-sha.outputs.SHA }}" \
            --build-arg S3_AWS_ACCESS_KEY_ID="${{ secrets.s3_aws_access_key_id }}" \
            --build-arg S3_AWS_SECRET_ACCESS_KEY="${{ secrets.s3_aws_secret_access_key }}" \
            --build-arg AWS_REGION="${{ env.AWS_REGION }}" \
            -f deploy/backend/Dockerfile \
            -t $ECR_REGISTRY/${{ inputs.ecr_repository }}:latest .
          docker push $ECR_REGISTRY/${{ inputs.ecr_repository }}:latest
      
      - name: Force ECS Update
        run: |
          aws ecs update-service \
            --cluster ${{ inputs.ecs_cluster }} \
            --service ${{ inputs.ecs_service }} \
            --force-new-deployment
