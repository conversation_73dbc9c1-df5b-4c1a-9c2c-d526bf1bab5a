import React from "react";

const Chat = () => {
  return (
    <>
      <div className="container mx-auto rounded-lg shadow-lg">
        <div className="flex flex-row justify-between bg-white">
          <div className="flex flex-col w-2/5 overflow-y-auto border-4 border-r-2 border-red-700">
            <div className="px-2 py-4 border-b-2">
              <div className="flex justify-between items-center text-black font-Avenir text-lg font-medium leading-[1.166]">
                <p>Messages</p>
                <div>:</div>
              </div>
              <div>
                <input
                  type="text"
                  placeholder="search chatting"
                  className="w-full px-2 py-2 border-2 border-gray-200 rounded-2xl"
                />
              </div>
            </div>
            <div className="flex flex-row items-center justify-center px-2 py-4 border-b-2">
              <div className="w-1/4">
                <img
                  src="https://source.unsplash.com/_7LbC5J-jw4/600x600"
                  className="object-cover w-12 h-12 rounded-full"
                  alt
                />
              </div>
              <div className="w-full">
                <div className="text-lg font-semibold">Kristin Watson</div>
                <span className="text-gray-500">Haha that's terrifying 😂</span>
              </div>
            </div>
            <div className="flex flex-row items-center px-2 py-4 border-b-2">
              <div className="w-1/4">
                <img
                  src="https://source.unsplash.com/otT2199XwI8/600x600"
                  className="object-cover w-12 h-12 rounded-full"
                  alt
                />
              </div>
              <div className="w-full">
                <div className="text-lg font-semibold">Nela Scarlett</div>
                <span className="text-gray-500">Haha that's terrifying 😂</span>
              </div>
            </div>
            <div className="flex flex-row items-center px-2 py-4 bg-blue-100 border-b-2 border-l-4">
              <div className="w-1/4">
                <img
                  src="https://source.unsplash.com/L2cxSuKWbpo/600x600"
                  className="object-cover w-12 h-12 rounded-full"
                  alt
                />
              </div>
              <div className="w-full">
                <div className="text-lg font-semibold">MERN Stack</div>
                <span className="text-gray-500">Lusi : Thanks Everyone</span>
              </div>
            </div>
            <div className="flex flex-row items-center px-2 py-4 border-b-2">
              <div className="w-1/4">
                <img
                  src="https://source.unsplash.com/vpOeXr5wmR4/600x600"
                  className="object-cover w-12 h-12 rounded-full"
                  alt
                />
              </div>
              <div className="w-full">
                <div className="text-lg font-semibold">
                  Javascript Indonesia
                </div>
                <span className="text-gray-500">
                  Evan : some one can fix this
                </span>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-between w-full px-5">
            <div className="flex items-center justify-between px-5 py-5 bg-white border-b-2">
              <div className="flex items-center justify-center font-semibold ">
                <img
                  src="https://source.unsplash.com/vpOeXr5wmR4/1200x1200"
                  className="object-cover w-12 h-12 rounded-full"
                  alt
                />
                <div className="mx-3">
                  <p className="text-lg font-medium leading-6 text-black uppercase font-avenir">
                    Neha Scarlet
                  </p>
                  <p className="text-black font-lato text-xs font-normal leading-[124.5%] capitalize">
                    Online
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-col mt-5">
              <div className="flex justify-end mb-4">
                <div className="px-4 py-3 mr-2 text-white bg-blue-400 rounded-bl-3xl rounded-tl-3xl rounded-tr-xl">
                  Welcome to group everyone !
                </div>
                <img
                  src="https://source.unsplash.com/vpOeXr5wmR4/600x600"
                  className="object-cover w-8 h-8 rounded-full"
                  alt
                />
              </div>
              <div className="flex justify-start mb-4">
                <img
                  src="https://source.unsplash.com/vpOeXr5wmR4/600x600"
                  className="object-cover w-8 h-8 rounded-full"
                  alt
                />
                <div className="px-4 py-3 ml-2 text-white bg-gray-400 rounded-br-3xl rounded-tr-3xl rounded-tl-xl">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit.
                  Quaerat at praesentium, aut ullam delectus odio error sit rem.
                  Architecto nulla doloribus laborum illo rem enim dolor odio
                  saepe, consequatur quas?
                </div>
              </div>
              <div className="flex justify-end mb-4">
                <div>
                  <div className="px-4 py-3 mr-2 text-white bg-blue-400 rounded-bl-3xl rounded-tl-3xl rounded-tr-xl">
                    Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                    Magnam, repudiandae.
                  </div>
                  <div className="px-4 py-3 mt-4 mr-2 text-white bg-blue-400 rounded-bl-3xl rounded-tl-3xl rounded-tr-xl">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit.
                    Debitis, reiciendis!
                  </div>
                </div>
                <img
                  src="https://source.unsplash.com/vpOeXr5wmR4/600x600"
                  className="object-cover w-8 h-8 rounded-full"
                  alt
                />
              </div>
              <div className="flex justify-start mb-4">
                <img
                  src="https://source.unsplash.com/vpOeXr5wmR4/600x600"
                  className="object-cover w-8 h-8 rounded-full"
                  alt
                />
                <div className="px-4 py-3 ml-2 text-white bg-gray-400 rounded-br-3xl rounded-tr-3xl rounded-tl-xl">
                  happy holiday guys!
                </div>
              </div>
            </div>
            <div className="py-5">
              <input
                className="w-full px-3 py-5 bg-gray-300 rounded-xl"
                type="text"
                placeholder="type your message here..."
              />
            </div>
          </div>

          <div className="w-2/5 px-5 border-l-2">
            <div className="flex flex-col">
              <div className="py-4 text-xl font-semibold">Mern Stack Group</div>
              <img
                src="https://source.unsplash.com/L2cxSuKWbpo/600x600"
                className="object-cover h-64 rounded-xl"
                alt
              />
              <div className="py-4 font-semibold">Created 22 Sep 2021</div>
              <div className="font-light">
                Lorem ipsum dolor sit amet consectetur adipisicing elit.
                Deserunt, perspiciatis!
              </div>
            </div>
          </div>
        </div>
        &nbsp;&nbsp;&nbsp;
      </div>
    </>
  );
};

export default Chat;
