import React, { useState } from 'react';
import Slider from '@mui/material/Slider';

const PriceFilter = ({ minValue, maxValue }) => {
  const [priceRange, setPriceRange] = useState({ min: minValue, max: maxValue });

  const handleChange = (event, value) => {
    setPriceRange(value);
  };

  return (
    <div>
      <Slider
        draggableTrack
        max={maxValue}
        min={minValue}
        onChange={handleChange}
        value={priceRange}
      />
      <div className="flex gap-4 my-2">
        <div className="p-2 w-1/2 text-start border border-gray-400 rounded-md">
          <p className="text-xs">Minimum</p>
          <p className="font-semibold">$ {priceRange.min}</p>
        </div>
        <div className="p-2 w-1/2 text-start border border-gray-400 rounded-md">
          <p className="text-xs">Maximum</p>
          <p className="font-semibold">$ {priceRange.max}</p>
        </div>
      </div>
    </div>
  );
};

export default PriceFilter;
