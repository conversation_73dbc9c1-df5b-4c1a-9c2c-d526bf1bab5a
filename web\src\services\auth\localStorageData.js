import { config } from 'config.js';

/**
 * Retrieves a specific value from local storage.
 * @param {string} key - The key to retrieve from local storage.
 * @returns {*} The value associated with the key, or null if not found.
 */
export function localStorageData(key) {
  const localData = JSON.parse(localStorage.getItem(config.localToken)) || {};
  return localData[key] || null;
}

/**
 * Retrieves the entire local storage data.
 * @returns {Object|null} The stored object or null if no data exists.
 */
export function getLocalUserdata() {
  return JSON.parse(localStorage.getItem(config.localToken)) || null;
}

/**
 * Stores data in local storage.
 * @param {Object} data - The data object to store.
 */
export function storeLocalData(data) {
  if (data && typeof data === 'object') {
    localStorage.setItem(config.localToken, JSON.stringify(data));
  }
}

/**
 * Logs the user out by removing stored data from local storage.
 * @returns {boolean} Always returns true.
 */
export function Logout() {
  localStorage.removeItem(config.localToken);
  return true;
}
