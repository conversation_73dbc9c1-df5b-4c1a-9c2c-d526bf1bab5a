name: Frontend deployment pipeline

on:
  push:
    branches: 
      - main
    paths:
      - 'web/**'
      - 'deploy/frontend/**'
      - '.github/workflows/frontend-*.yml'
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'main'

jobs:
  deploy:
    uses: ./.github/workflows/frontend-deploy.yml
    secrets:
      aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      react_app_map_key: ${{ secrets.REACT_APP_MAP_KEY }}
      react_app_google_api: ${{ secrets.REACT_APP_GOOGLE_API }}
      react_app_secret_encryption_key: ${{ secrets.REACT_APP_SECRET_ENCRYPTION_KEY }}
      react_app_standard_client_id: ${{ secrets.REACT_APP_STANDARD_CLIENT_ID }}
      react_app_google_analytic_id: ${{ secrets.REACT_APP_GOOGLE_ANALYTICS_MEASURMENT_ID }}
      react_app_texteditor_tiny_key: ${{ secrets.REACT_APP_TEXTEDITOR_TINY_KEY }}
      react_app_stripe_publishkey: ${{ secrets.REACT_APP_STRIPE_PUBLISH_KEY }}
    with:
      environment: production
      ecr_repository: prod-bnbyond-web
      ecs_cluster: bnbyond-prod
      ecs_service: bnbyond-web-service
      react_app_endpoint: ${{ vars.REACT_APP_ENDPOINT }}
      react_app_base_url: ${{ vars.REACT_APP_BASE_URL }}
      react_app_image_endpoint: ${{ vars.REACT_APP_IMAGE_ENDPOINT }}
