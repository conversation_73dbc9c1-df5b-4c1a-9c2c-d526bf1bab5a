import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Rating from 'react-rating';
import { FaRegStar, FaStar, FaStarHalfAlt } from "react-icons/fa";

export default function SearchCard({ data, isHighlighted }) {
  let parsedData;

  if (typeof data?.spaceTypeDetail === "string") {
    try {
      parsedData = JSON.parse(data.spaceTypeDetail);
    } catch (error) {
      console.error("Error parsing JSON:", error);
    }
  } else {
    parsedData = data?.spaceTypeDetail;
  }

  const navigate = useNavigate();

  const handleClick = () => {
    const currentSearch = window.location.search;

  navigate(`/room/${data._id}${currentSearch}`);
  };

  const [averageRating, setAverageRating] = useState(0);

  useEffect(() => {
    if (data?.reviews && data.reviews.length > 0) {
      const ratings = data.reviews.map((review) => review.rating);
      const total = ratings.reduce((acc, rating) => acc + rating, 0);
      const average = total / ratings.length;
      setAverageRating(Number(average.toFixed(1)));
    } else {
      setAverageRating(0);
    }
  }, [data?.reviews]);

  const minPoint =
    Array.isArray(data.points) && data.points.length > 0
      ? data.points.reduce((min, p) => (p.point < min ? p.point : min), data.points[0].point)
      : null;

  return (
    <div
      className={`flex flex-col sm:flex-row w-full my-3 relative cursor-pointer transition-all duration-300 ${isHighlighted ? "bg-neutral-200" : "bg-white"
        } p-3 rounded-lg`}
      key={data._id}
      onClick={handleClick}
    >
      <div className="w-full sm:w-1/3">
        <img
          src={data.pics[0]}
          className="w-full h-40 object-cover rounded"
          alt="review card"
        />
      </div>

      <div className="flex-1 sm:mx-3 relative">
        {averageRating > 0 ? (
          <div className="flex items-center">
            <Rating
              initialRating={averageRating}
              readonly
              emptySymbol={<FaRegStar color="#d3d3d3" />}
              halfSymbol={<FaStarHalfAlt color="#ffd700" />}
              fullSymbol={<FaStar color="#ffd700" />}
              fractions={2}
            />
            <span className="ml-2">({averageRating})</span>
          </div>
        ) : (
          <span className="text-[#AFB5C1]">Not Rated Yet.</span>
        )}

        <h2 className="text-lg font-semibold hover:text-blue-700 mt-1">
          {data.title}
        </h2>
        <div className="text-sm font-semibold">{data.address}</div>
        <div className="text-sm text-gray-400">
          bath: {parsedData?.bathrooms} bedrooms: {parsedData?.bedrooms} guests:{" "}
          {parsedData?.guests}
        </div>

        <div className="mt-2 text-lg text-rose-800 flex justify-between items-center font-semibold">
          <p>From</p>
          <p>{minPoint} Points/Night</p>
        </div>
      </div>
    </div>

  );
}

