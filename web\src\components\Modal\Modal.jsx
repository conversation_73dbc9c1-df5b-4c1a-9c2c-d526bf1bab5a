import React from "react";

const Modal = ({ isOpen, onClose, children, width, height }) => {
  const modalStyle = isOpen ? "block" : "hidden";

  return (
    <div className={`${modalStyle} fixed inset-0 z-50 flex items-center justify-center`}>
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black opacity-50"
        onClick={onClose}
      ></div>

      {/* Modal Content */}
      <div
        className={`
          relative z-50 bg-white rounded-[20px] shadow-md overflow-y-auto
          w-[90%] sm:w-[80%] md:w-[60%] lg:w-[500px]
          max-h-[90vh]
        `}
        style={{
          width: width || undefined,
          height: height || undefined,
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default Modal;
