import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useMutation } from "react-query";
import { toast } from "react-toastify";
import { LuCopy } from "react-icons/lu";
import { FaWhatsapp, FaFacebook, FaTwitter, FaSpinner } from "react-icons/fa";
import { MdEmail, MdMessage } from "react-icons/md";
import { FaXmark } from "react-icons/fa6";
import icon from "assets/img/icon.png";

// Assets
import ReferralPic from "assets/img/ReferralPic.png";
import SOFA from "assets/img/sofa.avif";
import backgroundImage from "assets/img/backgroundImage.png";
import referalpoint from "assets/img/referalpoint.png";
import PintsPic from "assets/img/PintsPic.png";
import CirclePic from "assets/img/CirclePic.png";
import STR from "assets/img/StrInnovate.jpg";
import ReferralsIcon from "assets/img/ReferralsIcon.png";

// Components
import Modal from "components/Modal/Modal";
import { GraphComponent } from 'common';

// Services
import userServices from 'services/httpService/userAuth/userServices';
import { localStorageData } from "services/auth/localStorageData";
import ErrorService from 'services/formatError/ErrorService';

const Referrals = () => {
  // URL handling
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // State
  const [userData, setUserData] = useState(null);
  const [referralData, setReferralData] = useState({ users: [], totalPages: 0, currentPage: 1, totalReferred: 0 });
  const [isCopied, setIsCopied] = useState(false);
  const [referralPage, setReferralPage] = useState(1);

  // User data
  const userId = localStorageData("_id");
  const userReferralCode = localStorageData("referralCode");
  const userRoles = localStorageData("roles");
  const isInfluencer = userRoles?.includes("Influencer");

  // Determine modal state from URL on component mount
  useEffect(() => {
    const modalParam = searchParams.get('modal');
    if (modalParam === 'refer') {
      openReferralCodeModal();
    } else if (modalParam === 'history') {
      openReferralsModal();
    }
  }, []);

  // Modal control with URL updates
  const openReferralCodeModal = () => {
    setSearchParams({ modal: 'refer' });
  };

  const closeReferralCodeModal = () => {
    setSearchParams({});
    setIsCopied(false);
  };

  const openReferralsModal = () => {
    setSearchParams({ modal: 'history' });
    if (!referralData.users.length) {
      fetchReferral(1);
    }
  };

  const closeReferralsModal = () => {
    setSearchParams({});
  };

  // Check if modals should be open based on URL
  const isReferralCodeModalOpen = searchParams.get('modal') === 'refer';
  const isReferralsModalOpen = searchParams.get('modal') === 'history';

  // Navigation
  const navigateToInnovator = () => {
    navigate('/str-innovator');
  };

  const handleButtonClick = () => {
    if (isInfluencer) {
      navigateToInnovator();
    } else {
      openReferralCodeModal();
    }
  };

  // API calls with react-query
  const { mutate: fetchUser, isLoading: isUserLoading } = useMutation({
    mutationFn: () => userServices.userById(`/userAuth/user/${userId}`),
    onSuccess: (res) => {
      if (res?.data) {
        setUserData(res.data);
      }
    },
    onError: (error) => {
      toast.error(ErrorService.uniformError(error));
    }
  });

  const { mutate: fetchReferral, isLoading: isReferralLoading } = useMutation({
    mutationFn: (page = 1) =>
      userServices.getReferralUser(`/userAuth/getrefferduser?id=${userId}&page=${page}`),
    onSuccess: (res) => {
      if (res?.data) {
        setReferralData(res.data);
      }
    },
    onError: (error) => {
      toast.error(ErrorService.uniformError(error));
    },
  });

  // Fetch data on component mount
  useEffect(() => {
    if (userId) {
      fetchUser();
      // Only fetch referrals if we're showing the referrals modal
      if (isReferralsModalOpen) {
        fetchReferral(referralPage);
      }
    }
  }, [userId]);

  // Handle pagination for referrals
  const handlePageChange = (page) => {
    if (page < 1 || (referralData.totalPages && page > referralData.totalPages)) return;
    setReferralPage(page);
    fetchReferral(page);
  };

  // Clipboard functions
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setIsCopied(true);

      // Reset the "Copied" status after 3 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 3000);
    } catch (err) {
      toast.error('Failed to copy text');
    }
  };

  // Referral URL generation with tracking
  const getReferralUrl = (source) => {
    if (!userReferralCode) return '';
    const sourceSpecificCode = source ? `${userReferralCode}-${source}` : userReferralCode;
    return `https://beta.bnbyond.com/auth/signup?referralCode=${sourceSpecificCode}`;
  };

  // Default referral URL (for copy link)
  const referralUrl = getReferralUrl('direct');

  // Share functions for different platforms
  const shareViaEmail = () => {
    const emailUrl = `mailto:?subject=Join BnByond&body=I think you'd love BnByond! Sign up using my referral link: ${getReferralUrl('email')}`;
    window.open(emailUrl, '_blank');
  };

  const shareViaWhatsapp = () => {
    const whatsappUrl = `https://wa.me/?text=I think you'd love BnByond! Sign up using my referral link: ${getReferralUrl('whatsapp')}`;
    window.open(whatsappUrl, '_blank');
  };

  const shareViaFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(getReferralUrl('facebook'))}`;
    window.open(facebookUrl, '_blank');
  };

  const shareViaTwitter = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=I think you'd love BnByond! Sign up using my referral link:&url=${encodeURIComponent(getReferralUrl('twitter'))}`;
    window.open(twitterUrl, '_blank');
  };

  const shareViaMessages = () => {
    const messageUrl = `sms:?&body=I think you'd love BnByond! Sign up using my referral link: ${getReferralUrl('sms')}`;
    window.open(messageUrl, '_blank');
  };

  // Render loading states
  if (isUserLoading && !userData) {
    return <div className="flex justify-center items-center min-h-screen">
      <img src={icon} className="w-28 h-28 animate-spin "></img>
    </div>
  }

  return (
    <>
      <div className='flex flex-col gap-8 md:flex-row md:my-16 md:mx-20 md:gap-20'>
        <div className='md:w-[50%] pl-5 pr-5 md:pl-10 md:pr-5 mt-14'>
          <div className='flex items-center gap-2'>
            <h2 className='text-2xl font-bold leading-tight text-yellow-500 font-avenir'>
              {isInfluencer ? "STR Innovator Program" : "Member Referral Program"}
            </h2>
            <img src={ReferralsIcon} className='w-7 h-7' alt='Referrals Icon' />
          </div>
          <p className='mt-1 text-lg leading-7' style={{ fontFamily: 'Avenir' }}>
            {isInfluencer
              ? "Earn stays at STR properties by simply introducing BnByond"
              : "Increase your points and unlock travel rewards by inviting friends to join BnByond!"}
          </p>

          <div className='flex flex-col md:flex-row gap-4 pb-5 mt-3'>
            <button
              onClick={handleButtonClick}
              className="bg-[#b04130] text-white font-bold md:text-xl text-lg md:py-4 py-2 md:px-4 px-2 rounded-2xl md:w-[220px]"
            >
              Get Your Shareable Code/Link
            </button>
            <button
              onClick={openReferralsModal}
              className="bg-[#3f88c5] text-white font-bold md:text-xl text-lg md:py-4 py-2 md:px-4 px-2 rounded-2xl md:w-[180px]"
            >
              View Your Referrals
            </button>
          </div>
        </div>

        <div className='relative md:w-[20%]'>
          <img src={ReferralPic} alt='Referral' />
          <img src={CirclePic} className='absolute top-0 -z-10' alt='Circle' />
          <img src={PintsPic} className='absolute bottom-10 -left-4 w-[120px]' alt='Points' />
        </div>
        <div>
          <img src={STR} alt='STR' className='w-[260px]' />
        </div>
      </div>

      <div className='w-full pt-2 bg-color-darkgray lg:h-72'>
        <p className='text-center mt-5 text-[36px] leading-10 font-medium font-avenir'>
          It doesn't get more simple.
        </p>
        <div className='flex justify-center w-full'>
          <div className='flex flex-col md:flex-row md:justify-between md:mx-4 mt-10 w-[82%]'>
            {/* Steps 1, 2, and 3 */}
            {[
              {
                title: 'Share Your Code',
                description: "Promote your code to your audience, client base or members"
              },
              {
                title: 'They join',
                description: 'Clicking your shareable code will bring them directly to register. They will complete their profile and earn bonus points to use right away!'
              },
              {
                title: 'Thats it!',
                description: "Seriously. That's it. You will automatically earn 50 points for every new paid Member using your code to join BnByond. Make the World you Happy Place!"
              }
            ].map((step, index) => (
              <div key={index} className='flex w-full mt-6 mb-8 md:mx-2 md:mb-0'>
                <div className='w-[50px] h-[50px] border-solid border-white border-2 rounded-full flex justify-center items-center bg-color-blue text-white'>
                  <p>{index + 1}</p>
                </div>

                <div className='flex flex-col items-start ml-4 w-[90%]'>
                  <div className='text-lg md:text-xl font-medium text-black font-avenir normal-italic'>
                    {step.title}
                  </div>
                  <div className='mt-1 text-sm'>
                    {step.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className='flex items-center justify-center my-10'>
        <div className='w-full md:w-4/5 lg:gap-96 flex flex-col md:flex-row items-center lg:h-40 bg-cover bg-center xs:h-[300px] xs:flex xs:justify-center xs:items-center xs:gap-[20px] md:h-40'
          style={{ backgroundImage: `url(${backgroundImage})` }}>
          <div className='flex gap-4 pl-5'>
            <img
              className="w-[100px] h-[100px] rounded-full object-cover"
              src={userData?.pic}
              alt="Profile"
            />
            <div className='flex flex-col justify-center text-white'>
              <h3 className='text-lg font-bold'>{`${userData?.fname || ''} ${userData?.lname || ''}`}</h3>
              <p className='text-sm font-normal'>Booking with your earning points</p>
            </div>
          </div>
          <div className='flex gap-6'>
            <img
              className="object-cover"
              src={referalpoint}
              alt="Referral Points"
            />
            <h2 className='flex items-center justify-center text-2xl font-semibold text-white font-avenir'>
              {userData?.points || 0} Points
            </h2>
          </div>
        </div>
      </div>

      {/* Referral Share Modal */}
      <Modal isOpen={isReferralCodeModalOpen} onClose={closeReferralCodeModal}>
        <div className='relative flex flex-col items-center py-8 px-6 max-w-md mx-auto'>
          {/* Close button */}
          <button
            onClick={closeReferralCodeModal}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
          >
            <FaXmark size={24} />
          </button>

          <h2 className="text-2xl font-semibold mb-6 text-center">
            Share your referral
          </h2>

          {/* Logo and message */}
          <div className="flex items-center w-full mb-8">
            <div className="flex-shrink-0 w-14 h-14 rounded-lg flex items-center justify-center mr-4">
              <img
                className="w-10 h-10"
                src="https://bnbpictures.s3.us-east-1.amazonaws.com/favicon.ico"
                alt="BnByond Logo"
              />
            </div>
            <p className="text-gray-800 text-lg">
              You'd be a great BnByond traveler!
            </p>
          </div>

          {/* Sharing options grid */}
          <div className="grid grid-cols-2 gap-4 w-full">
            {/* Copy Link button */}
            <button
              onClick={() => copyToClipboard(referralUrl)}
              className="flex items-center justify-center gap-3 py-3 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <LuCopy size={20} />
              <span>Copy Link</span>
            </button>

            {/* Email button */}
            <button
              onClick={shareViaEmail}
              className="flex items-center justify-center gap-3 py-3 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <MdEmail size={20} />
              <span>Email</span>
            </button>

            {/* Messages button */}
            <button
              onClick={shareViaMessages}
              className="flex items-center justify-center gap-3 py-3 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <MdMessage size={20} />
              <span>Messages</span>
            </button>

            {/* WhatsApp button */}
            <button
              onClick={shareViaWhatsapp}
              className="flex items-center justify-center gap-3 py-3 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FaWhatsapp size={20} color="#25D366" />
              <span>WhatsApp</span>
            </button>

            {/* Messenger/Facebook button */}
            <button
              onClick={shareViaFacebook}
              className="flex items-center justify-center gap-3 py-3 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FaFacebook size={20} color="#1877F2" />
              <span>Facebook</span>
            </button>

            {/* Twitter/X button */}
            <button
              onClick={shareViaTwitter}
              className="flex items-center justify-center gap-3 py-3 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FaTwitter size={20} color="#1DA1F2" />
              <span>Twitter</span>
            </button>
          </div>

          {/* Show referral code below */}
          <div className='mt-8 w-full'>
            <p className="text-gray-700 mb-2 font-medium">Your Referral Code:</p>
            <div className='relative'>
              <input
                className='w-full h-12 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring focus:border-blue-500 pr-12 font-mono text-lg'
                type='text'
                value={userReferralCode || ''}
                readOnly
              />
              <button
                className='absolute inset-y-0 right-0 flex items-center px-3 text-gray-700 hover:text-blue-600'
                onClick={() => copyToClipboard(userReferralCode)}
              >
                <LuCopy className="w-5 h-5" />
              </button>
            </div>
          </div>

          {isCopied && (
            <div className="bg-green-100 text-green-800 px-4 py-2 rounded-md mt-4 text-center w-full">
              Link copied
            </div>
          )}
        </div>
      </Modal>

      {/* Referrals History Modal */}
      <Modal isOpen={isReferralsModalOpen} onClose={closeReferralsModal} width="75vh" height="85vh">
        <div className="relative flex flex-col h-full w-full max-w-7xl mx-auto px-6 py-8">
          {/* Close button */}
          <button
            onClick={closeReferralsModal}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
          >
            <FaXmark size={24} />
          </button>
          <h2 className="text-2xl font-semibold mb-4 text-center">
            Your referrals
          </h2>

          {/* Scrollable Content with Loading State */}
          <div className="flex-1 overflow-y-auto">
            {isReferralLoading ? (
              <div className="flex justify-center items-center py-20">
                <FaSpinner />
              </div>
            ) : referralData?.users?.length > 0 ? (
              <div className="px-4">
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-2">In progress</h3>
                  <p className="text-gray-500 mb-6">
                    These friends haven't hosted their first eligible booking yet.
                  </p>

                  {/* Referral list */}
                  {referralData.users.map((user) => (
                    <div key={user._id} className="flex items-center py-4 border-b border-gray-100 last:border-0">
                      {/* Profile image or initial */}
                      {user.pic && user.pic !== "https://bnbpictures.s3.amazonaws.com/emptyProfilepic.jpeg" ? (
                        <img
                          src={user.pic}
                          alt={`${user.fname} ${user.lname}`}
                          className="w-12 h-12 rounded-full object-cover mr-4"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-800 text-white rounded-full flex items-center justify-center text-xl font-medium mr-4">
                          {user.fname?.charAt(0).toUpperCase() || '?'}
                        </div>
                      )}

                      <div>
                        <p className="font-medium">{`${user.fname || ''} ${user.lname || ''}`}</p>
                        <div className="flex items-center">
                          <span className={`text-white text-xs px-2 py-1 rounded mr-2 ${user.hasListedProperty ? "bg-blue-600" : "bg-yellow-600"
                            }`}>
                            {user.hasListedProperty ? "Listing" : "Invited"}
                          </span>
                          <span className="text-gray-500 text-sm">
                            {user.hasListedProperty ? "Property published." : "waiting to publish."}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Pagination - only show if totalReferred > 5 */}
                  {referralData.totalReferred > 5 && (
                    <div className="flex justify-center items-center mt-8 mb-4">
                      <button
                        onClick={() => handlePageChange(referralData.currentPage - 1)}
                        disabled={referralData.currentPage === 1}
                        className={`px-3 py-1 rounded-md mr-2 ${referralData.currentPage === 1
                            ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                            : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                          }`}
                      >
                        Previous
                      </button>

                      <div className="flex space-x-1">
                        {Array.from({ length: referralData.totalPages }, (_, i) => i + 1).map((page) => (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`w-8 h-8 rounded-full flex items-center justify-center ${page === referralData.currentPage
                                ? "bg-blue-600 text-white"
                                : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                              }`}
                          >
                            {page}
                          </button>
                        ))}
                      </div>

                      <button
                        onClick={() => handlePageChange(referralData.currentPage + 1)}
                        disabled={referralData.currentPage === referralData.totalPages}
                        className={`px-3 py-1 rounded-md ml-2 ${referralData.currentPage === referralData.totalPages
                            ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                            : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                          }`}
                      >
                        Next
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center mt-8">
                <h3 className="text-2xl font-semibold mb-8 text-center">
                  You haven't referred anyone yet
                </h3>
                <div className="mb-12">
                  <img
                    src={SOFA}
                    alt="Cozy sofa with teddy bear"
                    className="w-72"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Sticky Footer Button */}
          <button
            onClick={() => {
              closeReferralsModal();
              openReferralCodeModal();
            }}
            className="bg-black text-white font-medium py-3 px-6 rounded-lg w-full mt-4"
          >
            Refer a host
          </button>
        </div>
      </Modal>
    </>
  );
};

export default Referrals;