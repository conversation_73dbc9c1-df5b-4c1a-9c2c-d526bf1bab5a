FROM node:20.11.0-alpine

# Set build arguments
ARG NODE_ENV
ARG SENTRY_DSN
ARG MONGO_URI
ARG TOKEN_KEY
ARG ACCOUNTID_TWILIO
ARG AUTHTOKEN_TWILIO
ARG TESTNO_TWILIO
ARG MAIL_JET_KEY1
ARG MAIL_JET_KEY2
ARG STRIPE_SECRET_KEY
ARG STRIPE_PUBLIC_KEY
ARG SECRET_ENCRYPTION_KEY
ARG SUBSCRIPTION_WITHAFFILIATED
ARG SUBSCRIPTION_WITHOUTAFFLIATED
ARG WEBSITE_LINK
ARG FRONTEND_LINK
ARG BACKEND_LINK
ARG STRIPE_WEBHOOK_SECRET
ARG GIT_COMMIT_SHA
ARG S3_AWS_SECRET_ACCESS_KEY
ARG S3_AWS_ACCESS_KEY_ID
ARG AWS_REGION

# Set environment variables
ENV NODE_ENV=$NODE_ENV \
    SENTRY_DSN=$SENTRY_DSN \
    MONGO_URI=$MONGO_URI \
    TOKEN_KEY=$TOKEN_KEY \
    ACCOUNTID_TWILIO=$ACCOUNTID_TWILIO \
    AUTHTOKEN_TWILIO=$AUTHTOKEN_TWILIO \
    TESTNO_TWILIO=$TESTNO_TWILIO \
    MAIL_JET_KEY1=$MAIL_JET_KEY1 \
    MAIL_JET_KEY2=$MAIL_JET_KEY2 \
    STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY \
    STRIPE_PUBLIC_KEY=$STRIPE_PUBLIC_KEY \
    SECRET_ENCRYPTION_KEY=$SECRET_ENCRYPTION_KEY \
    SUBSCRIPTION_WITHAFFILIATED=$SUBSCRIPTION_WITHAFFILIATED \
    SUBSCRIPTION_WITHOUTAFFLIATED=$SUBSCRIPTION_WITHOUTAFFLIATED \
    websiteLink=$WEBSITE_LINK \
    frontendLink=$FRONTEND_LINK \
    backendLink=$BACKEND_LINK \
    STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET \
    VERSION=$GIT_COMMIT_SHA \
    API_PORT=5000 \
    MONGO_Counter=4000 \
    AWS_SECRET_ACCESS_KEY=$S3_AWS_SECRET_ACCESS_KEY \
    AWS_ACCESS_KEY_ID=$S3_AWS_ACCESS_KEY_ID \
    AWS_REGION=$AWS_REGION

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY api/package*.json ./

# Install dependencies
RUN npm install --production=false

# Copy the rest of your application
COPY api .

RUN npm run build

# Expose the application port
EXPOSE 5000 

# Command to run the application
CMD ["npm", "start"]
