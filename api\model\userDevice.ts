import mongoose from "mongoose";

const userDeviceSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  deviceType: String,  // e.g., mobile, desktop
  browser: String,
  os: String,
  deviceInfo: Object,
  deviceFingerprint:String,  // Store full device info (optional)
  loginTimestamp: { type: Date, default: Date.now }
});

export const UserDevice = mongoose.model('UserDevice', userDeviceSchema);
