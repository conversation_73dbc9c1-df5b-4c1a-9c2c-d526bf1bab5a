import React, { useEffect, useState, useMemo, useCallback, useRef } from "react";
import Grid from "@mui/material/Grid";
import SearchCard from "components/Cards/SearchCard";
import { config } from "config.js";
import ErrorService from "services/formatError/ErrorService";
import userServices from "services/httpService/userAuth/userServices";
import GoogleMapReact from "google-map-react";
import { useLocation } from "react-router-dom";
import LandingSearchBar from "components/LandingSearchBar/LandingSearchBar";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { IoClose } from "react-icons/io5";
import { Pagination } from "@mui/material";
import dayjs from "dayjs";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const useOutsideClickDetector = (ref, callback) => {
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ref.current && 
          !ref.current.contains(event.target) && 
          !event.target.closest('.property-marker')) {
        callback();
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, callback]);
};

const PropertyMarker = ({ property, onSelect, isOpen, mapRef }) => {
  const location = useLocation();
  const [popupPosition, setPopupPosition] = useState("bottom");
  const popupRef = useRef(null);
  
  // Close popup when clicking outside
  useOutsideClickDetector(popupRef, () => {
    if (isOpen) onSelect(null);
  });

  const handlePropertyClick = useCallback(() => {
    const url = `/room/${property._id}${location.search}`;
    window.open(url, '_blank');
  }, [property._id, location.search]);

  const price = useMemo(() => {
    return property.points && property.points.length > 0
      ? property.points[0].point
      : property.price || 0;
  }, [property.points, property.price]);

  const totalPrice = useMemo(() => price * 7, [price]);

  const sliderSettings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    autoplay: false,
    adaptiveHeight: true,
    className: "property-slider",
    accessibility: false,
    draggable: false,
    swipe: false,
    touchMove: false,
  };

  const handleMarkerClick = useCallback((e) => {
    e.stopPropagation(); 
    onSelect(isOpen ? null : property);
    
    if (mapRef?.current && !isOpen) {
      const mapWidth = mapRef.current.getBoundingClientRect().width;
      const mapHeight = mapRef.current.getBoundingClientRect().height;
      
      // Get the x, y coordinates of the marker on the map
      const markerElement = document.getElementById(`marker-${property._id}`);
      if (markerElement) {
        const markerRect = markerElement.getBoundingClientRect();
        const markerX = markerRect.left - mapRef.current.getBoundingClientRect().left;
        const markerY = markerRect.top - mapRef.current.getBoundingClientRect().top;
        
        // Determine popup position based on marker location
        if (markerX < mapWidth * 0.3) {
          // Marker is on the left side - show popup on the right
          setPopupPosition("right");
        } else if (markerX > mapWidth * 0.7) {
          // Marker is on the right side - show popup on the left
          setPopupPosition("left");
        } else if (markerY < mapHeight * 0.3) {
          // Marker is at the top - show popup below
          setPopupPosition("bottom");
        } else {
          // Default position (marker is in middle or bottom) - show popup above
          setPopupPosition("top");
        }
      }
    }
  }, [isOpen, property, onSelect, mapRef]);

  const handleCloseClick = useCallback((e) => {
    e.stopPropagation();
    onSelect(null);
  }, [onSelect]);

  // Truncate text utility
  const truncateText = useCallback((text, wordLimit = 3) => {
    if (!text) return "";
    const words = text.split(" ");
    return words.length > wordLimit 
      ? words.slice(0, wordLimit).join(" ") + "..."
      : text;
  }, []);

  // Compute popup position styles based on calculated position
  const getPopupStyles = useMemo(() => {
    const baseStyles = {
      width: "270px",
      zIndex: 50,
      borderRadius: "0.75rem",
      boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    };
    
    switch (popupPosition) {
      case "right":
        return { ...baseStyles, left: "120%", top: "50%", transform: "translateY(-50%)" };
      case "left":
        return { ...baseStyles, right: "120%", top: "50%", transform: "translateY(-50%)" };
      case "top":
        return { ...baseStyles, bottom: "120%", left: "50%", transform: "translateX(-50%)" };
      case "bottom":
      default:
        return { ...baseStyles, top: "120%", left: "50%", transform: "translateX(-50%)" };
    }
  }, [popupPosition]);

  return (
    <div className="cursor-pointer relative property-marker">
      <div 
        id={`marker-${property._id}`} 
        onClick={handleMarkerClick}
        className="property-marker-icon"
      >
        <img
          src={require("assets/img/bnbyondmapiconborder.png")}
          className="lg:w-25 lg:h-25 xs:w-8 xs:h-8 rounded object-contain"
          alt="Property Marker"
        />
      </div>

      {isOpen && (
        <div
          ref={popupRef}
          className="absolute bg-white rounded-xl"
          style={getPopupStyles}
          onClick={(e) => e.stopPropagation()} // Prevent click from bubbling to map
        >
          <div className="relative rounded-t-xl overflow-hidden">
            <button
              className="absolute top-2 right-2 bg-white rounded-full p-1 z-20"
              onClick={handleCloseClick}
            >
              <IoClose className="h-4 w-4" />
            </button>

            {property.pics && property.pics.length > 0 ? (
              <Slider {...sliderSettings} >
                {property.pics.map((pic, index) => (
                  <div key={index} className="outline-none w-full" onClick={handlePropertyClick}>
                    <img
                      src={pic || "https://via.placeholder.com/300x200"}
                      className="w-full h-[180px] object-cover rounded-t-xl"
                      alt={`Property ${index + 1}`}
                      loading="lazy"
                    />
                  </div>
                ))}
              </Slider>
            ) : (
              <img
                src="https://via.placeholder.com/300x200"
                className="w-full h-[180px] object-cover"
                alt="Property"
              />
            )}
          </div>

          <div className="p-3">
            <div className="flex items-center justify-between flex-wrap gap-2">
              <div>
                <h3 className="font-semibold text-sm">
                  {truncateText(property.title)}
                </h3>
                <p className="text-xs text-gray-600">
                  {truncateText(property.description)}
                </p>
                <p className="text-xs text-gray-600">
                  {property.spaceTypeDetail?.bedrooms || 1}{" "}
                  {property.spaceTypeDetail?.bedrooms === 1 ? "bed" : "beds"}
                </p>
              </div>
              <div className="flex items-center">
                <span className="text-xs">
                  ★ {property.averageRating || "Not Review Yet"}
                </span>
                <span className="text-xs text-gray-500 ml-1">
                  ({property.reviews?.length || "0"})
                </span>
              </div>
            </div>

            <p className="text-sm font-semibold mt-2">
              {price} points per night
            </p>
            <p className="text-sm font-semibold">
              {totalPrice} points for 7 nights
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

// Extracted to its own component
const SearchCardSkeleton = React.memo(() => (
  <div className="mb-4 p-2">
    <div className="border rounded-lg overflow-hidden shadow-sm bg-gray-50 flex flex-row">
      <div className="w-1/3">
        <Skeleton height={160} className="h-full" />
      </div>

      <div className="w-2/3 p-4 flex flex-col justify-between">
        <div className="flex items-center mb-1">
          <Skeleton width={100} height={20} />
          <Skeleton width={30} height={20} className="ml-2" />
        </div>

        <div>
          <Skeleton width={120} height={24} className="mb-1" />
        </div>

        <div>
          <Skeleton width={180} height={20} className="mb-1" />
        </div>

        <div>
          <Skeleton width={200} height={20} className="mb-2" />
        </div>

        <div className="flex justify-between items-center mt-2">
          <Skeleton width={60} height={20} />
          <Skeleton width={120} height={24} />
        </div>
      </div>
    </div>
  </div>
));

const SearchListings = () => {
  const { search, state } = useLocation();
  const [mapCenter, setMapCenter] = useState({ lat: null, lng: null });
  const [googleMapsLoaded, setGoogleMapsLoaded] = useState(false);
  const [allProperties, setAllProperties] = useState([]); // Store all fetched properties
  const [properties, setProperties] = useState([]); // Store filtered and paginated properties
  const [searchInput, setSearchInput] = useState("");
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState("");
  const [selectedPrice, setSelectedPrice] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [propertiesPerPage] = useState(5);
  const [selectedProperty, setSelectedProperty] = useState(null);
  const mapRef = useRef(null);
  
  // Parse URL parameters
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const queryParams = useMemo(() => ({
    city: params.get("city"),
    state: params.get("state"),
    StateAbbr: params.get("StateAbbr"),
    country: params.get("country"),
    CountryAbbr: params.get("CountryAbbr"),
    checkIn: params.get("check_in"),
    checkOut: params.get("check_out"),
    guest: params.get("spaceTypeDetail"),
    room: params.get("bedroomData"),
  }), [params]);

  // Parse check-in/check-out dates
  const parsedDates = useMemo(() => ({
    checkIn: queryParams.checkIn ? dayjs(queryParams.checkIn) : null,
    checkOut: queryParams.checkOut ? dayjs(queryParams.checkOut) : null
  }), [queryParams.checkIn, queryParams.checkOut]);

  // Load Google Maps API
  useEffect(() => {
    if (window.google) {
      setGoogleMapsLoaded(true);
      return;
    }

    const loadGoogleMapsAPI = () => {
      const script = document.createElement("script");
      script.src = `https://maps.googleapis.com/maps/api/js?key=${config.mapkey}&libraries=places&callback=googleMapsCallback`;
      script.async = true;
      script.defer = true;

      window.googleMapsCallback = () => {
        setGoogleMapsLoaded(true);
      };

      document.head.appendChild(script);
    };

    loadGoogleMapsAPI();

    return () => {
      window.googleMapsCallback = null;
    };
  }, []);

  // Set map center coordinates from address data
  useEffect(() => {
    if (state?.fullAddress) {
      const latitude = parseFloat(state.fullAddress.lat);
      const longitude = parseFloat(state.fullAddress.long);

      if (!isNaN(latitude) && !isNaN(longitude)) {
        setMapCenter({ lat: latitude, lng: longitude });
      } else {
        console.error("Invalid lat/long values", state.fullAddress);
        setMapCenter({ lat: 0, lng: 0 });
      }
    }
  }, [state]);

  // Fetch properties when primary location filters change
  useEffect(() => {
    const { city, state: queryState, country } = queryParams; // Renamed state to queryState to avoid conflict
    if (city || queryState || country) {
      fetchProperties();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams.city, queryParams.state, queryParams.country, queryParams.StateAbbr, queryParams.CountryAbbr, state?.fullAddress?.address]); // Removed currentPage dependency

  // Apply secondary filters and update properties state whenever allProperties or filter criteria change
  useEffect(() => {
    let filteredData = [...allProperties]; // Start with all fetched properties

    // Apply additional filters from state
    if (state?.spaceTypeDetail) {
      filteredData = filteredData.filter(
        item => item.spaceTypeDetail.guests >= state.spaceTypeDetail
      );
    }
    
    if (state?.bedroomData) {
      filteredData = filteredData.filter(
        item => item.spaceTypeDetail.bedrooms >= state.bedroomData
      );
    }
    
    if (state?.checkIn && state?.checkOut) {
      const checkInDate = new Date(state.checkIn);
      const checkOutDate = new Date(state.checkOut);
      
      filteredData = filteredData.filter(item => {
        const bookedDates = item.bookedDates || [];
        return !bookedDates.some(bookedDate => {
          const bookedDateObj = new Date(bookedDate);
          // Check for date range overlap
          return bookedDateObj >= checkInDate && bookedDateObj < checkOutDate; // Adjusted logic slightly if needed
        });
      });
    }

    setProperties(filteredData); // Update the state with filtered properties
    setCurrentPage(1); // Reset to page 1 when filters change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allProperties, state?.spaceTypeDetail, state?.bedroomData, state?.checkIn, state?.checkOut]);


  // Handler functions
  const handleSearchInputChange = useCallback((value) => {
    setSearchInput(value);
  }, []);

  const handleFilter = useCallback((type) => {
    if (type === "bedroom" || type === "guest") {
      setFilterType(type);
    }
  }, []);

  const handleFilterPrice = useCallback((val) => {
    setSelectedPrice(val);
  }, []);

  const handlePageChange = useCallback((event, newPage) => {
    setCurrentPage(newPage);
  }, []);

  // Fetch properties data
  const fetchProperties = useCallback(async () => {
    setLoading(true);
    try {
      // Build query params
      let queryString = "";
      if (queryParams.city) queryString += `city=${queryParams.city}`;
      if (queryParams.state) queryString += `&state=${queryParams.state}`;
      if (queryParams.country) queryString += `&country=${queryParams.country}`;
      if (queryParams.StateAbbr) queryString += `&StateAbbr=${queryParams.StateAbbr}`;
      if (queryParams.CountryAbbr) queryString += `&CountryAbbr=${queryParams.CountryAbbr}`;

      // REMOVED PAGINATION PARAMS (skip, limit) from API call
      // const skip = (currentPage - 1) * propertiesPerPage;
      // queryString += `&skip=${skip}&limit=${propertiesPerPage}`;

      // Add address
      if (state?.fullAddress?.address) {
        queryString += `&address=${encodeURIComponent(state.fullAddress.address)}`;
      }

      // Fetch data
      const res = await userServices.commonGetService(`/property/getAllProperty?${queryString}`);

      // Store all fetched data, filtering will happen in a separate useEffect
      setAllProperties(res?.data?.data || []);

    } catch (error) {
      ErrorService.handleError(error);
      setAllProperties([]); // Clear on error
    } finally {
      setLoading(false);
    }
  }, [currentPage, propertiesPerPage, queryParams, state]);

  // Computed values for pagination
  const totalPages = useMemo(() =>
    Math.ceil(properties.length / propertiesPerPage),
    [properties, propertiesPerPage]
  );

  const displayedProperties = useMemo(() => {
    const startIndex = (currentPage - 1) * propertiesPerPage;
    const endIndex = startIndex + propertiesPerPage;
    return properties.slice(startIndex, endIndex);
  }, [properties, currentPage, propertiesPerPage]);

  useEffect(() => {
    if (displayedProperties.length > 0) {
      const latitudes = displayedProperties.map(p => parseFloat(p.loc?.coordinates?.[1])).filter(Boolean);
      const longitudes = displayedProperties.map(p => parseFloat(p.loc?.coordinates?.[0])).filter(Boolean);
  
      if (latitudes.length && longitudes.length) {
        const avgLat = latitudes.reduce((a, b) => a + b, 0) / latitudes.length;
        const avgLng = longitudes.reduce((a, b) => a + b, 0) / longitudes.length;
  
        setMapCenter({ lat: avgLat, lng: avgLng });
      }
    }
  }, [displayedProperties]);
  

  // For skeleton loading
  const renderSkeletons = useCallback(() => {
    return Array(4)
      .fill(0)
      .map((_, index) => <SearchCardSkeleton key={`skeleton-${index}`} />);
  }, []);

  return (
    <div className="md:mt-20 mt-10">
      <Grid item sm={12} xs={12} style={{ border: 'none' }}>
        <div className="px-4 pt-3 pb-7 bg-cover bg-center bg-[url('assets/img/headerBg.png')]">
          <LandingSearchBar
            notPedding="notapply"
            checkindate={parsedDates.checkIn}
            checkoutdate={parsedDates.checkOut}
            initialGuest={queryParams.guest}
            initialRoom={queryParams.room}
            onSearchInputChange={handleSearchInputChange}
            handleFilter={handleFilter}
            filterType={filterType}
            filterPrice={handleFilterPrice}
            setSelectedPrice={setSelectedPrice}
          />
        </div>
      </Grid>
      
      <Grid container spacing={2}>
        {/* Property listings section */}
        <Grid item lg={6} md={6} sm={12} xs={12}>
          <div className="px-2 lg:px-0 w-full md:ml-10 xl:ml-24 h-full overflow-y-auto">
            {loading ? (
              <div>
                <p className="px-2 py-4 font-bold text-md">
                  <Skeleton width={120} />
                </p>
                {renderSkeletons()}
              </div>
            ) : displayedProperties.length === 0 ? (
              <p className="flex items-center justify-center my-4 text-lg font-semibold">
                No data matches
              </p>
            ) : (
              <div>
                <p className="px-2 py-4 font-bold text-md">
                  {properties.length} Homes Available
                </p>
                {displayedProperties.map((item) => (
                  <SearchCard
                    key={item._id}
                    data={item}
                    isHighlighted={selectedProperty?._id === item._id}
                  />
                ))}
              </div>
            )}
            
            {/* Pagination */}
            {properties.length > 0 && (
              <div className="flex justify-center mt-8 mb-12">
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  variant="outlined"
                  sx={{
                    "& .Mui-selected": {
                      backgroundColor: "black !important",
                      color: "white !important"
                    },
                    "& .Mui-selected:hover": {
                      backgroundColor: "#333 !important",
                      color: "white !important"
                    }
                  }}
                />
              </div>
            )}
          </div>
        </Grid>

        {/* Map section */}
        <Grid item lg={6} md={6} sm={12} xs={12}>
          <div 
            className="w-full md:h-screen h-[50vh] md:px-20 px-10" 
            ref={mapRef}
          >
            {googleMapsLoaded && mapCenter.lat && mapCenter.lng ? (
              <GoogleMapReact
                bootstrapURLKeys={{
                  key: config.mapkey,
                  libraries: ["places"],
                }}
                defaultZoom={11}
                center={mapCenter}
                onClick={() => {/* Let outside click detector handle this */}}
              >
                {displayedProperties.length > 0 &&
                  displayedProperties.map((item) => (
                    <PropertyMarker
                      key={item._id}
                      lat={item.loc.coordinates[1]}
                      lng={item.loc.coordinates[0]}
                      property={item}
                      onSelect={setSelectedProperty}
                      isOpen={selectedProperty?._id === item._id}
                      mapRef={mapRef}
                    />
                  ))}
              </GoogleMapReact>
            ) : (
              <div className="flex items-center justify-center h-full">
                {loading ? (
                  <Skeleton width="100%" height="100%" />
                ) : (
                  <p className="text-lg font-semibold">Loading map...</p>
                )}
              </div>
            )}
          </div>
        </Grid>
      </Grid>
    </div>
  );
};

export default SearchListings;
