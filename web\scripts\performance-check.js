#!/usr/bin/env node

/**
 * Performance check script for BnbYond
 * Runs various performance checks and reports results
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 BnbYond Performance Check Starting...\n');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Performance budgets
const budgets = {
  maxBundleSize: 1024 * 1024, // 1MB
  maxCSSSize: 200 * 1024,     // 200KB
  maxImageSize: 500 * 1024,   // 500KB per image
  maxChunkSize: 500 * 1024,   // 500KB per chunk
};

/**
 * Check build output sizes
 */
function checkBuildSizes() {
  log('📦 Checking build sizes...', 'blue');
  
  const buildPath = path.join(__dirname, '../build');
  
  if (!fs.existsSync(buildPath)) {
    log('❌ Build directory not found. Run "npm run build" first.', 'red');
    return false;
  }

  const staticPath = path.join(buildPath, 'static');
  let totalSize = 0;
  let violations = [];

  // Check JavaScript files
  const jsPath = path.join(staticPath, 'js');
  if (fs.existsSync(jsPath)) {
    const jsFiles = fs.readdirSync(jsPath).filter(file => file.endsWith('.js'));
    let totalJSSize = 0;

    jsFiles.forEach(file => {
      const filePath = path.join(jsPath, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      totalJSSize += size;
      totalSize += size;

      if (size > budgets.maxChunkSize) {
        violations.push({
          file: `js/${file}`,
          size: formatBytes(size),
          budget: formatBytes(budgets.maxChunkSize),
          type: 'chunk'
        });
      }
    });

    log(`  JavaScript: ${formatBytes(totalJSSize)} (${jsFiles.length} files)`, 
        totalJSSize > budgets.maxBundleSize ? 'red' : 'green');
  }

  // Check CSS files
  const cssPath = path.join(staticPath, 'css');
  if (fs.existsSync(cssPath)) {
    const cssFiles = fs.readdirSync(cssPath).filter(file => file.endsWith('.css'));
    let totalCSSSize = 0;

    cssFiles.forEach(file => {
      const filePath = path.join(cssPath, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      totalCSSSize += size;
      totalSize += size;
    });

    log(`  CSS: ${formatBytes(totalCSSSize)} (${cssFiles.length} files)`, 
        totalCSSSize > budgets.maxCSSSize ? 'red' : 'green');
  }

  // Check media files
  const mediaPath = path.join(staticPath, 'media');
  if (fs.existsSync(mediaPath)) {
    const mediaFiles = fs.readdirSync(mediaPath);
    let totalMediaSize = 0;

    mediaFiles.forEach(file => {
      const filePath = path.join(mediaPath, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      totalMediaSize += size;
      totalSize += size;

      if (size > budgets.maxImageSize) {
        violations.push({
          file: `media/${file}`,
          size: formatBytes(size),
          budget: formatBytes(budgets.maxImageSize),
          type: 'image'
        });
      }
    });

    log(`  Media: ${formatBytes(totalMediaSize)} (${mediaFiles.length} files)`, 'blue');
  }

  log(`  Total: ${formatBytes(totalSize)}`, totalSize > budgets.maxBundleSize * 2 ? 'red' : 'green');

  // Report violations
  if (violations.length > 0) {
    log('\n⚠️  Budget Violations:', 'yellow');
    violations.forEach(violation => {
      log(`  ${violation.file}: ${violation.size} (budget: ${violation.budget})`, 'red');
    });
  } else {
    log('✅ All size budgets met!', 'green');
  }

  return violations.length === 0;
}

/**
 * Check for performance optimizations
 */
function checkOptimizations() {
  log('\n🔍 Checking performance optimizations...', 'blue');
  
  const checks = [
    {
      name: 'Service Worker',
      check: () => fs.existsSync(path.join(__dirname, '../public/sw.js')),
      message: 'Service worker for caching and offline support'
    },
    {
      name: 'Lazy Loading Components',
      check: () => {
        const routesFile = path.join(__dirname, '../src/routes/index.js');
        const content = fs.readFileSync(routesFile, 'utf8');
        return content.includes('React.lazy');
      },
      message: 'Components are lazy loaded'
    },
    {
      name: 'Optimized Images',
      check: () => {
        const lazyImageFile = path.join(__dirname, '../src/components/LazyImage/LazyImage.jsx');
        return fs.existsSync(lazyImageFile);
      },
      message: 'Lazy image loading implemented'
    },
    {
      name: 'API Optimization',
      check: () => {
        const apiFile = path.join(__dirname, '../src/services/optimizedApiService.js');
        return fs.existsSync(apiFile);
      },
      message: 'Optimized API service with caching'
    },
    {
      name: 'Performance Monitoring',
      check: () => {
        const perfFile = path.join(__dirname, '../src/components/PerformanceMonitor/PerformanceMonitor.jsx');
        return fs.existsSync(perfFile);
      },
      message: 'Performance monitoring implemented'
    }
  ];

  let passedChecks = 0;
  checks.forEach(check => {
    const passed = check.check();
    log(`  ${passed ? '✅' : '❌'} ${check.name}: ${check.message}`, passed ? 'green' : 'red');
    if (passed) passedChecks++;
  });

  const score = Math.round((passedChecks / checks.length) * 100);
  log(`\nOptimization Score: ${score}%`, score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red');

  return score >= 80;
}

/**
 * Check dependencies for performance issues
 */
function checkDependencies() {
  log('\n📋 Checking dependencies...', 'blue');
  
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  // Heavy dependencies to watch out for
  const heavyDeps = [
    'moment', 'lodash', 'jquery', 'bootstrap', 'material-ui'
  ];
  
  const foundHeavyDeps = [];
  heavyDeps.forEach(dep => {
    if (dependencies[dep]) {
      foundHeavyDeps.push(dep);
    }
  });

  if (foundHeavyDeps.length > 0) {
    log(`  ⚠️  Heavy dependencies found: ${foundHeavyDeps.join(', ')}`, 'yellow');
    log('  Consider lazy loading or replacing with lighter alternatives', 'yellow');
  } else {
    log('  ✅ No heavy dependencies detected', 'green');
  }

  // Check for duplicate dependencies
  const depNames = Object.keys(dependencies);
  const duplicates = depNames.filter(name => 
    depNames.some(other => other !== name && other.includes(name))
  );

  if (duplicates.length > 0) {
    log(`  ⚠️  Potential duplicate dependencies: ${duplicates.join(', ')}`, 'yellow');
  }

  return foundHeavyDeps.length === 0;
}

/**
 * Generate performance report
 */
function generateReport(results) {
  log('\n📊 Performance Report', 'blue');
  log('='.repeat(50), 'blue');
  
  const overallScore = results.filter(Boolean).length / results.length * 100;
  
  log(`Overall Performance Score: ${Math.round(overallScore)}%`, 
      overallScore >= 80 ? 'green' : overallScore >= 60 ? 'yellow' : 'red');
  
  log('\nRecommendations:', 'blue');
  
  if (!results[0]) {
    log('  • Optimize bundle sizes - consider code splitting', 'yellow');
    log('  • Remove unused dependencies', 'yellow');
  }
  
  if (!results[1]) {
    log('  • Implement missing performance optimizations', 'yellow');
    log('  • Add lazy loading for components and images', 'yellow');
  }
  
  if (!results[2]) {
    log('  • Review and optimize heavy dependencies', 'yellow');
    log('  • Consider lighter alternatives', 'yellow');
  }
  
  if (overallScore >= 80) {
    log('\n🎉 Great job! Your app is well optimized for performance.', 'green');
  } else if (overallScore >= 60) {
    log('\n👍 Good progress! A few more optimizations will make it even better.', 'yellow');
  } else {
    log('\n🚨 Performance needs attention. Please address the issues above.', 'red');
  }
}

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Run all checks
async function runPerformanceCheck() {
  try {
    const results = [
      checkBuildSizes(),
      checkOptimizations(),
      checkDependencies()
    ];
    
    generateReport(results);
    
    const overallScore = results.filter(Boolean).length / results.length * 100;
    process.exit(overallScore >= 60 ? 0 : 1);
    
  } catch (error) {
    log(`\n❌ Performance check failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the performance check
runPerformanceCheck();
