import React from 'react';
import { useNavigate } from 'react-router-dom';
import Container from '@mui/material/Container';

function Unauthorized() {
  
  let navigate = useNavigate();

  return (
    <Container maxWidth='md'>
      <div className='center-styl mt-20'>
        <div className='w-full m-auto py-4  flex items-center justify-center'>
          <div className='bg-white shadow overflow-hidden sm:rounded-lg pb-8'>
            <div className='border-t border-gray-200 text-center pt-2'>
              <h1 className='error-main-text font-bold text-color-primary'>
                403
              </h1>
              <h1 className='text-5xl font-medium py-4'>
                oops! You don't have permission to access this page
              </h1>
              <p className='text-xl pb-8 px-12 font-medium'>
                Please login with valid credentials to access this page.
              </p>
              <div className='mx-20'>
                <button onClick={() => navigate('/auth/signin')} className='btn-styl'>
                  LOGIN
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
}

export default Unauthorized;
