import mongoose from "mongoose";

const OfferStateSchema = new mongoose.Schema({
     checkIn: { type: String },
     checkOut: { type: String },
     count: { type: Number },
     fees: { type: Number },
     points: { type: Number },
     serviceFee: { type: Number },
     total: { type: Number },
  });

  const TripeSchema = new mongoose.Schema({
  message: { type: String },
  phoneNumber: { type: String },
  showPhoneField: { type: Boolean },
  showTextarea: { type: Boolean },

  });

const ReservationSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'Users' },
  property_id  : { type: mongoose.Schema.Types.ObjectId, ref: 'property' },
  paymentType: { type: String },
  offerState:  OfferStateSchema,
  tripe:TripeSchema,
  status: {
    type:String,
    default:'pending'
  },
  timeStamp: {
    type: Date,
    default: Date.now,
  },

  isRefundRequest:{type:Boolean,default:false},
  isRefundStatus:{ type:String,
    default:'pending'},
  paymentIntentId: { type: String },
  refundReason: { type: String },
  refundRecord: {
    refundedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Users' }, 
    pointsToGuest: { type: Number, default: 0 }, 
    pointsToHost: { type: Number, default: 0 },  
    refundProcessedAt: { type: Date }, 
  },
  serviceFeeRefunded: { type: Number, default: 0 }, 

});

export const Reservation = mongoose.model("reservation", ReservationSchema);