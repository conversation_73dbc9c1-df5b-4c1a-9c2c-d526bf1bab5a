import React, { useRef, useEffect } from "react";
import { RiArrowDropDownFill } from "react-icons/ri";

const LanguageDropdown = ({
  setDropdownlang,
  dropdownlang,
  selectlang,
  setSelectlang,
}) => {
  const dropdownlangRef = useRef(null);


  const handleClickOutsidelang = (event) => {
    if (
      dropdownlangRef.current &&
      !dropdownlangRef.current.contains(event.target)
    ) {
      setDropdownlang(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutsidelang);
    return () => {
      document.removeEventListener("mousedown", handleClickOutsidelang);
    };
  }, []);
  
  return (
    <>
      <h2 className="my-2">Choose Language</h2>
      <div
        ref={dropdownlangRef}
        className="relative  border border-slate-300 mb-2 p-2 rounded-md w-[280px] "
      >
        <div
          onClick={() => setDropdownlang(!dropdownlang)}
          className=" flex justify-between items-center cursor-pointer"
        >
          <p className="text-color-darkgrey/50">
            {" "}
            {selectlang ? selectlang : "Choose Language"}
          </p>

          <span>
            <RiArrowDropDownFill className="text-2xl" />
          </span>
        </div>
        {dropdownlang && (
          <div className="absolute z-50  top-[100%] w-full bg-white left-0 border border-color-grey">
            <label
              onClick={() => setSelectlang("english")}
              className="flex items-center gap-2  p-2 border-b border-b-color-grey hover:bg-color-yellow/20"
              for="english-radio"
            >
              <input type="radio" name="user-type" id="english-radio" />
              <p className="text-color-darkgrey/80"> English</p>
            </label>
            <label
              onClick={() => setSelectlang("urdu")}
              className="flex items-center gap-2  p-2 border-b border-b-color-grey hover:bg-color-yellow/20"
              for="urdu-radio"
            >
              <input type="radio" name="user-type" id="urdu-radio" />
              <p className="text-color-darkgrey/80"> urdu</p>
            </label>
            <label
              onClick={() => setSelectlang("pashto")}
              className="flex items-center gap-2 p-2 hover:bg-color-yellow/20"
              for="pashto-radio"
            >
              <input type="radio" name="user-type" id="pashto-radio" />
              <p className="text-color-darkgrey/80"> Pashto</p>
            </label>
          </div>
        )}
      </div>
    </>
  );
};

export default LanguageDropdown;
