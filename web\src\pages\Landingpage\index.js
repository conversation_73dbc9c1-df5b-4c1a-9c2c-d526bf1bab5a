import React, { useState, useEffect, useRef, lazy } from "react";
import Container from "@mui/material/Container";
import { CircularProgress, Divider } from "@mui/material";
import { useMutation } from "react-query";
import { toast } from "react-toastify";
import { localStorageData } from "services/auth/localStorageData";
import userService from "services/httpService/userAuth/userServices";
import PropertyCardSkeleton from "common/skeletons/PropertyCardSkeleton";
import { useLazyPagination } from "hooks/useLazyLoad";
import optimizedApiService from "services/optimizedApiService";

// Lazy load heavy components
const Slider = lazy(() => import("react-slick"));
const LandingHeader = lazy(() => import("components/Headers/LandingHeader"));
const HomeCard = lazy(() => import("components/Cards/HomeCard"));
const Navbar = lazy(() => import("components/Navbars/AuthNavbar"));
const Footerbn = lazy(() => import("pages/Footer/Footerbn"));
const OurBlogs = lazy(() => import("components/Blogs/OurBlogs"));
const PopupCards = lazy(() => import("../welcomemessage/PopupCards"));

// Lazy load CSS for slick carousel
const loadSlickCSS = () => {
  if (!document.querySelector('link[href*="slick.css"]')) {
    const link1 = document.createElement('link');
    link1.rel = 'stylesheet';
    link1.href = 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css';
    document.head.appendChild(link1);

    const link2 = document.createElement('link');
    link2.rel = 'stylesheet';
    link2.href = 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css';
    document.head.appendChild(link2);
  }
};

// Critical images are now preloaded by the performance optimizer
export default function LandingPage() {
  const videoRef = useRef();


  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);
  const [favoriteRender, setFavoriteRender] = useState(false);
  const [email, setEmail] = useState("");

  // Lazy load images with proper paths
  const images = [
    "/assets/img/image1.png",
    "/assets/img/image2.png",
    "/assets/img/image3.png",
    "/assets/img/image4.png",
    "/assets/img/image5.png"
  ];
  const names = ["Chicago", "New York", "San Diego", "Seattle", "Washington, DC"];

  // Optimized property loading with caching
  const loadProperties = async (page = 1, pageSize = 8) => {
    try {
      const res = await optimizedApiService.getProperties({
        page,
        limit: pageSize,
        status: 'Active',
        cache: true,
        cacheTTL: 10 * 60 * 1000 // 10 minutes
      });

      if (res.data && Array.isArray(res.data.data)) {
        const activeProperties = res.data.data.filter(
          (property) => property.status === "Active"
        );
        return {
          data: activeProperties,
          hasMore: activeProperties.length === pageSize,
          total: res.data.total || activeProperties.length
        };
      }
      return { data: [], hasMore: false, total: 0 };
    } catch (error) {
      console.error("Fetch error:", error);
      throw error;
    }
  };

  // Use lazy pagination for properties
  const {
    data: allPost,
    isLoading,
    hasMore,
    triggerRef
  } = useLazyPagination(loadProperties, {
    pageSize: 8,
    threshold: 0.1,
    rootMargin: '100px'
  });

  // Show more cards function (manual trigger for load more)
  const showMoreCards = () => {
    // This will be handled by the lazy pagination hook automatically
    // when the trigger element comes into view
  };

  // Properties are now loaded automatically by useLazyPagination

  useEffect(() => {
    const welcomeMessageKey = 'welcomeMessageShown';
    const hasWelcomeMessageShown = localStorage.getItem(welcomeMessageKey);

    if (!hasWelcomeMessageShown) {
      setShowWelcomeMessage(true);
      localStorage.setItem(welcomeMessageKey, 'true');
    }

    // Load slick CSS when needed
    loadSlickCSS();
  }, []);

  // Infinite scroll effect (now handled by useLazyPagination)
  // The triggerRef from useLazyPagination handles infinite scroll automatically

  const { mutate, isEmailLoading } = useMutation(
    (email) => userService.subscribeNewsletter(email),
    {
      onError: (error) => {
        toast.error(`Error: ${error.message}`);
      },
      onSuccess: () => {
        toast.success("Successfully subscribed!");
        setEmail("");
      },
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email) return toast.error("Email is required");
    if (!emailRegex.test(email)) return toast.error("Please enter a valid email address");

    mutate(email);
  };

  const settings = {
    autoplay: true,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    responsive: [
      { breakpoint: 1024, settings: { slidesToShow: 3 } },
      { breakpoint: 768, settings: { slidesToShow: 2 } },
      { breakpoint: 640, settings: { slidesToShow: 1 } },
    ],
  };

  // Video visibility observer removed for performance optimization

  // Video width animation removed for performance optimization

  const handleCloseWelcomeMessage = () => {
    setShowWelcomeMessage(false);
  };

  return (
    <>
      <Navbar />
      <main style={{ overflowX: "hidden" }}>
        <LandingHeader />

        <section className="relative block py-10 lg:pt-0 overflow-visible">
          <div
            ref={videoRef}
            className="w-full flex justify-center items-center -mt-[60px] z-30"
          >
            <video
              src="https://d36i7rvokxtewk.cloudfront.net/BnByondWelcome21%3B9111224.mp4"
              autoPlay
              muted
              loop
              playsInline
              preload="auto"
              className="w-full max-w-[100%] h-auto max-h-[500px] sm:max-h-[400px] md:max-h-[450px] lg:max-h-[500px] rounded-lg object-contain transition-all duration-2000 ease-in-out"
            >
              Your browser does not support the video tag.
            </video>
          </div>
        </section>

        <section className="block py-10 lg:pt-0 lg:mx-12 md:mx-14 sm:mx-0 xs:mx-0">
          <Container maxWidth="lg">
            <div style={{ marginTop: "-30px" }}>
              <Slider {...settings}>
                {images.map((imageUrl, index) => (
                  <div key={index}>
                    <div
                      className="md:w-[290px] w-full h-[369px] group bg-center bg-cover relative overflow-hidden"
                      style={{
                        backgroundImage: `url(${imageUrl})`,
                      }}
                    >
                      <div className="overlay absolute inset-0  bg-[#58C0D0] opacity-0 transition-opacity group-hover:opacity-90"></div>
                      <span className="text-[#FFFFFF] group-hover:text-xl text-base p-3 absolute bottom-0 z-30">
                        {names[index]}
                      </span>
                    </div>
                  </div>
                ))}
              </Slider>
            </div>
          </Container>
        </section>
         {showWelcomeMessage && (
          <PopupCards onClose={handleCloseWelcomeMessage} />
        )}

        <section className="block py-10 lg:pt-0 lg:mx-12 md:mx-14 sm:mx-0 xs:mx-0">
        <Container maxWidth="lg">
          <div className="text-center mb-6">
            <h4 className="text-[#000000] text-3xl font-medium">Explore Homes Nearby</h4>
          </div>
         
            <div>
              

              {isLoading ? (
                <div className="flex flex-wrap w-full gap-5 sm:justify-center">
                  <PropertyCardSkeleton cards={8} />
                </div>
              ) : allPost.length > 0 ? (
                <div>
                  <div className="flex flex-wrap w-full gap-5 sm:justify-center">
                    {allPost.map((item) => {
                      const favourite =
                        item?.favouriteList &&
                        item.favouriteList.find(
                          (user) => user.userId === localStorageData("_id")
                        );

                      return (
                        <div
                          key={item._id}
                          className="mx-1 my-1 xs:w-full sm:w-[265px] md:w-[265px] w-full"
                        >
                          <HomeCard
                            data={item}
                            favourite={favourite}
                            favoriteRender={favoriteRender}
                            setFavoriteRender={setFavoriteRender}
                          />
                        </div>
                      );
                    })}
                  </div>
                  {/* Infinite scroll trigger */}
                  {hasMore && (
                    <div
                      ref={triggerRef}
                      className="mt-6 text-center"
                    >
                      {isLoading ? (
                        <div className="flex justify-center">
                          <CircularProgress size={24} />
                        </div>
                      ) : (
                        <button
                          onClick={showMoreCards}
                          className="text-base font-medium border border-[#E8AD21] rounded-full w-full md:w-[150px] h-[50px] text-[#E8AD21] bg-[#FFFFFF]"
                        >
                          See more
                        </button>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                // Fallback UI for zero properties
                <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-xl border border-gray-200">
                  <div className="text-[#58C0D0] text-5xl mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-16 w-16"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-medium text-gray-800 mb-2">
                    No properties available
                  </h3>
                  <p className="text-gray-500 text-center max-w-md">
                    We're currently adding new properties to our network. Check
                    back soon for exciting new stay options!
                  </p>
                </div>
              )}
            </div>
          
        </Container>
      </section>

        <section className="">
          <section className="bg-[#58C0D0]">
            <section className="block py-10 mt-24 ">
              <Container maxWidth="lg">
                <div className="flex w-full gap-4 xs:flex-wrap sm:justify-center xs:justify-center md:justify-between sm:flex-wrap lg:flex-row">
                  <div className="bg-white w-full md:w-[300px] lg:w-[265px] z-10 py-10 rounded-xl">
                    <div className="flex flex-col items-center justify-center">
                      <div className="w-[60px] h-[60px]">
                        <img src={require("assets/img/freeValue.png")} />
                      </div>
                      <div className="text-center">
                        <h4 className="text-[#000000] pt-3 text-md font-normal">
                          Far More Value
                        </h4>
                        <p className="text-[#AFB5C1] pt-3 text-base font-normal w-auto mx-3">
                          Get the absolute most from your vacation rental
                          property
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white w-full md:w-[300px] lg:w-[265px] z-10 py-10 rounded-xl">
                    <div className="flex flex-col items-center justify-center">
                      <div className="w-[60px] h-[60px]">
                        <img src={require("assets/img/accomodation.png")} />
                      </div>
                      <div className="text-center">
                        <h4 className="text-[#000000] pt-3 text-md font-normal">
                          Enjoy Endless Accommodations
                        </h4>
                        <p className="text-[#AFB5C1] pt-3 text-base font-normal w-auto mx-3">
                          Enjoy accommodations everywhere without paying a dime
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white w-full md:w-[300px] lg:w-[265px] z-10 py-10 rounded-xl">
                    <div className="flex flex-col items-center justify-center">
                      <div className="w-[60px] h-[60px]">
                        <img src={require("assets/img/community-icon.png")} />
                      </div>

                      <div className="text-center">
                        <h4 className="text-[#000000] pt-3 text-md font-normal">
                          Amazing Community
                        </h4>
                        <p className="text-[#AFB5C1] pt-3 text-base font-normal w-auto mx-3">
                          Amazing community of vacation rental
                          owners/like-minded travellers
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white w-full md:w-[300px] lg:w-[265px] z-10 py-10 rounded-xl">
                    <div className="flex flex-col items-center justify-center">
                      <div className="w-[60px] h-[60px]">
                        <img src={require("assets/img/guests.png")} />
                      </div>
                      <div className="text-center">
                        <h4 className="text-[#000000] pt-3 text-md font-normal">
                          Quality Guests
                        </h4>
                        <p className="text-[#AFB5C1] pt-3 text-base font-normal w-auto mx-3">
                          The most responsible guests are other vacation rental
                          owners
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="w-full  relative max-w-[1200px] ml-auto mr-auto px-6 md:px-[120px] mt-10 z-40 hidden md:flex flex-wrap justify-between">
                  <div
                    className="w-[20px] rounded-full border border-[#FFFFFF] bg-[#58C0D0] h-[20px]"
                    style={{ zIndex: "40" }}
                  />
                  <div
                    className="w-[20px] rounded-full bg-[#C1E1C2] h-[20px]"
                    style={{ zIndex: "40" }}
                  />
                  <div
                    className="w-[20px] rounded-full bg-[#E8AD21] h-[20px]"
                    style={{ zIndex: "40" }}
                  />
                  <div
                    className="w-[20px] rounded-full bg-[#C0392D] h-[20px]"
                    style={{ zIndex: "40" }}
                  />
                  <Divider className="w-full absolute top-2.5 left-0 bg-[#EDEDED] max-w-[1140px] ml-auto mr-auto mt-[-10px] z-0" />
                </div>
              </Container>
            </section>
          </section>
        </section>

        <section className="block py-10 lg:pt-0 lg:mx-12 sm:mx-0 xs:mx-0">
          <Container maxWidth="lg">
            <div className="w-full mt-8">
              <div className="text-center">
                <h4 className="text-[#000000] text-3xl font-medium">
                  Trending Destinations
                </h4>
              </div>
              <div className="md:flex w-full h-full md:h-[600px] mt-5 ">
                <div className="relative w-full">
                  <p className="absolute bottom-0 z-40 text-white left-2 text-[30px]">
                    Key West
                  </p>

                  <img
                    className="h-[100%] w-full rounded-l-md object-cover"
                    src={require("assets/img/pic01.jpg")}
                  />
                </div>
                <div className="w-full md:flex md:flex-col">
                  <div className="w-full md:flex ">
                    <div className="relative w-full group">
                      <p className="absolute bottom-0 z-40 text-white left-2 text-[30px]">
                        Orlando
                      </p>
                      <img
                        className="h-[300px] w-full object-cover"
                        src={require("assets/img/pic02.jpg")}
                      />
                      <div className="absolute inset-0 bg-[#58C0D0] opacity-0 group-hover:opacity-90 transition-opacity"></div>
                    </div>
                    <div className="relative w-full group">
                      <p className="absolute bottom-0 z-40 text-white left-2 text-[30px]">
                        San Francisco
                      </p>

                      <img
                        className="h-[300px] rounded-tr-md w-full object-cover"
                        src={require("assets/img/pic04.jpg")}
                      />
                      <div className="absolute inset-0 bg-[#58C0D0] opacity-0 group-hover:opacity-90 transition-opacity"></div>
                    </div>
                  </div>
                  <div className="md:flex ">
                    <div className="relative w-full group">
                      <p className="absolute bottom-0 z-40 text-white left-2 text-[30px]">
                        Las Vegas
                      </p>

                      <img
                        className="h-[300px] w-full object-cover"
                        src={require("assets/img/pic03.jpg")}
                      />
                      <div className="absolute inset-0 bg-[#58C0D0] opacity-0 group-hover:opacity-90 transition-opacity"></div>
                    </div>
                    <div className="relative w-full group">
                      <p className="absolute bottom-0 z-40 text-white left-2 text-[30px]">
                        New York
                      </p>

                      <img
                        className="h-[300px] w-full rounded-br-md object-cover"
                        src={require("assets/img/pic05.jpg")}
                      />
                      <div className="absolute inset-0 bg-[#58C0D0] opacity-0 group-hover:opacity-90 transition-opacity"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </section>
        <section>
          <OurBlogs />
        </section>
      </main>
      <div className="flex justify-center  w-full">
        <section className=" flex relative z-30 md:top-20  w-[94%]  lg:mb-10 ">
          <div className="">
            <img
              src={require("assets/img/icon.png")}
              className="absolute left-[-20px] lg:w-[170.64px] lg:h-[261.17px] lg:block hidden opacity-[21%] z-[-10]  "
            />
          </div>
          <Container
            sx={{
              width: "100%", // xl:w-full
              maxWidth: "100%", // xl:w-full
              "@media (min-width: 1024px)": {
                width: "90%", // lg:w-[90%]
                maxWidth: "90%", // lg:w-[90%]
              },
            }}
            style={{ paddingLeft: "6px", paddingRight: "6px" }}
          >
            <div
              className="bg-[#AF3227]/95 md:h-[300px] h-full lg:w-[95%] xl:w-[96%] mx-auto rounded-2xl "
              style={{
                backgroundImage:
                  "url(" + require("assets/img/badgebg.png") + ")",
                backgroundRepeat: "no-repeat",
              }}
            >
              <Container>
                <div className="flex flex-col md:flex-row items-center justify-between w-full md:h-[300px] h-auto">
                  {/* Left Section (Text & Input) */}
                  <div className="lg:pl-10 text-center md:text-left">
                    <div className="md:w-[445px] w-full flex items-center justify-center md:justify-start">
                      <p className="text-white text-[25px] md:text-[36px] font-bold pt-3 md:pt-0">
                        Get travel tips, offers, and inspiration from BnByond
                      </p>
                    </div>
                    {/* Email Input */}
                    <div className="relative pt-4 flex flex-col md:flex-row md:items-center">
                      <input
                        type="email"
                        placeholder="Email address"
                        className="w-full md:w-[400px] lg:w-[495px] bg-transparent pl-5 border text-white placeholder-white border-white h-[50px] rounded-2xl"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                      />
                      <button
                        className="text-[#C0392D] md:absolute md:right-2 mt-3 md:mt-0 text-base font-medium bg-white rounded-2xl md:w-[150px] w-full h-[50px]"
                        onClick={handleSubmit}
                        disabled={isEmailLoading}
                      >
                        {isEmailLoading ? "Submitting..." : "Subscribe"}
                      </button>
                    </div>
                  </div>

                  {/* Down Arrow Image */}
                  <div className="hidden md:block">
                    <img src={require("assets/img/downrrow.png")} alt="Arrow" />
                  </div>

                  {/* Right Section (Rotated Image) */}
                  <div className="relative mt-6 md:mt-0">
                    <div className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl h-auto border-4 border-white rounded-xl transform rotate-6 overflow-hidden">
                      <img
                        src={require("assets/img/squadNew.jpeg")}
                        className="w-full h-auto rounded-lg object-cover"
                        alt="Squad"
                      />
                    </div>
                  </div>
                </div>
              </Container>
            </div>
          </Container>
          <div className="">
            <img
              src={require("assets/img/icon.png")}
              className="absolute right-[-20px] lg:w-[170.64px] lg:h-[261.17px] lg:block hidden opacity-[21%] transform scale-x-[-1] z-[-10]   "
            />
          </div>
        </section>
      </div>
      <Footerbn />
    </>
  );
}
