/**
 * Bundle optimization utilities for code splitting and lazy loading
 */

/**
 * Dynamic import with error handling and retry logic
 */
export const dynamicImport = (importFunction, retries = 3, delay = 1000) => {
  return new Promise((resolve, reject) => {
    const attemptImport = (attempt) => {
      importFunction()
        .then(resolve)
        .catch((error) => {
          if (attempt < retries) {
            console.warn(`Import failed, retrying... (${attempt}/${retries})`);
            setTimeout(() => attemptImport(attempt + 1), delay);
          } else {
            console.error('Import failed after all retries:', error);
            reject(error);
          }
        });
    };
    
    attemptImport(1);
  });
};

/**
 * Preload components based on user interaction patterns
 */
export const preloadComponent = (importFunction, trigger = 'hover') => {
  let isPreloaded = false;
  
  const preload = () => {
    if (!isPreloaded) {
      isPreloaded = true;
      importFunction().catch(err => 
        console.warn('Preload failed:', err)
      );
    }
  };

  return {
    preload,
    component: React.lazy(() => dynamicImport(importFunction))
  };
};

/**
 * Route-based code splitting helper
 */
export const createRouteComponent = (importFunction, fallback = null) => {
  const Component = React.lazy(() => dynamicImport(importFunction));
  
  return (props) => (
    <React.Suspense fallback={fallback || <div>Loading...</div>}>
      <Component {...props} />
    </React.Suspense>
  );
};

/**
 * Feature-based code splitting
 */
export const createFeatureBundle = (features) => {
  const loadedFeatures = new Map();
  
  return {
    loadFeature: async (featureName) => {
      if (loadedFeatures.has(featureName)) {
        return loadedFeatures.get(featureName);
      }
      
      const feature = features[featureName];
      if (!feature) {
        throw new Error(`Feature ${featureName} not found`);
      }
      
      try {
        const module = await dynamicImport(feature.import);
        loadedFeatures.set(featureName, module);
        return module;
      } catch (error) {
        console.error(`Failed to load feature ${featureName}:`, error);
        throw error;
      }
    },
    
    isFeatureLoaded: (featureName) => loadedFeatures.has(featureName),
    
    preloadFeatures: (featureNames) => {
      featureNames.forEach(name => {
        if (!loadedFeatures.has(name) && features[name]) {
          dynamicImport(features[name].import)
            .then(module => loadedFeatures.set(name, module))
            .catch(err => console.warn(`Failed to preload ${name}:`, err));
        }
      });
    }
  };
};

/**
 * Vendor library optimization
 */
export const optimizeVendorLibraries = () => {
  // Lazy load heavy libraries
  const libraries = {
    moment: () => import('moment'),
    chart: () => import('chart.js'),
    fullCalendar: () => import('@fullcalendar/core'),
    tinymce: () => import('@tinymce/tinymce-react'),
    stripe: () => import('@stripe/stripe-js'),
    apexcharts: () => import('apexcharts'),
    slick: () => import('react-slick')
  };

  const loadedLibraries = new Map();

  return {
    loadLibrary: async (name) => {
      if (loadedLibraries.has(name)) {
        return loadedLibraries.get(name);
      }

      if (!libraries[name]) {
        throw new Error(`Library ${name} not configured for lazy loading`);
      }

      try {
        const lib = await libraries[name]();
        loadedLibraries.set(name, lib);
        return lib;
      } catch (error) {
        console.error(`Failed to load library ${name}:`, error);
        throw error;
      }
    },

    preloadLibraries: (names) => {
      names.forEach(name => {
        if (libraries[name] && !loadedLibraries.has(name)) {
          libraries[name]()
            .then(lib => loadedLibraries.set(name, lib))
            .catch(err => console.warn(`Failed to preload ${name}:`, err));
        }
      });
    }
  };
};

/**
 * Bundle size analyzer
 */
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV !== 'development') return;

  const getResourceSizes = () => {
    const resources = performance.getEntriesByType('resource');
    const sizes = {
      total: 0,
      js: 0,
      css: 0,
      images: 0,
      fonts: 0,
      other: 0
    };

    resources.forEach(resource => {
      const size = resource.transferSize || 0;
      sizes.total += size;

      const url = resource.name;
      if (url.includes('.js')) {
        sizes.js += size;
      } else if (url.includes('.css')) {
        sizes.css += size;
      } else if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
        sizes.images += size;
      } else if (url.match(/\.(woff|woff2|ttf|otf)$/i)) {
        sizes.fonts += size;
      } else {
        sizes.other += size;
      }
    });

    return sizes;
  };

  const formatSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const logBundleAnalysis = () => {
    const sizes = getResourceSizes();
    
    console.group('📦 Bundle Size Analysis');
    console.log(`Total: ${formatSize(sizes.total)}`);
    console.log(`JavaScript: ${formatSize(sizes.js)}`);
    console.log(`CSS: ${formatSize(sizes.css)}`);
    console.log(`Images: ${formatSize(sizes.images)}`);
    console.log(`Fonts: ${formatSize(sizes.fonts)}`);
    console.log(`Other: ${formatSize(sizes.other)}`);
    console.groupEnd();

    // Check against budgets
    const budgets = {
      total: 2 * 1024 * 1024, // 2MB
      js: 1 * 1024 * 1024,    // 1MB
      css: 200 * 1024,        // 200KB
      images: 500 * 1024,     // 500KB
      fonts: 100 * 1024       // 100KB
    };

    const violations = [];
    Object.entries(budgets).forEach(([type, budget]) => {
      if (sizes[type] > budget) {
        violations.push({
          type,
          size: sizes[type],
          budget,
          exceeded: sizes[type] - budget
        });
      }
    });

    if (violations.length > 0) {
      console.group('⚠️ Bundle Budget Violations');
      violations.forEach(violation => {
        console.warn(
          `${violation.type}: ${formatSize(violation.size)} ` +
          `(budget: ${formatSize(violation.budget)}, ` +
          `exceeded by: ${formatSize(violation.exceeded)})`
        );
      });
      console.groupEnd();
    }

    return { sizes, violations };
  };

  // Run analysis after page load
  if (document.readyState === 'complete') {
    setTimeout(logBundleAnalysis, 1000);
  } else {
    window.addEventListener('load', () => {
      setTimeout(logBundleAnalysis, 1000);
    });
  }

  return { getResourceSizes, formatSize, logBundleAnalysis };
};

/**
 * Tree shaking helper for unused exports
 */
export const createTreeShakableModule = (exports) => {
  // This helps bundlers identify unused exports
  const usedExports = new Set();
  
  const trackUsage = (exportName) => {
    usedExports.add(exportName);
    if (process.env.NODE_ENV === 'development') {
      console.log(`Using export: ${exportName}`);
    }
  };

  const getUsageReport = () => {
    const allExports = Object.keys(exports);
    const unusedExports = allExports.filter(name => !usedExports.has(name));
    
    return {
      total: allExports.length,
      used: usedExports.size,
      unused: unusedExports,
      usageRate: (usedExports.size / allExports.length) * 100
    };
  };

  // Wrap exports with usage tracking
  const wrappedExports = {};
  Object.entries(exports).forEach(([name, value]) => {
    Object.defineProperty(wrappedExports, name, {
      get() {
        trackUsage(name);
        return value;
      },
      enumerable: true
    });
  });

  wrappedExports.__getUsageReport = getUsageReport;
  
  return wrappedExports;
};

export default {
  dynamicImport,
  preloadComponent,
  createRouteComponent,
  createFeatureBundle,
  optimizeVendorLibraries,
  analyzeBundleSize,
  createTreeShakableModule
};
