import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

let mongoServer;

export const connectTestDB = async () => {
  try {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri);
    console.log('Connected to in-memory test database');
    
    mongoose.connection.on('error', (err) => {
      console.error('Test MongoDB connection error:', err);
    });
  } catch (error) {
    console.error('Error connecting to test MongoDB:', error);
    throw error;
  }
};

export const disconnectTestDB = async () => {
  try {
    await mongoose.disconnect();
    if (mongoServer) {
      await mongoServer.stop();
    }
    console.log('Disconnected from in-memory test database');
  } catch (error) {
    console.error('Error disconnecting from test MongoDB:', error);
    throw error;
  }
};

export const clearDatabase = async () => {
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('Attempted to clear non-test database!');
  }
  
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
};