import React, { useState, useEffect, useRef } from 'react';

function GuestAndRoomSelector({ guests, rooms, handleGuestChange, handleRoomChange, isGuestIncrementDisabled, isRoomIncrementDisabled }) {
    const [isOpen, setIsOpen] = useState(false);
    const [localGuests, setLocalGuests] = useState(Number(guests) || 1);
    const [localRooms, setLocalRooms] = useState(Number(rooms) || 1);
    const dropdownRef = useRef(null);

    useEffect(() => {
        setLocalGuests(Number(guests) || 1);
        setLocalRooms(Number(rooms) || 1);
    }, [guests, rooms]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('click', handleClickOutside);
        return () => document.removeEventListener('click', handleClickOutside);
    }, []);

    const handleGuestIncrement = () => {
        const newValue = Number(localGuests) + 1;
        setLocalGuests(newValue);
        handleGuestChange('increment');
    };

    const handleGuestDecrement = () => {
        if (Number(localGuests) > 1) {
            const newValue = Number(localGuests) - 1;
            setLocalGuests(newValue);
            handleGuestChange('decrement');
        }
    };

    const handleRoomIncrement = () => {
        const newValue = Number(localRooms) + 1;
        setLocalRooms(newValue);
        handleRoomChange('increment');
    };

    const handleRoomDecrement = () => {
        if (Number(localRooms) > 1) {
            const newValue = Number(localRooms) - 1;
            setLocalRooms(newValue);
            handleRoomChange('decrement');
        }
    };

    return (
        <div className="relative w-full" ref={dropdownRef}>
            <div className="flex items-center justify-center">
                <button
                    className="flex items-center justify-between w-full p-2 border border-gray-400 rounded-md focus:outline-none focus:border-blue-500"
                    onClick={() => setIsOpen(!isOpen)}
                >
                    <span>{localGuests} guests & {localRooms} rooms</span>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className={`h-6 w-6 ${isOpen ? 'transform rotate-180' : ''}`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                        />
                    </svg>
                </button>
            </div>

            {isOpen && (
                <div className="absolute z-10 top-full left-0 right-0 xl:right-auto bg-white border border-gray-400 rounded-md shadow-md">
                    <div className="flex flex-col p-2">
                        <div className="flex items-center gap-1 justify-between">
                            <div>
                                <span className="ml-2 font-bold text-lg">Guests</span>
                            </div>
                            <div className='flex gap-2'>
                                <button
                                    className={`px-3.5 py-1.5 rounded-full ${Number(localGuests) <= 1 ? 'bg-gray-300' : 'bg-blue-500 text-white'}`}
                                    onClick={handleGuestDecrement}
                                    disabled={Number(localGuests) <= 1}
                                >
                                    -
                                </button>
                                <input
                                    value={localGuests}
                                    readOnly
                                    className="w-16 text-center border border-gray-400 rounded-full"
                                />
                                <button
                                    className={`px-3 py-1 rounded-full ${
                                        isGuestIncrementDisabled
                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            : 'bg-blue-500 text-white'
                                    }`}
                                    onClick={handleGuestIncrement}
                                    disabled={isGuestIncrementDisabled}
                                >
                                    +
                                </button>
                            </div>
                        </div>
                        <div className="flex items-center gap-1 justify-between mt-2">
                            <div>
                                <span className="ml-2 font-bold text-lg">Rooms</span>
                            </div>
                            <div className='flex gap-2'>
                                <button
                                    className={`px-3.5 py-1.5 rounded-full ${
                                        Number(localRooms) <= 1
                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            : 'bg-blue-500 text-white'
                                    }`}
                                    onClick={handleRoomDecrement}
                                    disabled={Number(localRooms) <= 1}
                                >
                                    -
                                </button>
                                <input
                                    value={localRooms}
                                    readOnly
                                    className="w-16 text-center border rounded-full border-gray-400"
                                />
                                <button
                                    className={`px-3 py-1 rounded-full ${
                                        isRoomIncrementDisabled
                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            : 'bg-blue-500 text-white'
                                    }`}
                                    onClick={handleRoomIncrement}
                                    disabled={isRoomIncrementDisabled}
                                >
                                    +
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default GuestAndRoomSelector;
