import React, { useState } from 'react'
import { Container } from "@mui/material";
import PropertyListSteps from './PropertyListSteps';

const GuestProperty = () => {
  const [active, setActive] = useState(2);
  const [ActiveProperties, setActiveProperties] = useState([]);
  const [PendingProperties, setPendingProperties] = useState([]);
  const [RejectedProperties, setRejectedProperties] = useState([]);
  const handleActive = (num) => {
    setActive(num);
  };
  return (
    <>
      <section>
   <div className='md:py-8 py-4 md:px-12 px-5'>
            <div className="my-4">
              <h1 className="text-xl font-medium ml-4">My Properties</h1>
            </div>
            <div className=" md:p-4 p-1">
              <div className="flex items-center flex-wrap border-b border-b-color-grey relative ">
                <p
                  onClick={() => handleActive(1)}
                  className={`md:px-4 px-2 md:py-2 py-1 cursor-pointer text-color-grey border-b-2 mr-2 relative -bottom-[0.5px] ${active == 1
                      ? "border-b-color-yellow text-color-black"
                      : "border-b-transparent"
                    } `}
                >
                  Pending List
                </p>
                <p
                  onClick={() => handleActive(2)}
                  className={`md:px-4 px-2 md:py-2 py-1 cursor-pointer text-color-grey border-b-2 md:mx-2 relative -bottom-[0.5px] ${active == 2
                      ? "border-b-color-yellow text-color-black"
                      : "border-b-transparent"
                    } `}
                >
                  Active List
                </p>
                <p
                  onClick={() => handleActive(3)}
                  className={`md:px-4 px-2 md:py-2 py-1 cursor-pointer text-color-grey border-b-2 md:mx-2 relative -bottom-[0.5px] ${active == 3
                      ? " border-b-color-yellow text-color-black"
                      : "border-b-transparent"
                    }`}
                >
                  Rejected List
                </p>
              </div>
              <PropertyListSteps
                active={active}
                ActiveProperties={ActiveProperties}
                PendingProperties={PendingProperties}
                RejectedProperties={RejectedProperties}
              />
            </div>
            </div>
      
      </section>
    </>
  )
}

export default GuestProperty