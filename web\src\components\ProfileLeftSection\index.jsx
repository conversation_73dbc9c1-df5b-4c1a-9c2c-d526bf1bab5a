import React, { useEffect, useState, useCallback } from "react";
import { ButtonOutlined } from "common/buttons/buttonOutlined";
import checkedImg from "assets/img/checkedImg.png";
import locationImg from "assets/img/locationimg.png";
import { localStorageData, storeLocalData } from "services/auth/localStorageData";
import { MdOutlineCancel } from "react-icons/md";
import { useMutation } from "react-query";
import userServices from "services/httpService/userAuth/userServices";
import { toast } from "react-toastify";
import ErrorService from "services/formatError/ErrorService";
import { useNavigate } from "react-router-dom";
import PopupModal from "components/PopupModal/PopupModal";
import { loadStripe } from "@stripe/stripe-js";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import config from "config.js";

const stripePromise = loadStripe(config.stripe_publishkey);

export const ProfileLeftSection = () => {
  const userId = localStorageData("_id");
  const navigate = useNavigate();

  const [showModal, setShowModal] = useState(false);
  const [stripeCode, setStripeCode] = useState("");
  const [isLoadingUser, setIsLoadingUser] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [userData, setUserData] = useState({});
  const [disableBtn, setDisableBtn] = useState(false);
  const [showMembershipPopup, setShowMembershipPopup] = useState(false);

  const fetchUserData = useCallback(async () => {
    try {
      setIsLoadingUser(true);
      const res = await userServices.userDatagetById(`userAuth/userdatabyId/${userId}`);
      const data = res?.data?.data;
      setUserData(data);
      storeLocalData(data);
    } catch (error) {
      console.error("Failed to load user data:", error);
    } finally {
      setIsLoadingUser(false);
    }
  }, [userId]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get("code");
    const state = urlParams.get("state");
    if (code && state) {
      setStripeCode(code);
      setShowModal(true);
    }
  }, []);

  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  const handleGoogleVerification = () => {
    googleVerifyMutate();
    setIsModalOpen(false)
  };

  const handleSubscription = async () => {
    setDisableBtn(true);
    subscriptionMutate({
      email: localStorageData("email"),
      userId,
    });
  };

  const handleVerifyModal = () => {
    if (userData?.verify === "no") setIsModalOpen(true);
  };

  const handleMembershipClick = () => {
    setShowMembershipPopup(true);
  };

  const handleMembershipAccept = () => {
    setShowMembershipPopup(false);
  };

  const { mutate: googleVerifyMutate } = useMutation(
    () => userServices.googleVerification(`/userAuth/gmailVerification/${userId}`),
    {
      onSuccess: () => {
        toast.success("Verification email has been sent!");
        setUserData((prev) => ({ ...prev, verify: "pending" }));
      },
      onError: (error) => toast.error(ErrorService.uniformError(error)),
    }
  );

  const { mutate: subscriptionMutate } = useMutation(
    (token) => userServices.stripeauthorize(`stripe/create-checkout-session`, token),
    {
      onSuccess: async (data) => {
        const sessionId = data?.data?.sessionId;
        if (sessionId) {
          const stripe = await stripePromise;
          const result = await stripe.redirectToCheckout({ sessionId });
          if (result.error) {
            toast.error(`Subscription failed Please try again.`);
          }
        } else {
          toast.error("Failed to initiate checkout session.");
        }
        setDisableBtn(false);
      },
      onError: (error) => {
        toast.error(ErrorService.uniformError(error));
        setDisableBtn(false);
      },
    }
  );

  const navigateTo = (path) => navigate(path);

  const getRole = () => {
    const role = localStorageData("loginAs");
    if (role?.includes("admin")) return "Admin";
    if (role?.includes("Host")) return "Host";
    if (role?.includes("Guest")) return "Guest";
    return "Influencer";
  };

  const isVerified = localStorageData("verify") !== "no";

  return (
    <div className="mx-auto bg-white rounded-xl border border-[#C1E1C2] p-6 md:w-[360px] w-full">
      <div className="flex justify-center">
        <img
          className="object-cover w-40 h-40 rounded-full"
          src={localStorageData("pic") || require("assets/img/profile.png")}
          alt="profile"
        />
      </div>

      <div className="pb-10 mt-4 space-y-8 text-center">
        <div>
          <p className="text-4xl font-medium capitalize">
            {localStorageData("fname")} {localStorageData("lname")}
          </p>
          <p className="text-[#929293] text-[16px] font-normal capitalize">{getRole()}</p>
          <div className="flex items-center justify-center mt-2">
            <img src={locationImg} alt="location" className="mr-2" />
            <p className="text-[#3D3D3D] text-[14px] font-medium">{localStorageData("address")}</p>
          </div>
        </div>

        <hr className="border-t border-[#C1E1C2]" />

        <div className="space-y-2">
          <p className="text-xl font-semibold capitalize">
            {localStorageData("fname")} confirmed information
          </p>
          <div className="flex justify-center items-center gap-2">
            {isVerified ? (
              <img src={checkedImg} alt="verified" className="w-6 h-6" />
            ) : (
              <MdOutlineCancel className="text-[#A0DBE4] text-lg" />
            )}
            <p className="text-[#929293] text-[16px] font-normal">{localStorageData("email")}</p>
          </div>
        </div>

        <hr className="border-t border-[#C1E1C2]" />

        <div>
          <p className="text-xl font-semibold">Verify your identity</p>
          {!isVerified && (
            <p className="text-[#929293] text-[16px] font-normal">
              Before you book or host on BnByond, complete this step.
            </p>
          )}
          <button className="pt-5 w-full" onClick={handleVerifyModal}>
            {!isVerified ? (
              <p className="w-full py-2 rounded-full border border-yellow-500 text-yellow-500 text-lg font-semibold hover:bg-yellow-500 hover:text-white">
                Verify Account
              </p>
            ) : (
              <div className="flex items-center justify-center border border-green-500 py-2 rounded-full">
                <span className="text-green-500 text-lg font-semibold">Verified</span>
                <img src={checkedImg} alt="checked" className="ml-2 w-5 h-5" />
              </div>
            )}
          </button>
        </div>

        <PopupModal
          icon={
            <div className="bg-green-200 h-12 w-12 rounded-full flex items-center justify-center">
              <ExclamationTriangleIcon className="h-6 w-6 text-green-600" />
            </div>
          }
          handleCancle={() => setIsModalOpen(false)}
          handleAccept={handleGoogleVerification}
          openAlert={isModalOpen}
          setOpenAlert={setIsModalOpen}
          description="Do you want to verify your account?"
          title="Verify Your Account"
          acceptbtnText={
            <div className="w-full py-2 text-sm font-bold text-center">Verify</div>
          }
        />

        <div className="mt-5">
          <ButtonOutlined onClick={() => navigateTo("/account")} text="Advanced Settings" />
        </div>

        <div className="mt-5 w-full">
          {isLoadingUser ? (
            <button
              disabled
              className="w-full py-2 rounded-full bg-yellow-500 text-white text-lg font-semibold cursor-not-allowed"
            >
              Loading...
            </button>
          ) : userData?.subscription ? (
            <button
              onClick={() => navigateTo("/manage-subscription")}
              className="w-full py-2 rounded-full border border-green-500 text-green-500 text-lg font-semibold hover:bg-green-500 hover:text-white"
            >
              Manage Subscription
            </button>
          ) : (
            <button
              onClick={handleMembershipClick}
              disabled={disableBtn}
              className="w-full py-2 rounded-full border border-yellow-500 text-yellow-500 text-lg font-semibold hover:bg-yellow-500 hover:text-white"
            >
              Membership Plan
            </button>
          )}
        </div>
        <PopupModal
          icon={
            <div className="bg-yellow-200 h-12 w-12 rounded-full flex items-center justify-center">
              <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
            </div>
          }
          handleCancle={() => setShowMembershipPopup(false)}
          handleAccept={handleMembershipAccept}
          openAlert={showMembershipPopup}
          setOpenAlert={setShowMembershipPopup}
          description={"Hi! You don't have to worry about adding your credit card right now. You can provide it once you've made or accepted your first booking on BnByond."}
          title="No Credit Card Needed Yet!"
          acceptbtnText={<div className="w-full py-2 text-sm font-bold text-center">OK</div>}
        />
      </div>
    </div>
  );
};
