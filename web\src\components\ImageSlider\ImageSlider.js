import React from 'react'
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { IoIosArrowBack, IoIosArrowForward } from 'react-icons/io';

const CustomPrevArrow = (props) => {
  const { className, onClick } = props;
  return (
    <div
      className="border border-white w-[40px] absolute top-[40%] left-3 h-[40px] z-10 cursor-pointer rounded-full flex justify-center items-center"
      onClick={onClick}
    >
      <IoIosArrowBack style={{ color: "white" }} />
    </div>
  );
};

const CustomNextArrow = (props) => {
  const { className, onClick } = props;
  return (
    <div
      className="border border-white w-[40px] h-[40px]  absolute top-[40%] right-3 z-10 cursor-pointer rounded-full flex justify-center items-center"
      onClick={onClick}
    >
      <IoIosArrowForward style={{ color: "white" }} />
    </div>
  );
};

const ImageSlider = ({ allPost }) => {
  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    prevArrow: <CustomPrevArrow />,
    nextArrow: <CustomNextArrow />,
  };
  return (
    <Slider {...settings}>
      {
        allPost.pics.map((item, index) => {
          return (
            <img
              key={index}
              src={item}
              className="object-cover w-full h-[500px]"
              alt={`Slide ${index + 1}`}
            />
          )
        })
      }
    </Slider>
  )
}

export default ImageSlider;