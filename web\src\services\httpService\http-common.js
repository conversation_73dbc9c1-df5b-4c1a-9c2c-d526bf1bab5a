import axios from "axios";
import { config } from "config.js";

const Api = axios.create({
  baseURL: config.endPoint,
  headers: {
    "Content-type": "application/json",
  },
});

Api.interceptors.request.use(
  (request) => {
    let localData = localStorage.getItem(config.localToken);
    if (localData) {
      localData = JSON.parse(localData);
      // localData from simple login and localData.data from admin <PERSON>gin
      const token = localData?.token || localData?.data?.token;
      if (token) {
        request.headers.Authorization = `Bearer ${token}`;
      }
    }
    return request;
  },
  (error) => Promise.reject(error)
);


Api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      if (error.response.status === 403) {
        localStorage.removeItem(config.localToken);
        window.location.href = "/auth/signin";
      }
    }
    return Promise.reject(error);
  }
);

export default Api;
