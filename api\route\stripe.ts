import { Router } from "express";
import checkRoleMiddleware from "../middleware/routeMiddleware";
import { Controller } from "../controllers/stripe";

const router = Router();

// Common middleware for stripe routes
const stripeAccessControl = checkRoleMiddleware(['subadmin', 'Blogger', 'Financial']);

// Card management routes
router.post("/AddCardInfo", stripeAccessControl, Controller.AddCardInfo);
router.post("/removeCard", stripeAccessControl, Controller.RemoveCard);

// Payment and transaction routes
router.get("/transactions", stripeAccessControl, Controller.allTransections);
router.post("/refundpayment", stripeAccessControl, Controller.refundPayment);
router.get("/trackpayment/:id", stripeAccessControl, Controller.trackPayment);

// Subscription management routes
router.post("/subscriptiondetail", stripeAccessControl, Controller.subscriptionDetail);
router.post("/cancelsubscription/:id", stripeAccessControl, Controller.cancelSubscription);
router.post("/create-checkout-session", stripeAccessControl, Controller.userSubscription);
router.post("/success_payment", stripeAccessControl, Controller.userSubscriptionAfterSuccess);

// Seller authorization
router.post("/authorize-seller/:userId", stripeAccessControl, Controller.authorizeSeller);

export default router;
