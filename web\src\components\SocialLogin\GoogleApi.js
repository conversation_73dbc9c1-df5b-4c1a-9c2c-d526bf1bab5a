import React from 'react';
import { useGoogleLogin } from '@react-oauth/google';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

import { config } from 'config.js';
import { useCreateGoogleAccount } from 'hooks/useCreateGoogleAccount';

function GoogleApi(props) {
  const referralCode = props.referralCode;
  let navigate = useNavigate();
  const { mutateAsync: createGoogleAccount } = useCreateGoogleAccount();

  const login = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      try {
        // Fetch user info using the access token
        const userInfoResponse = await axios.get(
          'https://www.googleapis.com/oauth2/v3/userinfo',
          { headers: { Authorization: `Bearer ${tokenResponse.access_token}` } }
        );
        
        const userInfo = userInfoResponse.data;
        
        // Format data for backend in the same structure as before
        let newdata = {
          email: userInfo.email,
          fname: userInfo.given_name,
          id: userInfo.sub, // sub is the Google ID
          pic: userInfo.picture,
        };
        if (referralCode) {
          newdata.referralCode = props.referralCode;
          newdata.isReferralCode = true;
        }
        const res = await createGoogleAccount(newdata);

        if (res.status) {
          let userData = res.data;
          userData['pic'] = config.ImageEndPoint + userData.pic;
          navigate('/');
        }
      } catch (error) {
        console.error("Error creating Google account:", error);
      }
    },
    onError: (error) => {
      console.error("Google login failed:", error);
    }
  });

  return (
    <button
      className='bg-white text-gray-900 rounded-full my-2 border border-gray-200 uppercase shadow-sm font-semibold px-3 py-2 w-full items-center justify-center outline-none focus:outline-none mr-2 hover:shadow-md ease-linear transition-all duration-150'
      onClick={() => login()}
    >
      <i className='fab fa-google'></i>
      <span className='ml-4'> {props.name}</span>
    </button>
  );
}

export default GoogleApi;
