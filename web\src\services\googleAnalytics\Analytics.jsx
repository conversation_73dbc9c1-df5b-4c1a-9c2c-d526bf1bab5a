import ReactGA from "react-ga4";
import config from "config.js"

export const initGA = () => {
    ReactGA.initialize(config.google_analytics_measurement_id);
};

export const logPageView = (path) => {
    ReactGA.send({ hitType: "pageview", page: path });
};

export const logTimeOnPage = (path, duration) => {
    ReactGA.event({
        category: "Engagement",
        action: "Time Spent",
        label: path,
        value: Math.round(duration / 1000),
    });
};
