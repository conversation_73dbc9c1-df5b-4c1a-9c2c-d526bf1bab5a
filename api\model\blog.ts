import mongoose from "mongoose";

const commentSchema = new mongoose.Schema({
  email: { type: String },
  website: { type: String },
  message: { type: String },
  name: { type: String },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "Users" },
  status: { type: Number, default: 0 },
  pic: { type: String },
  timeStamp: { type: Date, default: Date.now },
  parentCommentId: { type: mongoose.Schema.Types.ObjectId, default: null }, 
  editHistory: [
    {
      previousMessage: { type: String },
      editedAt: { type: Date, default: Date.now },
    },
  ],
});

const blogSchema = new mongoose.Schema({
  title: { type: String },
  description: { type: Object },
  comments: [commentSchema],
  likes: { type: [mongoose.Schema.Types.ObjectId], ref: "User", default: [] },
  dislikes: { type: [mongoose.Schema.Types.ObjectId], ref: "User", default: [] },
  timeStamp: { type: Date, default: Date.now },
  pic: { type: String },
});

export const Blog = mongoose.model("Blog", blogSchema);
