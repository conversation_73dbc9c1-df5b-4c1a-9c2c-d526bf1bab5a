import { jest } from '@jest/globals';

type MailjetResponse = {
  body: {
    Messages: Array<{ Status: string, [key: string]: any }>;
    [key: string]: any;
  };
};

const mailjetMock = {
  connect: jest.fn().mockReturnValue({
    post: jest.fn().mockReturnValue({
      request: jest.fn<() => Promise<MailjetResponse>>().mockResolvedValue({
        body: { Messages: [{ Status: 'success' }] }
      })
    })
  })
};

export default mailjetMock;