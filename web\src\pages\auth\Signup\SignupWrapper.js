import { useCreateEmailAccount } from "hooks";
import React, { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import SignupForm from "pages/auth/Signup/index.js"
import { useLocation, useNavigate } from 'react-router-dom';

const SignupWrapper = ({ isInfluencer }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Hook to handle API call for creating an email account
  const { mutateAsync: createEmailAccount, isLoading: apiLoading } = useCreateEmailAccount();

  let navigate = useNavigate();
  // Initial form values
  const initialValues = {
    fname: "",
    lname: "",
    email: "",
    phoneNumber: "",
    country: "",
    town: "",
    pass: "",

  };

  // Validation schema 
  const validationSchema = Yup.object({
    fname: Yup.string().required("First Name is required"),
    lname: Yup.string().required("Last Name is required"),
    email: Yup.string().email("Invalid email address").required("Email is required"),
    phoneNumber: Yup.string().required("Phone Number is required"),
    country: Yup.string().required("Country is required"),
    town: Yup.string().required("Town / City is required"),
    pass: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .matches(/[0-9]/, "Password must contain at least one numeric character")
      .matches(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character")
      .required("Password is required"),
    referralCode: isInfluencer
      ? Yup.string().required("Referral Code is required for influencers")
      : Yup.string(),
  });

  const onSubmit = async (values) => {
    setIsLoading(true);
    try {
      const { email } = values;
      const username = email.split("@")[0];
      const response = await createEmailAccount({ ...values, username });
      
      // Store user data in localStorage if registration is successful
      if (response.data) {
        storeLocalData(response.data);
      }
      
      openModal();
      navigate("/auth/signin");
    } catch (error) {
      console.error("Error submitting form", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize Formik for form handling, validation, and submission
  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit,
  });

  const handleCheckboxChange = () => setIsChecked(!isChecked);
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <SignupForm
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={formik.handleSubmit}
      isLoading={isLoading || apiLoading} // Show loading state if either form or API is loading
      referralCode={isInfluencer ? formik.values.referralCode : ""}
      isChecked={isChecked}
      handleCheckboxChange={handleCheckboxChange}
      showPassword={showPassword}
      setShowPassword={setShowPassword}
      formik={formik}
      isModalOpen={isModalOpen}
      openModal={openModal}
      closeModal={closeModal}
    />
  );
};

export default SignupWrapper;
