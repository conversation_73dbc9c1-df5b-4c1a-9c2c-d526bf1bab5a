import moment from "moment";
import React from "react";
import { useLocation } from "react-router-dom";

export default function ReservationDetail() {
  const location = useLocation();
  let reservationData;

  try {
    reservationData = location.state?.reservation;

    if (!reservationData) {
      return (
        <div className="max-w-5xl mx-auto p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Reservation Details</h2>
          <p className="text-gray-500">No reservation data found.</p>
        </div>
      );
    }

    return (
      <div className="max-w-5xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 border-b pb-4">
          <h2 className="text-3xl font-bold text-gray-800">Reservation Details</h2>
          <span className="text-sm text-gray-400 font-medium">Reservation ID: #{reservationData.id}</span>
        </div>

        {/* Reservation Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Guest Info */}
          <div className="bg-white p-5 rounded-2xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Guest Information</h3>
            <div className="flex items-center gap-4">
              <img
                src={reservationData.pic}
                alt="guest"
                className="w-16 h-16 rounded-full object-cover ring-2 ring-gray-200"
              />
              <div>
                <p className="text-base font-medium text-gray-900">{reservationData.guestName}</p>
                <p className="text-sm text-gray-500">{reservationData.email}</p>
                <p className="text-sm text-gray-500">{reservationData.phone}</p>
              </div>
            </div>
          </div>

          {/* Reservation Info */}
          <div className="bg-white p-5 rounded-2xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Reservation Info</h3>
            <ul className="text-sm text-gray-700 space-y-1">
              <li><strong>Check-in:</strong> {reservationData.checkIn}</li>
              <li><strong>Check-out:</strong> {reservationData.checkOut}</li>
              <li><strong>Guests:</strong> {reservationData.Guest}</li>
              <li><strong>Status:</strong> <span className="text-green-600 font-semibold">{reservationData.status}</span></li>
              <li><strong>Booked On:</strong> {moment(reservationData.createdAt).format("DD-MM-YYYY")}</li>
            </ul>
          </div>

          {/* Property Info */}
          <div className="bg-white p-5 rounded-2xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Property</h3>
            <img
              src={reservationData.image[0]}
              alt="property"
              className="w-full h-36 rounded-xl object-cover mb-3"
            />
            <p className="text-base font-medium text-gray-900">{reservationData.property}</p>
            <p className="text-sm text-gray-500">{reservationData.address}</p>
          </div>
        </div>

        {/* Price and Actions */}
        <div className="mt-10 bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <p className="text-gray-500 text-sm">Total Price</p>
              <p className="text-3xl font-extrabold text-blue-700">{reservationData.points} Points</p>
            </div>
            {/* <div className="flex flex-wrap gap-3">
              <button className="bg-blue-600 text-white px-5 py-2 rounded-lg hover:bg-blue-700 transition-all font-medium">
                Message Guest
              </button>
              <button className="bg-red-100 text-red-600 px-5 py-2 rounded-lg hover:bg-red-200 transition-all font-medium">
                Cancel Reservation
              </button>
            </div> */}
          </div>
          {/* Support Message */}
          <p className="mt-4 text-sm text-gray-400">
            Cancellations can be made by contacting us at <a href="mailto:<EMAIL>" className="text-blue-600 underline"><EMAIL></a>
          </p>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error in ReservationDetail:", error);
    return (
      <div className="max-w-5xl mx-auto p-6">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">Reservation Details</h2>
        <p className="text-red-500">Something went wrong. Please try again.</p>
      </div>
    );
  }
}
