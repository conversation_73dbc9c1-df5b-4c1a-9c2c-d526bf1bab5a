import mongoose from "mongoose";

const EmployeeSchema = new mongoose.Schema({
  emplname: {
    type: String,
    required: true,
  },
  jobTitle: {
    type: String,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  phone: {
    type: String,
    required: true,
  },
  gender: {
    type: String,
    enum: ["Male", "Female", "Other"],
    required: true,
  },
  role: {
    type: [String],
    enum: ["Financial", "Blogger", "subadmin"], // You can add "Employee" as a default role
    default: ["Employee"], // Set a default role if no value is provided
  },
  
  password: {
    type: String,
  },
  pic: {
    type: String,
    default: "https://bnbpictures.s3.amazonaws.com/emptyProfilepic.jpeg",
  },

  registeredByAdmin: {
    type: mongoose.Schema.Types.ObjectId, // Reference to the admin user
    ref: "users",
  },
  status: {
    type: Boolean,
    default: false,
  },
  token: { type: String },

  timeStamp: {
    type: Date,
    default: Date.now,
  },
});

export const Employee = mongoose.model("Employee", EmployeeSchema);
