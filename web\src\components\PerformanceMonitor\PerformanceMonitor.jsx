import { useEffect, useRef } from 'react';
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

/**
 * Performance monitoring component that tracks Web Vitals and custom metrics
 */
const PerformanceMonitor = ({ enableLogging = false, onMetric = null }) => {
  const metricsRef = useRef({});
  const startTime = useRef(performance.now());

  useEffect(() => {
    // Track Web Vitals
    const handleMetric = (metric) => {
      metricsRef.current[metric.name] = metric.value;
      
      if (enableLogging) {
        console.log(`${metric.name}: ${metric.value}`);
      }
      
      if (onMetric) {
        onMetric(metric);
      }
      
      // Send to analytics (optional)
      if (window.gtag) {
        window.gtag('event', metric.name, {
          event_category: 'Web Vitals',
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          non_interaction: true,
        });
      }
    };

    // Initialize Web Vitals tracking
    getCLS(handleMetric);
    getFID(handleMetric);
    getFCP(handleMetric);
    getLCP(handleMetric);
    getTTFB(handleMetric);

    // Track custom metrics
    const trackCustomMetrics = () => {
      // Time to Interactive (TTI) approximation
      const tti = performance.now() - startTime.current;
      handleMetric({ name: 'TTI', value: tti });

      // Bundle size tracking
      if (navigator.connection) {
        const connection = navigator.connection;
        handleMetric({ 
          name: 'NetworkType', 
          value: connection.effectiveType || 'unknown' 
        });
        handleMetric({ 
          name: 'DownlinkSpeed', 
          value: connection.downlink || 0 
        });
      }

      // Memory usage (if available)
      if (performance.memory) {
        handleMetric({ 
          name: 'JSHeapSize', 
          value: performance.memory.usedJSHeapSize 
        });
      }

      // Resource timing
      const resources = performance.getEntriesByType('resource');
      const totalResourceSize = resources.reduce((total, resource) => {
        return total + (resource.transferSize || 0);
      }, 0);
      
      handleMetric({ 
        name: 'TotalResourceSize', 
        value: totalResourceSize 
      });

      // Count of resources by type
      const resourceTypes = resources.reduce((types, resource) => {
        const type = resource.name.split('.').pop() || 'other';
        types[type] = (types[type] || 0) + 1;
        return types;
      }, {});

      Object.entries(resourceTypes).forEach(([type, count]) => {
        handleMetric({ 
          name: `ResourceCount_${type}`, 
          value: count 
        });
      });
    };

    // Track metrics after page load
    if (document.readyState === 'complete') {
      trackCustomMetrics();
    } else {
      window.addEventListener('load', trackCustomMetrics);
    }

    // Track route changes (for SPA)
    const trackRouteChange = () => {
      const routeChangeTime = performance.now();
      handleMetric({ 
        name: 'RouteChangeTime', 
        value: routeChangeTime - startTime.current 
      });
      startTime.current = routeChangeTime;
    };

    // Listen for route changes (React Router)
    window.addEventListener('popstate', trackRouteChange);

    return () => {
      window.removeEventListener('load', trackCustomMetrics);
      window.removeEventListener('popstate', trackRouteChange);
    };
  }, [enableLogging, onMetric]);

  // Performance observer for additional metrics
  useEffect(() => {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navigationEntry = entry;
          
          // DNS lookup time
          const dnsTime = navigationEntry.domainLookupEnd - navigationEntry.domainLookupStart;
          metricsRef.current.DNSTime = dnsTime;
          
          // TCP connection time
          const tcpTime = navigationEntry.connectEnd - navigationEntry.connectStart;
          metricsRef.current.TCPTime = tcpTime;
          
          // Server response time
          const serverTime = navigationEntry.responseEnd - navigationEntry.requestStart;
          metricsRef.current.ServerTime = serverTime;
          
          // DOM processing time
          const domTime = navigationEntry.domComplete - navigationEntry.domLoading;
          metricsRef.current.DOMTime = domTime;

          if (enableLogging) {
            console.log('Navigation Timing:', {
              DNS: dnsTime,
              TCP: tcpTime,
              Server: serverTime,
              DOM: domTime
            });
          }
        }
        
        if (entry.entryType === 'paint') {
          metricsRef.current[entry.name] = entry.startTime;
          
          if (enableLogging) {
            console.log(`${entry.name}: ${entry.startTime}ms`);
          }
        }
      });
    });

    observer.observe({ entryTypes: ['navigation', 'paint'] });

    return () => observer.disconnect();
  }, [enableLogging]);

  // Expose metrics globally for debugging
  useEffect(() => {
    if (enableLogging) {
      window.performanceMetrics = metricsRef.current;
    }
  }, [enableLogging]);

  return null; // This component doesn't render anything
};

/**
 * Hook for tracking component-specific performance
 */
export const useComponentPerformance = (componentName) => {
  const renderStart = useRef(performance.now());
  const mountTime = useRef(null);

  useEffect(() => {
    mountTime.current = performance.now() - renderStart.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} mount time: ${mountTime.current}ms`);
    }
  }, [componentName]);

  const trackRender = () => {
    const renderTime = performance.now() - renderStart.current;
    renderStart.current = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} render time: ${renderTime}ms`);
    }
    
    return renderTime;
  };

  return { mountTime: mountTime.current, trackRender };
};

/**
 * Performance budget checker
 */
export const checkPerformanceBudget = (metrics) => {
  const budgets = {
    FCP: 1800, // First Contentful Paint < 1.8s
    LCP: 2500, // Largest Contentful Paint < 2.5s
    FID: 100,  // First Input Delay < 100ms
    CLS: 0.1,  // Cumulative Layout Shift < 0.1
    TTI: 3800, // Time to Interactive < 3.8s
    TotalResourceSize: 2000000, // Total resources < 2MB
  };

  const violations = [];
  
  Object.entries(budgets).forEach(([metric, budget]) => {
    if (metrics[metric] && metrics[metric] > budget) {
      violations.push({
        metric,
        value: metrics[metric],
        budget,
        exceeded: metrics[metric] - budget
      });
    }
  });

  return violations;
};

export default PerformanceMonitor;
