const PasswordReset = () => {
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f5f5f5',
      }}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          maxWidth: '500px',
          width: '100%',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '16px',
            backgroundColor: '#f5f5f5',
            borderTopLeftRadius: '8px',
            borderTopRightRadius: '8px',
          }}
        >
          <img
            src={`${process.env.PUBLIC_URL}/favicon.ico`}
            alt="Airbnb"
            style={{ width: '24px', height: '24px', marginRight: '8px' }}
          />
          <span style={{ fontWeight: 'bold' }}>BnByond</span>
        </div>
        <div style={{ padding: '24px' }}>
          <p style={{ margin: 0 }}>Hi <PERSON>,</p>
          <p style={{ margin: '16px 0' }}>
            We've received a request to reset your password.
          </p>
          <p style={{ margin: '16px 0' }}>
            If you didn't make the request, just ignore this message. Otherwise,
            you can reset your password.
          </p>
          <button
            style={{
              backgroundColor: '#ff5a5f',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '12px 24px',
              fontSize: '16px',
              cursor: 'pointer',
              margin: '12px 0',
            }}
          >
            Reset your password
          </button>
          <p style={{ margin: '16px 0' }}>
            Thanks,
            <br />
            The BnByond team
          </p>
        </div>
        <div
          style={{
            backgroundColor: '#f5f5f5',
            padding: '16px',
            borderBottomLeftRadius: '8px',
            borderBottomRightRadius: '8px',
            textAlign: 'center',
            fontSize: '14px',
            color: '#999',
          }}
        >
          <p style={{ margin: 0 }}>
            Airbnb Ireland UC, 8 Hanover Quay, Dublin 2, Ireland
          </p>
        </div>
      </div>
    </div>
  );
};

export default PasswordReset;
