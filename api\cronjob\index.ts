import dotenv from "dotenv";
dotenv.config();
import cron from "node-cron";
import { PointsMang, User, Reservation, Property, Referral } from "../model";
import * as Sentry from "@sentry/node";
import { sendEmail } from "../services/mailJetEmail";
import moment from "moment";
import { Stripe } from "stripe";
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("Missing STRIPE_SECRET_KEY in environment variables");
}
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY,);

export const subscriptionReminderJob = cron.schedule('0 2 * * *', async () => {
  console.log(`[CRON] Subscription Reminder Job Started at ${new Date().toISOString()}`);

  try {
    const users = await User.find({
      reservationCount: { $gte: 1 },
      subscription: false,
      membershipReminderCount: { $lt: 4 },
      roles: { $nin: ["Influencer"] },
    });

    const now = moment();
    console.log(`[CRON] Found ${users.length} users to process.`);

    for (const user of users) {
      if (!user.firstReservationDate) continue;

      const daysSinceFirstRes = now.diff(moment(user.firstReservationDate), 'days');
      const reminderCount = user.membershipReminderCount;

      const sendReminder = async (count: number, delayDays: number) => {
        if (daysSinceFirstRes >= delayDays && reminderCount === count) {
          console.log(`[CRON] Sending reminder ${count + 1} to user ${user._id}`);
          await sendEmail(
            user.email,
            "Upgrade Your Membership for More Benefits",
            {
              userName: user.fname || user.username,
              upgradeLink: `${process.env.frontendLink}/profile`
            },
            "subscription_Notification"
          );
          user.membershipReminderCount += 1;
          user.lastReminderSentAt = new Date();
          await user.save();
        }
      };

      await sendReminder(0, 1); // 1 day after first reservation
      await sendReminder(1, 4); // 4 days after first reservation

      if (reminderCount < 2) continue;

      if (daysSinceFirstRes < 6 || reminderCount !== 2) continue;

      const selectedCard = user.cardInfo?.find((c: any) => c.paymentMethodId);
      if (!selectedCard) {
        console.warn(`[CRON] Skipping user ${user._id} due to no payment method`);
        continue;
      }

      try {
        let stripeCustomerId = user.stripeCustomerId;

        try {
          await stripe.customers.retrieve(stripeCustomerId);
        } catch (err: any) {
          if (err.raw?.code === "resource_missing") {
            const customer = await stripe.customers.create({
              email: user.email,
              name: `${user.fname} ${user.lname}`,
              metadata: { id: user._id.toString() },
            });
            stripeCustomerId = customer.id;
            user.stripeCustomerId = stripeCustomerId;
            await user.save();

            await stripe.paymentMethods.attach(selectedCard.paymentMethodId, {
              customer: stripeCustomerId,
            });
          } else {
            throw err;
          }
        }

        await stripe.customers.update(stripeCustomerId, {
          invoice_settings: {
            default_payment_method: selectedCard.paymentMethodId,
          },
        });

        let priceId = process.env.SUBSCRIPTION_WITHOUTAFFLIATED;
        let isInfluencer = false;

        if (user.referredBy) {
          const referral = await Referral.findById(user.referredBy);
          const refUser = await User.findById(referral?.userId);
          isInfluencer = refUser?.roles.includes("Influencer") || false;
          priceId = isInfluencer
            ? process.env.SUBSCRIPTION_WITHAFFLIATED
            : process.env.SUBSCRIPTION_WITHOUTAFFLIATED;
        }

        if (!priceId) throw new Error("Subscription price ID missing");

        const subscription = await stripe.subscriptions.create({
          customer: stripeCustomerId,
          items: [{ price: priceId }],
          default_payment_method: selectedCard.paymentMethodId,
          expand: ["latest_invoice", "latest_invoice.payment_intent"],
        });

        if (["active", "trialing"].includes(subscription.status)) {
          user.subscription = true;
          user.autoCharged = true;
          user.autoChargeDate = new Date();
          user.subscriptionId = subscription.id;
          user.membershipReminderCount = 3;
          user.subRole = isInfluencer ? "Elite" : "Regular";
          user.nextBillingDate = new Date(subscription.current_period_end * 1000);
          user.lastReminderSentAt = new Date();
          await user.save();

          // ✅ Add referral points if not already given
          if (user.referredBy) {
            const referral = await Referral.findById(user.referredBy);
            if (referral && !referral.points) {
              referral.points = true;
              await referral.save();

              const refUser = await User.findById(referral.userId);
              if (refUser) {
                refUser.referrelUsers ??= [];
                if (!refUser.referrelUsers.includes(user._id)) {
                  refUser.referrelUsers.push(user._id);
                  refUser.points = (refUser.points || 0) + 50;
                  await refUser.save();
                  console.log(`[CRON] Awarded 50 points to referring user ${refUser._id}`);
                }
              }
            }
          }

          const invoiceUrl = typeof subscription.latest_invoice !== "string"
            ? subscription.latest_invoice?.hosted_invoice_url
            : null;

          const paymentMethod = await stripe.paymentMethods.retrieve(selectedCard.paymentMethodId);
          const product = await stripe.products.retrieve(subscription.items.data[0].price.product as string);
          const amount = `${(subscription.items.data[0].price.unit_amount / 100).toFixed(2)} ${subscription.currency.toUpperCase()}`;

          await sendEmail(
            user.email,
            "Subscription Activation Confirmation",
            {
              customerName: user.fname || "Valued Customer",
              plan: product.name || "Membership Plan",
              amount: `${amount} / ${subscription.items.data[0].price.recurring.interval}`,
              startDate: new Date(subscription.start_date * 1000).toDateString(),
              nextBillingDate: new Date(subscription.current_period_end * 1000).toDateString(),
              paymentMethod: `•••• •••• •••• ${paymentMethod.card.last4 || "****"}`,
              receiptNumber: subscription.id,
              hostedInvoiceUrl: invoiceUrl || "N/A"
            },
            "activate_subscription"
          );

          console.log(`[CRON] Subscription successful for user ${user._id}`);
        } else {
          console.warn(`[CRON] Subscription failed for user ${user._id}`);
        }

      } catch (err: any) {
        console.error(`[CRON] Error processing payment for user ${user._id}:`, err.message);
        user.paymentRetryCount = (user.paymentRetryCount || 0) + 1;
        user.lastPaymentError = err.message;
        user.lastPaymentAttemptAt = new Date();

        if (user.paymentRetryCount >= 3) {
          user.paymentFailed = true;
        }

        await user.save();
      }
    }

  } catch (err) {
    console.error(`[CRON] Subscription job failed:`, err);
  }
});





const fetchWithRetry = async (url, body, retries = 3, delay = 5000) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      return await response.json();

    } catch (error) {
      console.error(`❌ Attempt ${attempt} failed for ${url}:`, error.message);
      if (attempt < retries) await new Promise(res => setTimeout(res, delay));
    }
  }

  console.error(`⚠️ Skipping sync after ${retries} failed attempts: ${url}`);
  return null;
}

export const schedulingJob = cron.schedule("0 12,18 * * *", () => {
  Sentry.withIsolationScope(async () => {
    const span = Sentry.startSpanManual({
      op: 'cron.job',
      name: 'Check Expired Reservations',
    }, async () => {
      Sentry.getCurrentScope().setTag('job_type', 'reservation_expiry_check');
      Sentry.getCurrentScope().setTransactionName('Reservation Expiry Check');

      console.log(`[${new Date().toISOString()}] Cron Job Started: Checking expired reservations...`);

      try {
        const activePointMangs = await PointsMang.find({ isActive: true });

        if (!activePointMangs.length) {
          console.log(`[${new Date().toISOString()}] No active reservations found.`);
          return;
        }

        const currentDate = new Date();

        for (const pointMang of activePointMangs) {
          const checkoutDate = new Date(pointMang?.checkOut);

          if (checkoutDate <= currentDate) {
            console.log(
              `[${new Date().toISOString()}] Processing reservation: ${pointMang._id}, Checkout Date: ${checkoutDate.toDateString()}`
            );

            const updatedPointMang = await PointsMang.findByIdAndUpdate(
              pointMang._id,
              { isActive: false },
              { new: true }
            );

            console.log(`[${new Date().toISOString()}] Updated PointsMang:`, updatedPointMang);

            const updatedReservation = await Reservation.findByIdAndUpdate(
              pointMang?.reservationId,
              { status: "completed" },
              { new: true }
            );

            console.log(`[${new Date().toISOString()}] Updated Reservation:`, updatedReservation);

            const updatedUser = await User.findByIdAndUpdate(
              pointMang?.recievedBy,
              { $inc: { points: pointMang.pointsCount } },
              { new: true }
            );

            console.log(
              `[${new Date().toISOString()}] Updated User Points: ${updatedUser?.points}, User ID: ${updatedUser?._id}`
            );
          }
        }
      } catch (error) {
        console.error(`[${new Date().toISOString()}] Error in Cron Job:`, error);
        Sentry.captureException(error);
      } finally {
        console.log(`[${new Date().toISOString()}] Cron Job Finished`);
      }
    });
  });
});

export const IcalCronJob = cron.schedule("0 */2,*/6 * * *", () => {
  Sentry.withIsolationScope(async () => {
    const span = Sentry.startSpanManual({
      op: 'cron.job',
      name: 'iCal Booking Sync',
    }, async () => {
      Sentry.getCurrentScope().setTag('job_type', 'ical_sync');
      Sentry.getCurrentScope().setTransactionName('iCal Booking Sync');

      console.log(`[${new Date().toISOString()}] ⏳ Running scheduled booking sync...`);

      try {
        const properties = await Property.find({ externalSources: { $exists: true, $ne: [] } }).lean();

        if (!properties.length) {
          console.log("✅ No properties found with external sources. Skipping sync.");
          return;
        }

        for (const property of properties) {
          if (!Array.isArray(property.externalSources) || !property.externalSources.length) {
            console.warn(`⚠️ Invalid external sources for Property ID: ${property._id}`);
            continue;
          }

          for (const url of property.externalSources) {
            console.log(`🔄 Syncing bookings for Property ID: ${property._id} from ${url}`);

            const result = await fetchWithRetry(`${process.env.backendLink}/property/calendar/v1/sync`, {
              propertyId: property._id,
              url: url,
            });

            if (result) {
              console.log(`✅ Successfully synced bookings for Property ID: ${property._id}`);
            } else {
              console.warn(`⚠️ Skipped Property ID: ${property._id} due to fetch failure.`);
            }
          }
        }
      } catch (error) {
        console.error("❌ Error in booking sync cron job:", error);
        Sentry.captureException(error);
      } finally {
        console.log(`[${new Date().toISOString()}] ✅ Booking sync process completed.`);
      }
    });
  });
});






