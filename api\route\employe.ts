import { Router } from "express";
import { awsupload } from "../middleware/upload";
import checkRoleMiddleware from "../middleware/routeMiddleware";
import { Controller as EmployeController } from "../controllers/employe";

const router = Router();

// Common middleware for protected routes
const employeAccessControl = checkRoleMiddleware(['subadmin', 'Blogger', 'Financial']);

// Employee data retrieval routes
router.get("/getEmploye", employeAccessControl, EmployeController.getEmploye);

// Employee management routes
router.post('/addEmploye', employeAccessControl, awsupload.single('pic'), EmployeController.addEmploye);
router.put("/employeRole/:id", employeAccessControl, EmployeController.assignRole);

export default router;
