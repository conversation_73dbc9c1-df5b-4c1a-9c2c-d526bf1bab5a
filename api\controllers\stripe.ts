import { User, Reservation, Referral } from "../model";
import <PERSON><PERSON> from 'stripe';
import mongoose from "mongoose";
import { sendEmail } from '../services/mailJetEmail';
import { Request, Response } from 'express';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as  string, );

const authorizeSeller = async (req: Request, res: Response): Promise<void> => {
  const { code } = req.body;
  const { userId } = req.params;

  try {
    const response = await stripe.oauth.token({
      grant_type: "authorization_code",
      code,
    });

    const { access_token, refresh_token, stripe_user_id } = response;

    let userData;

    if (userId) {
      let userCheck = await User.findById({ _id: userId });
      if (!userCheck) {
        res.status(400).json({ message: "user not exist" });
        return;
      }
      await User.findByIdAndUpdate(
        { _id: userId },
        {
          $set: {
            stripe_account_id: stripe_user_id,
            stripe_refresh_token: refresh_token,
            stripe_access_token: access_token,
          },
        }
      );
      userData = await User.findById({ _id: userId });
    }

    res.json({
      userData,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: err.message,
    });
  }
};

const AddCardInfo = async (req: Request, res: Response): Promise<void> => {
  const { stripeToken, userId, cardName, isPrimary } = req.body;
  try {
    if (!stripeToken || !userId || !cardName) {
      res.status(400).json({ error: "Missing required fields" });
      return;
    }

    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    if (user.cardInfo.length >= 2) {
      res.status(400).json({ error: "You can only add up to two cards." });
      return;
    }

    let stripeCustomerId = user.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.fname + " " + user.lname,
        metadata: { userId: user._id.toString() },
      });
      stripeCustomerId = customer.id;
      user.stripeCustomerId = stripeCustomerId;
      await user.save();
    }

    const paymentMethod = await stripe.paymentMethods.create({
      type: "card",
      card: { token: stripeToken },
    });

    await stripe.paymentMethods.attach(paymentMethod.id, {
      customer: stripeCustomerId,
    });

    const paymentMethodDetails = await stripe.paymentMethods.retrieve(paymentMethod.id);
    const last4 = paymentMethodDetails.card.last4;
    const expiryDate = `${paymentMethodDetails.card.exp_month}/${paymentMethodDetails.card.exp_year}`;

    const isDuplicate = user.cardInfo.some(
      (card) => card.cardNumber.endsWith(last4) && card.expiryDate === expiryDate
    );

    if (isDuplicate) {
      res.status(400).json({ error: "This card is already added." });
      return;
    }

    if (isPrimary || user.cardInfo.length === 0) {
      await stripe.customers.update(stripeCustomerId, {
        invoice_settings: { default_payment_method: paymentMethod.id },
      });

      user.cardInfo.forEach(card => (card.primary = false));
    }


    user.cardInfo.push({
      paymentMethodId: paymentMethod.id,
      cardName: cardName,
      cardNumber: `**** **** **** ${paymentMethodDetails.card.last4}`,
      expiryDate: `${paymentMethodDetails.card.exp_month}/${paymentMethodDetails.card.exp_year}`,
      cardType: paymentMethodDetails.card.brand,
      country: paymentMethodDetails.card.country,
      funding: paymentMethodDetails.card.funding,
      primary: isPrimary || user.cardInfo.length === 0,
    });

    await user.save();

    res.status(200).json({ message: 'Card information saved successfully' });
  } catch (error) {
    console.error("Error saving card information:", error);
    res.status(500).json({ error: error.raw?.message || "Your card was declined." });
  }
};

const RemoveCard = async (req: Request, res: Response): Promise<void> => {
  const { userId, cardId } = req.body;

  try {
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }
    if (!user.cardInfo || user.cardInfo.length === 0) {
      res.status(400).json({ error: "No cards available to remove." });
      return;
    }

    const cardIndex = user.cardInfo.findIndex(card => card._id.toString() === cardId);
    if (cardIndex === -1) {
      res.status(404).json({ error: "Card not found" });
      return;
    }

    const isRemovingPrimary = user.cardInfo[cardIndex].primary;

    user.cardInfo.splice(cardIndex, 1);

    if (isRemovingPrimary && user.cardInfo.length > 0) {
      user.cardInfo[0].primary = true;
    }

    await user.save();
    res.status(200).json({ message: "Card removed successfully" });
  } catch (error) {
    console.error("Error removing card:", error);
    res.status(500).json({ error: "Failed to remove card." });
  }
};

const userSubscription = async (req: Request, res: Response): Promise<void> => {
  const { email, userId } = req.body;

  if (!email || !userId) {
    res.status(400).json({ success: false, message: "Required fields are missing" });
    return;
  }

  try {
    // Check if Stripe is initialized properly
    if (!stripe) {
      throw new Error("Stripe API is not initialized. Please check your configuration.");
    }

    // Retrieve user
    const userData = await User.findById(userId);
    if (!userData) {
      res.status(404).json({ success: false, message: "User not found" });
      return;
    }

    const isInfluencer = userData.roles.includes("Influencer");

    // Handle free Influencer subscription
    const totalFreeInfluencers = await User.countDocuments({
      roles: "Influencer",
      subscription: true,
      subscriptionType: "special"
    });

    if (isInfluencer && totalFreeInfluencers < 500) {
      await User.findByIdAndUpdate(userId, {
        $set: {
          subscription: true,
          subRole: "Elite"
        },
        $inc: { points: 3000 }
      });

      res.json({
        success: true,
        message: "Free subscription activated for Top 500 Influencer.",
      });
      return;
    }

    // Check if user has a Stripe customer and existing subscription
    let customer = await stripe.customers.list({ email, limit: 1 });
    let stripeCustomerId = customer.data.length ? customer.data[0].id : null;
    let previousSubscription = null;

    if (stripeCustomerId) {
      const subscriptions = await stripe.subscriptions.list({ customer: stripeCustomerId, limit: 1 });
      if (subscriptions.data.length > 0) {
        previousSubscription = subscriptions.data[0];
        const { status, cancel_at_period_end } = previousSubscription;

        if (status === "active" && !cancel_at_period_end) {
          res.status(400).json({ success: false, message: "You already have an active subscription." });
          return;
        }
      }
    }

    // Determine subscription price
    let subscriptionPrice = process.env.SUBSCRIPTION_WITHOUTAFFLIATED;
    if (!subscriptionPrice || typeof subscriptionPrice !== "string" || !subscriptionPrice.startsWith("price_")) {
      throw new Error("Invalid or missing Stripe price ID. Please check your environment variables.");
    }

    // Referral check
    let referrelCodeInfo = null;
    let ReferreledByUserType = "";

    if (userData.referredBy) {
      referrelCodeInfo = await Referral.findById(userData.referredBy);
      const referredByUser = await User.findById(referrelCodeInfo?.userId);
      ReferreledByUserType = referredByUser?.roles.includes("Influencer") ? "Influencer" : "host";

      if (ReferreledByUserType === "Influencer") {
        subscriptionPrice = process.env.SUBSCRIPTION_WITHAFFLIATED || subscriptionPrice;
      }
    }

    // Final Stripe customer check / create
    if (!stripeCustomerId) {
      const newCustomer = await stripe.customers.create({ email, metadata: { id: userId } });
      stripeCustomerId = newCustomer.id;
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      billing_address_collection: "required",
      customer: stripeCustomerId,
      line_items: [{ price: subscriptionPrice, quantity: 1 }],
      mode: "subscription",
      success_url: `${process.env.websiteLink}/paymentsuccess?userId=${userData._id}&referrelData=${referrelCodeInfo?._id}&subscriptionType=${referrelCodeInfo ? "referrel" : "normal"}`,
      cancel_url: `${process.env.websiteLink}/paymenterror`,
    });

    res.json({ sessionId: session.id });

  } catch (err) {
    console.error("Error in userSubscription:", err);
    res.status(500).json({
      success: false,
      message: err.message || "Something went wrong while processing the subscription.",
    });
  }
};

const allTransections = async (req: Request, res: Response): Promise<void> => {
  try {
    const allCharges = [];
    let hasMore = true;
    let startingAfter;

    while (hasMore) {
      const chargesList = await stripe.charges.list({
        limit: 100,
        starting_after: startingAfter,
        expand: ['data.customer']
      });

      const chargesDetails = chargesList.data.map(charge => {

        return {
          transactionId: charge.id,
          date: new Date(charge.created * 1000).toISOString(),
          transactionType: charge.object,
          amount: charge.amount / 100,
          paymentMethod: charge.payment_method_details?.type,
          cardBrand: charge.payment_method_details?.card?.brand,
          last4: charge.payment_method_details?.card?.last4,
          expMonth: charge.payment_method_details?.card?.exp_month,
          expYear: charge.payment_method_details?.card?.exp_year,
          description: charge.description,
          source: charge.source?.id,
          receiptUrl: charge.receipt_url,
          status: charge.status,
          name: charge.billing_details.name || null,
          email: charge.customer || null,
        };
      });

      allCharges.push(...chargesDetails);
      hasMore = chargesList.has_more;

      if (hasMore) {
        startingAfter = chargesList.data[chargesList.data.length - 1].id;
      }
    }

    res.json(allCharges);
  } catch (error) {
    console.error("Error retrieving charges:", error);
    res.status(500).json({ error: "Failed to retrieve transactions" });
  }
};

const userSubscriptionAfterSuccess = async (req: Request, res: Response): Promise<void> => {
  const { referrelId, userId, subscriptionType } = req.body;
  if (!userId) {
    res.status(400).json({ sucess: false, message: "required field are missings" });
    return;
  }
  try {
    let userData = await User.findById({ _id: userId });

    if (userData?.subRole && userData?.subscription === true) {
      res.status(400).json({
        success: false,
        message: "you have already subscription plan",
      });
      return;
    }
    const newPoints = subscriptionType === "referrel" ? 3000 : 2000;
    await User.findByIdAndUpdate(userId, {
      $set: {
        subscriptionType,
        subRole: subscriptionType === "referrel" ? "Elite" : "Regular",
        subscription: true,
      },
      $inc: { points: newPoints },
    });

    if (subscriptionType === "referrel" && referrelId) {
      let referrelData = await Referral.findByIdAndUpdate(referrelId, {
        points: true,
      });
      let referralUser = await User.findById({ _id: referrelData.userId });
      if (referralUser) {
        if (!referralUser.referrelUsers) {
          referralUser.referrelUsers = [];
        }
        !referralUser.referrelUsers.includes(userId) &&
          referralUser?.referrelUsers.push(userId);
        referralUser.points = (referralUser?.points || 0) + 50;
        await referralUser.save();
      }
    }
    let { stripeCustomerId, subscriptionId } = userData;

    if (!stripeCustomerId) {
      const customers = await stripe.customers.list({ email: userData.email, limit: 1 });

      if (customers.data.length === 0) {
        res.status(404).json({ success: false, message: "Stripe customer not found" });
        return;
      }

      stripeCustomerId = customers.data[0].id;
      await User.findByIdAndUpdate(userId, { stripeCustomerId });
    }

    if (!subscriptionId) {
      const subscriptions = await stripe.subscriptions.list({ customer: stripeCustomerId, status: "active" });

      if (subscriptions.data.length === 0) {
        res.status(404).json({ success: false, message: "No active subscriptions found" });
        return;
      }

      subscriptionId = subscriptions.data[0].id;
      await User.findByIdAndUpdate(userId, { subscriptionId });
    }

    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: [
        'customer',
        'default_payment_method',
        'items.data.price.product'
      ]
    });

    if (!subscription) {
      res.status(404).json({ success: false, message: "Subscription details not found" });
      return;
    }

    // TypeScript safe access with type casting
    const customer = subscription.customer as Stripe.Customer;
    const product = subscription.items.data[0]?.price?.product as Stripe.Product;
    const paymentMethod = subscription.default_payment_method as Stripe.PaymentMethod | null;

    // Check for missing required data
    if (!product || !customer) {
      res.status(404).json({
        success: false,
        message: "Subscription details incomplete"
      });
      return;
    }

    const nextBillingDate = new Date(subscription.current_period_end * 1000);
    await User.findByIdAndUpdate(userId, { nextBillingDate });

    const EmailParameters = {
      customerName: customer.name || "Valued Customer",
      plan: product.name || "Unknown Plan",
      amount: `${(subscription.items.data[0]?.price.unit_amount / 100).toFixed(2)} ${subscription.currency.toUpperCase()} / ${subscription.items.data[0]?.price.recurring.interval}`,
      startDate: new Date(subscription.start_date * 1000).toDateString(),
      nextBillingDate: nextBillingDate.toDateString(),
      paymentMethod: `•••• •••• •••• ${paymentMethod?.card?.last4 || "****"}`,
      receiptNumber: subscription.latest_invoice || "N/A",
    };
    sendEmail(
      customer.email,
      "Subscription Activation Confirmation",
      EmailParameters,
      "activate_subscription"
    );
    res.status(200).json({
      success: true, message: "successfully updated"

    });
  } catch (err) {
    console.error(err);
    res.status(500).send({ error: err.message });
  }
};

const refundPayment = async (req: Request, res: Response): Promise<void> => {
  const { paymentIntentId, amount, selectedReservationId } = req.body;
  if (!paymentIntentId || !amount) {
    res.status(400).json({
      success: false,
      message: "Invalid request. Please provide both paymentIntentId and amount.",
    });
  }

  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
      expand: ['latest_charge']
    });

    if (!paymentIntent) {
      res.status(404).json({
        success: false,
        message: "PaymentIntent not found.",
      });
      return;
    }

    const charge = typeof paymentIntent.latest_charge === 'object'
      ? paymentIntent.latest_charge
      : null;

    if (!charge) {
      res.status(404).json({
        success: false,
        message: "Associated charge not found for this payment intent.",
      });
      return;
    }

    const unrefundedAmount = charge.amount - charge.amount_refunded;

    const refundAmountInCents = Math.round(amount * 100);
    if (refundAmountInCents <= 0) {
      res.status(400).json({
        success: false,
        message: "Refund amount must be greater than zero.",
      });
      return;
    }

    if (refundAmountInCents > unrefundedAmount) {
      res.status(400).json({
        success: false,
        message: `Refund amount (${amount}) exceeds the remaining refundable amount (${unrefundedAmount / 100}).`,
      });
      return;
    }

    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
      amount: refundAmountInCents,
    });

    const updatedReservation = await Reservation.findByIdAndUpdate(
      selectedReservationId,
      {
        serviceFeeRefunded: amount,
      },
      { new: true }
    );

    if (!updatedReservation) {
      res.status(404).json({
        success: false,
        message: "Reservation not found, but payment refund was successful.",
        refund,
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: "Refund successful",
    });
  } catch (error) {
    console.error("Error processing refund:", error);

    if (error.type === "StripeInvalidRequestError") {
      res.status(400).json({
        success: false,
        message: `Invalid request: ${error.message}`,
      });
      return;
    }

    if (error.type === "StripeAPIError") {
      res.status(500).json({
        success: false,
        message: "An error occurred while communicating with Stripe.",
      });
      return;
    }

    if (error.type === "StripeCardError") {
      res.status(400).json({
        success: false,
        message: `Card error: ${error.message}`,
      });
      return;
    }

    if (error.type === "StripeAuthenticationError") {
      res.status(401).json({
        success: false,
        message: "Authentication with Stripe API failed. Please check your API keys.",
      });
      return;
    }

    if (error.type === "StripeRateLimitError") {
      res.status(429).json({
        success: false,
        message: "Too many requests made to Stripe API too quickly. Please try again later.",
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: "An unexpected error occurred while processing the refund.",
      error: error.message,
    });
  }
};


const trackPayment = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;

    if (!mongoose.isValidObjectId(userId)) {
      res.status(400).json({ error: "Invalid user ID" });
      return;
    }
    const user = await User.findById(userId, "email stripeCustomerId");
    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    const { email: userEmail, stripeCustomerId } = user;
    let stripeSubscriptions = [];
    const userData = await Reservation.aggregate([
      { $match: { userId: new mongoose.Types.ObjectId(userId) } },
      {
        $lookup: {
          from: "properties",
          localField: "property_id",
          foreignField: "_id",
          as: "property_details",
        },
      },
      { $unwind: { path: "$property_details", preserveNullAndEmptyArrays: true } },
      {
        $project: {
          reservation_id: "$_id",
          total_cost: "$offerState.total",
          serviceFee: "$offerState.serviceFee",
          payment_status: "$status",
          property_location: "$property_details.address",
          refund_status: "$isRefundStatus",
          check_in: "$offerState.checkIn",
          check_out: "$offerState.checkOut",
        },
      },
    ]);

    let customerId = stripeCustomerId;
    if (!customerId) {
      const customers = await stripe.customers.list({ email: userEmail, limit: 1 });
      if (customers.data.length > 0) {
        customerId = customers.data[0].id;
      } else {
        res.status(404).json({ error: "Stripe customer not found for this email." });
        return;
      }
    }
    const subscriptions = await stripe.subscriptions.list({ customer: customerId });
    stripeSubscriptions = subscriptions.data.map((sub) => ({
      subscription_id: sub.id,
      status: sub.status,
      amount: sub.items.data[0]?.price?.unit_amount / 100 || 0,
      plan: sub.items.data[0]?.plan?.nickname || "N/A",
      start_date: sub.start_date ? new Date(sub.start_date * 1000).toISOString() : null,
      current_period_end: sub.current_period_end ? new Date(sub.current_period_end * 1000).toISOString() : null,
      auto_renewal: !sub.cancel_at_period_end,
      canceled_at: sub.canceled_at ? new Date(sub.canceled_at * 1000).toISOString() : null,
    }));
    res.status(200).json({
      userData: userData.length > 0 ? userData : [],
      stripeSubscription: stripeSubscriptions,
    });

  } catch (error) {
    console.error("Error in trackPayment:", error);
    res.status(500).json({ error: "Internal server error. Please try again later." });
  }
};



const subscriptionDetail = async (req: Request, res: Response): Promise<void> => {
  const { user_email } = req.body;
  let stripeCustomer = null;
  let stripeSubscriptions = [];

  try {
    const customers = await stripe.customers.list({ email: user_email, limit: 1 });

    if (customers.data.length > 0) {
      stripeCustomer = customers.data[0].id;
    } else {
      res.status(404).json({ error: "Stripe customer not found for this email." });
      return;
    }

    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomer,
      expand: ['data.items.data.price'],
    });
    const productIds = subscriptions.data.map(
      (sub) => sub.items.data[0]?.price?.product
    );

    const productPromises = productIds.map((productId) =>
      stripe.products.retrieve(productId as string)
    );

    const products = await Promise.all(productPromises);

    stripeSubscriptions = subscriptions.data.map((sub, index) => {
      const price = sub.items.data[0]?.price;
      const product = products[index];

      return {
        subscription_id: sub.id,
        status: sub.status,
        amount: price?.unit_amount ? price.unit_amount / 100 : 0,
        plan_name: product?.name || 'N/A',
        start_date: new Date(sub.start_date * 1000).toISOString(),
        current_period_end: new Date(sub.current_period_end * 1000).toISOString(),
        auto_renewal: !sub.cancel_at_period_end,
        canceled_at: sub.canceled_at ? new Date(sub.canceled_at * 1000).toISOString() : null,
      };
    });

    res.status(200).json({
      stripeSubscription: stripeSubscriptions
    });

  } catch (error) {
    console.error("Error fetching subscription details:", error);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve subscription details",
      error: error.message,
    });
  }
};



const cancelSubscription = async (req: Request, res: Response): Promise<void> => {
  const userId = req.params.id;
  const { subscriptionId } = req.body;

  if (!subscriptionId || !userId) {
    res.status(400).json({ success: false, message: "Subscription ID and User ID are required" });
    return;
  }

  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    if (!subscription || subscription.status === "canceled") {
      res.status(404).json({ success: false, message: "Subscription not found or already canceled" });
      return;
    }

    const canceledSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    await User.findByIdAndUpdate(userId, {
      $set: {
        subscription: true,
        subscriptionStatus: "pending_cancellation",
        subscriptionCancelAt: new Date(canceledSubscription.current_period_end * 1000),
      },
    });

    const customerData = await stripe.customers.retrieve(canceledSubscription.customer as string);
    const customer = customerData as Stripe.Customer;

    const firstItem = canceledSubscription.items.data[0];
    const productId = firstItem?.price?.product as string;
    const product = productId ? await stripe.products.retrieve(productId) : null;

    if (!customer || !product) {
      console.error("Missing customer or product data in subscription");
    }

    const EmailParameters = {
      customerName: customer?.name || "Valued Customer",
      subscriptionPlan: product?.name || "Unknown Plan",
      cancellationDate: new Date().toDateString(),
      accessUntil: new Date(canceledSubscription.current_period_end * 1000).toDateString(),
      cancellationReference: canceledSubscription.id,
    };

    if (customer?.email) {
      await sendEmail(
        customer.email,
        "Subscription Cancellation Confirmation",
        EmailParameters,
        "Deactivate_subscription_template"
      );
    } else {
      console.error("Customer email not available for cancellation notification");
    }

    res.status(200).json({
      success: true,
      message: "Subscription cancellation scheduled",
      subscription_id: canceledSubscription.id,
      status: "pending_cancellation",
      cancel_at_period_end: canceledSubscription.cancel_at_period_end,
      current_period_end: new Date(canceledSubscription.current_period_end * 1000).toISOString(),
    });

  } catch (error) {
    console.error("Error canceling subscription:", error);
    res.status(500).json({ success: false, message: "Failed to cancel subscription", error: error.message });
  }
};

export const Controller = {
  authorizeSeller,
  AddCardInfo,
  RemoveCard,
  userSubscription,
  userSubscriptionAfterSuccess,
  refundPayment,
  allTransections,
  trackPayment,
  cancelSubscription,
  subscriptionDetail
};