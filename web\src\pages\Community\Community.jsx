import { Container, Rating } from "@mui/material";
import React, { useEffect, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import image1 from "assets/img/angela.png";
import image2 from "assets/img/angela.png";
import image3 from "assets/img/angela.png";
import image4 from "assets/img/angela.png";
import { useMutation } from "react-query";
import userServices from "services/httpService/userAuth/userServices";
import { useNavigate } from "react-router-dom";
import { localStorageData } from "services/auth/localStorageData";
import { toast } from "react-toastify";
import ErrorService from "services/formatError/ErrorService";

const Community = ({ setStartConversation }) => {

  const [slider, setSlider] = useState(null);
  const [allcommunity, setallcommunity] = useState([]);
  const [handleLikefunction, sethandleLikefunction] = useState("");
  const navigate = useNavigate();

  const slideData = [
    {
      id: 1,
      image: image1,
      name: "<PERSON> Karamoy",
      subtitle: "Port Moody, Canada Community Manager",
      replay: "Latest reply",
      descripation:
        "Do you have a Child Friendly Listing? Hello everyone,Creating a welcoming space for families seeking child-friendly listings can be a rewarding experience, as i...",
    },
    {
      id: 2,
      image: image2,
      name: "Angela Karamoy",
      subtitle: "Port Moody, Canada Community Manager",
      replay: "Latest reply",
      descripation:
        "Do you have a Child Friendly Listing? Hello everyone,Creating a welcoming space for families seeking child-friendly listings can be a rewarding experience, as i...",
    },
    {
      id: 3,
      image: image3,
      name: "Angela Karamoy",
      subtitle: "Port Moody, Canada Community Manager",
      replay: "Latest reply",
      descripation:
        "Do you have a Child Friendly Listing? Hello everyone,Creating a welcoming space for families seeking child-friendly listings can be a rewarding experience, as i...",
    },
    {
      id: 4,
      image: image4,
      name: "Angela Karamoy",
      subtitle: "Port Moody, Canada Community Manager",
      replay: "Latest reply",

      descripation:
        "Do you have a Child Friendly Listing? Hello everyone,Creating a welcoming space for families seeking child-friendly listings can be a rewarding experience, as i...",
    },
  ];

  //slider responsiveness
  const settings = {
    dots: true,
    autoplay: true,
    infinite: true,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };
  const defaultProps = {
    center: {},
    zoom: 11,
  };

  const handleNext = () => {
    setStartConversation(true);
  };

  // Mutation hook for liking a post
  const { mutate: communitylikeFunction, isLoading } = useMutation(
    (token) => userServices.communitylikePost("post/likePost", token),
    {
      onError: (error) => {
        toast.error(ErrorService.uniformError(error));
      },
      onSuccess: (data) => {
        toast.success("post like successfully");
        getproperty2();
      },
    }
  );

  // Function to fetch and set community posts
  const getproperty2 = async () => {
    let res = await userServices.commonGetService(`post/communityPost`);
    setallcommunity(res.data.data);
  };

  useEffect(() => {
    getproperty2();
  }, []);

  //like action for a specific community post
  const handleLike = (id) => {
    communitylikeFunction({
      userId: localStorageData("_id"),
      communityId: id,
    });
  };

  return (
    <>
      <section className="my-6">
        <Container maxWidth="lg">
          <Container maxWidth="lg">
            <div className=" p-2 md:flex ">
              <div className="md:text-3xl text-xl font-semibold my-4 md:my-0 mx-2 md:mx-0 flex  items-center md:w-1/2">
                <div className="  ">
                  <p>BnByonders' Hub: Your Gateway</p>
                  <p className="flex items-center">
                    to{" "}
                    <span className="text-color-primary ml-2">
                      {" "}
                      Extraordinary Travel
                    </span>
                    <span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="29"
                        height="37"
                        viewBox="0 0 29 37"
                        fill="none"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M18.2612 1.54401C18.4861 1.70415 19.3103 2.58764 18.9684 3.4905L6.59391 18.7962C6.43377 19.0212 6.12158 19.0737 5.89661 18.9136C5.67164 18.7534 5.6191 18.4413 5.77924 18.2163L15.8697 1.29459C16.6007 0.538276 18.0362 1.38386 18.2612 1.54401Z"
                          fill="#2459BF"
                        />
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M27.0545 29.0862C26.9895 29.3546 26.4761 30.4483 25.5108 30.4669L6.69986 24.6756C6.43146 24.6107 6.26653 24.3404 6.33147 24.072C6.39641 23.8036 6.66664 23.6387 6.93503 23.7036L26.3962 26.7736C27.3702 27.1708 27.1194 28.8178 27.0545 29.0862Z"
                          fill="#2459BF"
                        />
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M27.2843 15.8478C27.3499 16.1161 27.3962 17.3234 26.5474 17.7832L7.17808 21.2795C6.90984 21.3451 6.63921 21.1808 6.57361 20.9125C6.50802 20.6443 6.67229 20.3737 6.94053 20.3081L25.6374 14.096C26.685 14.0014 27.2187 15.5796 27.2843 15.8478Z"
                          fill="#2459BF"
                        />
                      </svg>
                    </span>
                  </p>
                  <p> Experiences</p>
                  <button
                    onClick={handleNext}
                    className="bg-blue-600 outline-none px-4 py-2 uppercase text-sm text-white rounded-full mt-2"
                  >
                    {" "}
                    Start a conversation
                  </button>
                </div>
              </div>
              <div>
                <img src={require("assets/img/person.png")} alt="" />
              </div>
            </div>
            <div className="my-10">
              <div className="flex flex-wrap justify-center md:justify-start gap-2 ">
                {[1, 1, 1, 1].map(() => {
                  return (
                    <div className="md:w-[265px] p-2 border rounded-md border-color-grey bg-color-grey/10">
                      <div className="mb-4 mt-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="54"
                          height="55"
                          viewBox="0 0 54 55"
                          fill="none"
                        >
                          <circle
                            opacity="0.1"
                            cx="27.0327"
                            cy="27.6978"
                            r="26.8608"
                            fill="#58C0D0"
                          />
                          <path
                            d="M20.7193 47.8127H33.5312C34.8177 47.8127 35.8606 46.7698 35.8606 45.4833V37.8736L40.99 36.4702C41.3567 36.3676 41.6798 36.1481 41.9102 35.8451C42.1407 35.542 42.2658 35.1719 42.2666 34.7912V24.7717L42.581 24.6838C44.0478 24.2726 44.9035 22.7504 44.4924 21.2836C44.4261 21.0472 44.3286 20.8207 44.2023 20.6101L42.1838 17.2325L42.1786 17.236C42.1047 17.108 41.9847 17.0132 41.8432 16.971L27.7548 12.958C27.3425 12.8421 26.9062 12.8421 26.4939 12.958L12.4067 16.971C12.2654 17.0134 12.1456 17.1082 12.0718 17.236L12.0666 17.2325L10.0487 20.6102C9.26413 21.9159 9.68657 23.6103 10.9922 24.3948C11.2039 24.522 11.4316 24.6201 11.6694 24.6867L11.9838 24.7747V34.7912C11.9848 35.1726 12.1105 35.5433 12.3418 35.8465C12.5732 36.1498 12.8974 36.369 13.265 36.4707L18.3897 37.8736V45.4833C18.3898 46.7698 19.4327 47.8127 20.7193 47.8127ZM34.6959 45.4833C34.6959 46.1266 34.1744 46.6481 33.5312 46.6481H20.7193C20.076 46.6481 19.5546 46.1266 19.5546 45.4833V30.9244C19.5546 30.2811 20.076 29.7596 20.7193 29.7596H33.5312C34.1744 29.7596 34.6959 30.2811 34.6959 30.9244V45.4833ZM41.1018 34.7912C41.101 34.917 41.0595 35.0392 40.9835 35.1394C40.9075 35.2397 40.8011 35.3126 40.6802 35.3473L35.8606 36.6658V30.9243C35.8606 29.6378 34.8177 28.5949 33.5312 28.5949H27.7076V23.7106L29.2648 26.3068C29.5104 26.7153 29.8574 27.0534 30.2722 27.2881C30.687 27.5228 31.1555 27.6462 31.6321 27.6463C31.8824 27.6466 32.1314 27.6123 32.3723 27.5444L41.1019 25.1008V34.7912H41.1018ZM43.3148 22.6199C43.2249 22.8474 43.0835 23.0509 42.9016 23.2144C42.7197 23.3779 42.5023 23.4969 42.2666 23.5621L32.0578 26.4221C31.7217 26.5158 31.3641 26.4967 31.0399 26.3676C30.7157 26.2385 30.4427 26.0067 30.263 25.7076L28.0168 21.9624L41.4111 18.212L43.2012 21.2082C43.3282 21.4181 43.4045 21.6547 43.4242 21.8992C43.4439 22.1437 43.4065 22.3894 43.3148 22.617V22.6199ZM26.8113 14.0796C27.016 14.0219 27.2327 14.0219 27.4374 14.0796L39.5411 17.5266L27.1252 21.0027L14.7093 17.5266L26.8113 14.0796ZM10.9357 22.617C10.8443 22.3891 10.8073 22.143 10.8276 21.8983C10.8478 21.6536 10.9247 21.417 11.0522 21.2071L12.8377 18.212L26.2319 21.9624L23.9858 25.7076C23.8062 26.0069 23.5333 26.2389 23.2091 26.368C22.8848 26.4971 22.5271 26.5161 22.1909 26.4221L11.9839 23.5621C11.7479 23.4966 11.5304 23.3771 11.3485 23.213C11.1666 23.049 11.0252 22.845 10.9357 22.617ZM13.5726 35.3503C13.4508 35.3159 13.3435 35.2427 13.267 35.1418C13.1905 35.0409 13.1489 34.9178 13.1486 34.7912V25.1007L21.8782 27.5466C23.0742 27.8802 24.3457 27.3743 24.9857 26.3103L26.5429 23.7106V28.5949H20.7193C19.4328 28.5949 18.3899 29.6378 18.3899 30.9243V36.6658L13.5726 35.3503Z"
                            fill="#58C0D0"
                          />
                          <path
                            d="M23.8016 31.6773L22.4663 33.0126L22.2957 32.842C22.1857 32.7368 22.0388 32.6787 21.8866 32.6804C21.7343 32.6821 21.5888 32.7433 21.4811 32.851C21.3735 32.9586 21.3123 33.1042 21.3106 33.2564C21.3089 33.4086 21.3669 33.5555 21.4722 33.6655L22.0545 34.2478C22.1638 34.357 22.3119 34.4183 22.4663 34.4183C22.6207 34.4183 22.7688 34.357 22.878 34.2478L24.6251 32.5007C24.7304 32.3907 24.7884 32.2439 24.7867 32.0916C24.785 31.9394 24.7238 31.7939 24.6162 31.6862C24.5085 31.5785 24.363 31.5173 24.2107 31.5157C24.0585 31.514 23.9116 31.572 23.8016 31.6773ZM32.3664 32.6714H26.5428C26.3883 32.6714 26.2402 32.7328 26.131 32.842C26.0218 32.9512 25.9604 33.0993 25.9604 33.2538C25.9604 33.4082 26.0218 33.5563 26.131 33.6655C26.2402 33.7747 26.3883 33.8361 26.5428 33.8361H32.3664C32.688 33.8361 32.9487 33.5754 32.9487 33.2538C32.9487 32.9321 32.688 32.6714 32.3664 32.6714ZM23.8016 35.1715L22.4663 36.5068L22.2957 36.3362C22.1857 36.2309 22.0388 36.1729 21.8866 36.1746C21.7343 36.1762 21.5888 36.2375 21.4811 36.3451C21.3735 36.4528 21.3123 36.5983 21.3106 36.7506C21.3089 36.9028 21.3669 37.0497 21.4722 37.1596L22.0545 37.742C22.1638 37.8511 22.3119 37.9125 22.4663 37.9125C22.6207 37.9125 22.7688 37.8511 22.878 37.742L24.6251 35.9949C24.7304 35.8849 24.7884 35.7381 24.7867 35.5858C24.785 35.4336 24.7238 35.288 24.6162 35.1804C24.5085 35.0727 24.363 35.0115 24.2107 35.0098C24.0585 35.0082 23.9116 35.0662 23.8016 35.1715ZM32.3664 36.1656H26.5428C26.3883 36.1656 26.2402 36.2269 26.131 36.3361C26.0218 36.4454 25.9604 36.5935 25.9604 36.7479C25.9604 36.9024 26.0218 37.0505 26.131 37.1597C26.2402 37.2689 26.3883 37.3303 26.5428 37.3303H32.3664C32.5208 37.3303 32.6689 37.2689 32.7781 37.1597C32.8874 37.0505 32.9487 36.9024 32.9487 36.7479C32.9487 36.5935 32.8874 36.4454 32.7781 36.3361C32.6689 36.2269 32.5208 36.1656 32.3664 36.1656ZM23.8016 38.6656L22.4663 40.001L22.2957 39.8304C22.1857 39.7251 22.0388 39.6671 21.8866 39.6687C21.7343 39.6704 21.5888 39.7316 21.4811 39.8393C21.3735 39.947 21.3123 40.0925 21.3106 40.2447C21.3089 40.397 21.3669 40.5438 21.4722 40.6538L22.0545 41.2362C22.1638 41.3453 22.3119 41.4066 22.4663 41.4066C22.6207 41.4066 22.7688 41.3453 22.878 41.2362L24.6251 39.4891C24.7304 39.3791 24.7884 39.2322 24.7867 39.08C24.785 38.9277 24.7238 38.7822 24.6162 38.6745C24.5085 38.5669 24.363 38.5057 24.2107 38.504C24.0585 38.5023 23.9116 38.5603 23.8016 38.6656ZM32.3664 39.6598H26.5428C26.3883 39.6598 26.2402 39.7211 26.131 39.8303C26.0218 39.9395 25.9604 40.0876 25.9604 40.2421C25.9604 40.3965 26.0218 40.5447 26.131 40.6539C26.2402 40.7631 26.3883 40.8244 26.5428 40.8244H32.3664C32.688 40.8244 32.9487 40.5637 32.9487 40.2421C32.9487 39.9204 32.688 39.6598 32.3664 39.6598ZM23.8016 42.1598L22.4663 43.4951L22.2957 43.3245C22.1857 43.2193 22.0388 43.1612 21.8866 43.1629C21.7343 43.1646 21.5888 43.2258 21.4811 43.3335C21.3735 43.4411 21.3123 43.5867 21.3106 43.7389C21.3089 43.8911 21.3669 44.038 21.4722 44.148L22.0545 44.7303C22.1638 44.8395 22.3119 44.9008 22.4663 44.9008C22.6207 44.9008 22.7688 44.8395 22.878 44.7303L24.6251 42.9832C24.7304 42.8732 24.7884 42.7264 24.7867 42.5742C24.785 42.4219 24.7238 42.2764 24.6162 42.1687C24.5085 42.0611 24.363 41.9998 24.2107 41.9982C24.0585 41.9965 23.9116 42.0545 23.8016 42.1598ZM32.3664 43.1539H26.5428C26.3883 43.1539 26.2402 43.2153 26.131 43.3245C26.0218 43.4337 25.9604 43.5818 25.9604 43.7363C25.9604 43.8907 26.0218 44.0388 26.131 44.148C26.2402 44.2572 26.3883 44.3186 26.5428 44.3186H32.3664C32.688 44.3186 32.9487 44.0579 32.9487 43.7363C32.9487 43.4146 32.688 43.1539 32.3664 43.1539Z"
                            fill="#58C0D0"
                          />
                        </svg>
                      </div>
                      <p>Ask about your </p>
                      <p>listing</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </Container>
        </Container>
      </section>
      <section>
        <div className="bg-color-grey/50 p-4">
          <h2 className="text-center font-semibold text-2xl ">
            Featured Conversations
          </h2>
          <p className="text-sm text-color-darkgrey text-center">
            Discover why they rave about their stays and adventures. Their
            stories are a testament to the exceptional memories that await you.
          </p>
          <div className="w-3/4 block mx-auto pb-4">
            <Slider ref={(c) => setSlider(c)} {...settings}>
              {slideData.map((item) => (
                <div className="px-1 py-4 w-[352.06px]  mt-5">
                  <div className="w-full h-full px-4 py-4 rounded-xl bg-[#F8F8F8]">
                    <div className="flex items-center gap-4  h-[52px]">
                      <div>
                        <img src={item.image} />
                      </div>
                      <div>
                        <h4 className="text-lg text-[#000000] font-normal">
                          {item.name}
                        </h4>
                        <p className="text-sm text-[#646464] font-normal">
                          {item.subtitle}
                        </p>
                      </div>
                    </div>
                    <div className="mt-5">
                      <p className="text-[#00000] text-base font-normal leading-7">
                        {item.descripation}
                      </p>
                    </div>
                    <div className="mt-5">
                      <p className="text-[#00000] text-base font-normal leading-7">
                        {item.replay}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        </div>
      </section>
      <section>
        <Container maxWidth="lg">
          <Container maxWidth="lg">
            <div>
              <h3 className="text-xl font-semibold my-4">
                What Hosts are saying
              </h3>
              <div className="border rounded-md p-4">
                {allcommunity.map((item, index) => {
                  const hasLiked = item?.likes.length && item.likes.some(like => like.user?._id == localStorageData("_id"));
                  return (
                    <div
                      key={index}
                      className="mt-2 md:flex border-b border-b-color-grey"
                    >
                      <div className="md:w-1/12 p-1">
                        <img
                          src={item.userId.pic}
                          className="block md:mx-auto w-[50px]   rounded-full "
                          alt=""
                        />
                      </div>
                      <div className="w-full">
                        <p className="font-medium">
                          {item.userId.fname} {item.userId.lname}
                        </p>
                        <p className="text-sm text-color-darkgrey">
                          Port Moody, Canada Community Manager
                        </p>
                        <div className=" my-4">
                          <h3 className="font-semibold">
                            {/* Getting ready for World Clean Up Day 2023 */}
                            {item.subject}
                          </h3>

                          <p
                            className="text-sm text-color-darkgrey"
                            dangerouslySetInnerHTML={{
                              __html: item.description,
                            }}
                          ></p>
                        </div>
                        <div className="flex mb-4 justify-between  text-sm text-color-grey">
                          <p>Host Advisory Board Member . Australia</p>
                          <p>
                            Monday . {item.replies.length}{" "}
                            <span
                              className="cursor-pointer"
                              onClick={() =>
                                navigate(`/communityreply/${item._id}`)
                              }
                            >
                              Replies
                            </span>{" "}
                            . {item.likes.length}{" "}
                            <span
                              className="cursor-pointer"
                              onClick={() => handleLike(item._id)}
                            >
                              {
                                hasLiked ? "Unlike" : "Like"
                              }
                            </span>

                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
                <button className="border outline-none border-color-primary text-sm mx-auto block rounded-full px-4 py-2 text-color-primary my-4">
                  Read More
                </button>
              </div>
            </div>
          </Container>
        </Container>
      </section>
      <section>
        <div className="bg-[#C0392D] md:flex  px-4 relative">
          <div className="md:w-1/2  z-10 flex flex-col items-center justify-center">
            <div>
              <h3 className="text-white text-3xl font-semibold ">
                Something on your mind?
              </h3>
              <h3 className="text-white text-3xl font-semibold ">
                Discuss with us!
              </h3>
            </div>
            <button className="text-[#C0392D] outline-none bg-white px-4 py-2 mt-2 rounded-full ">
              Start a conversation
            </button>
          </div>
          <div className="md:w-1/2 z-10">
            <img
              src={require("assets/img/alluser.png")}
              className="z-10"
              alt=""
            />
          </div>
          <img
            src={require("assets/img/inside.png")}
            className="absolute left-0 bottom-0 md:opacity-0 opacity-50"
            style={{ zIndex: 5 }}
            alt=""
          />
        </div>
      </section>
    </>
  );
};

export default Community;
