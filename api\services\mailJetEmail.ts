import mailjet from 'node-mailjet';
import {
  forgetEmailBody,
  verifyEmailBody,
  welcomeEmailBody,
  employDetail,
  propertyListingEmailBody,
  bookingDetailsConfirmationMessage,
  toTheOwnerTemplate,
  emailsubscription,
  propertyVerify,
  propertyRejected,
  transactionDetails,
  contactHostEmailTemplate,
  activateSubscription,
  CancelSubscription,
  influencerSignupTemplate,
  afterListingNotification,
  subscriptionNotification,
  thanksEmailTemplate
} from './EmailTemplates';

// Initialize only if not in test mode or if keys are available
const isTestMode = process.env.NODE_ENV === 'test';
const client = !isTestMode && process.env.MAIL_JET_KEY1 && process.env.MAIL_JET_KEY2 
  ? mailjet.connect(process.env.MAIL_JET_KEY1, process.env.MAIL_JET_KEY2)
  : null;

// Helper to get template content
const getEmailTemplate = (templateName:any, data:any) => {
  switch(templateName) {
    case 'veerify_Email_Body': return verifyEmailBody(data);
    case 'Welcome_Email_Body': return welcomeEmailBody(data);
    case 'ForgetPass_Email_Body': return forgetEmailBody(data);
    case 'send_employe_detail': return employDetail(data);
    case 'booking_details_confirmation': return bookingDetailsConfirmationMessage(data);
    case 'Property_Listing_Email_Body': return propertyListingEmailBody(data);
    case 'send_to_owner_template': return toTheOwnerTemplate(data);
    case 'email_subscription': return emailsubscription(data);
    case 'property_listed': return propertyVerify(data);
    case 'property_rejected': return propertyRejected(data);
    case 'transaction_details': return transactionDetails(data);
    case 'contact_host_template': return contactHostEmailTemplate(data);
    case 'activate_subscription': return activateSubscription(data);
    case 'cancel_subscription': return CancelSubscription(data);
    case 'influencer_Email_Body': return influencerSignupTemplate(data);
    case 'after_Listing_Notification': return afterListingNotification(data);
    case 'subscription_Notification': return subscriptionNotification(data);
    case 'thanks_Email_Template': return thanksEmailTemplate(data);
    default: return '';
  }
};

export const sendEmail = async (recipient, subject, data, emailTemplate) => {
  console.log(recipient);
  
  try {
    const htmlContent = getEmailTemplate(emailTemplate, data);
    
    // Don't attempt to send emails in test mode
    if (isTestMode) {
      console.log(`[TEST MODE] Would send email to: ${recipient}, subject: ${subject}`);
      return { success: true, testMode: true };
    }
    
    if (!client) {
      console.error('Email client not initialized, check MAIL_JET_KEY1 and MAIL_JET_KEY2');
      throw new Error('Email client not initialized');
    }
    
    const request = client.post('send', { version: 'v3.1' }).request({
      Messages: [
        {
          From: {
            Email: process.env.FROM_EMAIL || "<EMAIL>",
            Name: process.env.FROM_NAME || "BnByond"
          },
          To: [{ Email: recipient }],
          Subject: subject,
          HTMLPart: htmlContent
        }
      ]
    });
    
    const result = await request;
    return { success: true, result };
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};
