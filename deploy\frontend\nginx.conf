# http {
#     # Default MIME types
#     include       /etc/nginx/mime.types;
#     default_type  application/octet-stream;

#     # Log format and access log
#     access_log /var/log/nginx/access.log;
#     error_log /var/log/nginx/error.log;

#     # Server block that serves the React app
#     server {
#         listen 80;

#         # Serve React app from /usr/share/nginx/html
#         location / {
#             root /usr/share/nginx/html;
#             try_files $uri /index.html;
#         }

#         # Proxy API requests to the backend
#         location /api/ {
#             proxy_pass http://localhost:5000/;  # backend service (use Docker Compose service name)
#             proxy_set_header Host $host;
#             proxy_set_header X-Real-IP $remote_addr;
#             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#             proxy_set_header X-Forwarded-Proto $scheme;
#         }
#     }
# }

# /etc/nginx/nginx.conf

events {}

http {
    include       mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;

        # Server name (optional)
        server_name _;

        # Serve static files from /usr/share/nginx/html
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;

            # For single-page apps (React, Angular, etc.)
            try_files $uri /index.html;
        }

        # Optional: Error page handling (for 404 errors)
        error_page  404 /404.html;
        location = /404.html {
            root /usr/share/nginx/html;
        }

        # Optionally, add other error pages if needed
    }
}

