import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { IoMdCall, IoMdMail } from "react-icons/io";
import { HiLocationMarker } from "react-icons/hi";
// import { MdLocationOn } from "react-icons/md";
import Logo from "assets/img/logo2.png";

function Footerbn() {
  const [phone, setPhone] = useState("");

  return (
    <>
      <div
        className="md:flex bg-[#DAF4F9] border justify-center  relative overflow-hidden h-auto sm:px-0 md:pt-36 lg:pt-20 md:h-380 pb-20
      "
      >
        <div className="w-[90%] lg:w-[80%] mx-auto sm:flex flex-wrap  ">
          <div className="flex w-full px-2 md:w-4/12 sm:w-1/2 md:justify-start">
            <div className="">
              <div className="h-auto ">
                <div className="">
                  <Link
                    className="inline-block py-2 ml-2 font-bold leading-relaxed text-white uppercase whitespace-nowrap"
                    to="/"
                  >
                    <div className="">
                      <img src={Logo} alt="Logo" width="120" height="64" />
                    </div>
                  </Link>
                </div>

                <div className="ml-2 flex flex-col gap-2 justify-center">
                  <span className="flex gap-3 items-center">
                    <IoMdMail />
                    <a
                      href="mailto:<EMAIL>"
                      className="cursor-pointer hover:text-blue-500"
                    >
                      <EMAIL>
                    </a>
                  </span>
                  <span className="flex gap-3 items-center">
                    <IoMdCall size={18}/>
                    <a
                      href="tel:+18552966363"
                      className="cursor-pointer hover:text-blue-500"
                    >
                      ****************
                    </a>
                  </span>
                  <span className="flex gap-3 items-center">
                    <HiLocationMarker size={21}/>
                    <p>
                      168 Hobsons Lake Dr Suite 301, Beechville,
                      NS, Canada B3S 1A2
                    </p>
                  </span>
                </div>

                <h2
                  className={`ml-2 text-black font-Avenir text-lg font-bold leading-[116%]  mt-3`}
                >
                  Follow us
                </h2>
                <div>
                  <div className="mt-2 ml-2 text-sm cursor-pointer bn-text-col">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="205"
                      height="31"
                      viewBox="0 0 205 31"
                      fill="none"
                    >
                      <a
                        href="https://twitter.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <circle
                          cx="155"
                          cy="15.3867"
                          r="15"
                          fill="#AAC4C9"
                          className="hover:fill-blue-500"
                        />
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M153.081 15.8348L147.087 8.38672H151.834L155.534 12.9901L159.487 8.40745H162.102L156.799 14.563L163.087 22.3867H158.354L154.347 17.4084L150.07 22.3729H147.441L153.081 15.8348ZM159.043 21.0067L149.998 9.76671H151.144L160.178 21.0067H159.043Z"
                          fill="white"
                        />
                      </a>
                      <a
                        href="https://ca.pinterest.com/bnbyond/"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <circle
                          cx="120"
                          cy="15.3867"
                          r="15"
                          fill="#AAC4C9"
                          className="hover:fill-blue-500"
                        />
                        <path
                          d="M121.137 7.38672C116.478 7.38672 114 10.3724 114 13.6281C114 15.1379 114.844 17.0207 116.194 17.6179C116.399 17.7103 116.511 17.671 116.556 17.4808C116.597 17.3363 116.774 16.6404 116.86 16.312C116.887 16.2068 116.873 16.1155 116.788 16.0166C116.34 15.4981 115.984 14.5535 115.984 13.6674C115.984 11.3967 117.789 9.19197 120.861 9.19197C123.517 9.19197 125.376 10.9175 125.376 13.3858C125.376 16.175 123.9 18.1045 121.982 18.1045C120.92 18.1045 120.13 17.2715 120.381 16.2408C120.683 15.0136 121.277 13.6939 121.277 12.8088C121.277 12.0151 120.829 11.3585 119.913 11.3585C118.832 11.3585 117.956 12.4285 117.956 13.865C117.956 14.7777 118.279 15.394 118.279 15.394C118.279 15.394 117.21 19.7121 117.011 20.5186C116.675 21.884 117.057 24.0951 117.09 24.2853C117.11 24.3905 117.228 24.4234 117.294 24.3374C117.399 24.1992 118.691 22.3557 119.053 21.0233C119.185 20.5377 119.726 18.5689 119.726 18.5689C120.082 19.2117 121.109 19.7504 122.204 19.7504C125.459 19.7504 127.812 16.889 127.812 13.338C127.8 9.93362 124.887 7.38672 121.137 7.38672Z"
                          fill="white"
                        />
                      </a>
                      <a
                        href="https://www.facebook.com/profile.php?id=61567282227975"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <circle
                          cx="15"
                          cy="15.3867"
                          r="15"
                          fill="#AAC4C9"
                          className="hover:fill-blue-500"
                        />
                        <path
                          d="M13.4888 23.3191H16.3422V15.788H18.3272L18.5133 13.256H16.3422C16.3422 13.256 16.3422 12.2821 16.3422 11.8277C16.3422 11.2434 16.4663 10.9837 17.0246 10.9837C17.4588 10.9837 18.5753 10.9837 18.5753 10.9837V8.38672C18.5753 8.38672 16.9625 8.38672 16.5903 8.38672C14.4813 8.38672 13.4888 9.36057 13.4888 11.2434C13.4888 12.8664 13.4888 13.256 13.4888 13.256H12V15.788H13.4888V23.3191Z"
                          fill="white"
                        />
                      </a>
                      <a
                        href="https://www.instagram.com/bnbyond_travelfreely/?igsh=MTVqZjliajVndGlleg%3D%3D&utm_source=qr"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <circle
                          cx="50"
                          cy="15.3867"
                          r="15"
                          fill="#AAC4C9"
                          className="hover:fill-blue-500"
                        />
                        <path
                          d="M57.216 12.7659C57.1477 11.9762 57.0793 11.4019 56.8743 10.9712C56.6692 10.4686 56.4642 10.0379 56.0541 9.67895C55.644 9.2482 55.234 9.03283 54.8239 8.81746C54.3455 8.60209 53.867 8.5303 53.1152 8.45851C52.2951 8.38672 52.09 8.38672 50.108 8.38672C48.1943 8.38672 47.9209 8.38672 47.1691 8.45851C46.4173 8.45851 45.9389 8.60209 45.4605 8.74567C44.982 8.96104 44.572 9.2482 44.2302 9.67895C43.8202 10.0379 43.5468 10.4686 43.3417 10.9712C43.205 11.4737 43.0683 11.9762 43.0683 12.7659C43 13.5556 43 13.8428 43 15.8529C43 17.8631 43 18.1502 43.0683 18.9399C43.1367 19.7296 43.205 20.3039 43.4101 20.7347C43.6151 21.2372 43.8202 21.6679 44.2302 22.0269C44.6403 22.4576 45.0504 22.673 45.4605 22.8884C45.9389 23.1038 46.4173 23.1755 47.1691 23.2473C47.9209 23.3191 48.1943 23.3191 50.108 23.3191C52.0217 23.3191 52.2951 23.3191 53.0469 23.2473C53.7987 23.1755 54.3455 23.1038 54.7555 22.8884C55.234 22.673 55.644 22.4576 55.9858 22.0269C56.3958 21.5962 56.6009 21.1654 56.8059 20.7347C57.011 20.2321 57.0793 19.7296 57.1477 18.9399C57.216 18.1502 57.216 17.8631 57.216 15.8529C57.2843 13.8428 57.2843 13.5556 57.216 12.7659ZM50.108 19.7296C48.0576 19.7296 46.4173 18.0066 46.4173 15.8529C46.4173 13.6992 48.0576 11.9762 50.108 11.9762C52.1584 11.9762 53.7987 13.6992 53.7987 15.8529C53.7987 18.0066 52.1584 19.7296 50.108 19.7296ZM53.9354 12.7659C53.457 12.7659 53.0469 12.3352 53.0469 11.8327C53.0469 11.3301 53.457 10.8994 53.9354 10.8994C54.4138 10.8994 54.8239 11.3301 54.8239 11.8327C54.8239 12.407 54.4138 12.7659 53.9354 12.7659ZM52.5001 15.8529C52.5001 17.2169 51.4066 18.3656 50.108 18.3656C48.8094 18.3656 47.7159 17.2169 47.7159 15.8529C47.7842 14.4889 48.8094 13.412 50.108 13.412C51.4749 13.412 52.5001 14.4889 52.5001 15.8529Z"
                          fill="white"
                        />
                      </a>
                      <a
                        href="https://youtube.com/@bnbyond1156?si=j_TlqguEzAywYhF_"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <circle
                          cx="190"
                          cy="15.3867"
                          r="15"
                          fill="#AAC4C9"
                          className="hover:fill-blue-500"
                        />
                        <path
                          d="M198.082 11.2336C197.888 10.5114 197.319 9.94213 196.596 9.74766C195.277 9.38672 190 9.38672 190 9.38672C190 9.38672 184.724 9.38672 183.405 9.73399C182.696 9.92825 182.113 10.5116 181.919 11.2336C181.572 12.5527 181.572 15.2883 181.572 15.2883C181.572 15.2883 181.572 18.0377 181.919 19.343C182.113 20.0651 182.683 20.6344 183.405 20.8288C184.738 21.1899 190.001 21.1899 190.001 21.1899C190.001 21.1899 195.277 21.1899 196.596 20.8426C197.319 20.6483 197.888 20.079 198.082 19.3569C198.43 18.0377 198.43 15.3022 198.43 15.3022C198.43 15.3022 198.443 12.5527 198.082 11.2336ZM188.32 17.8156V12.761L192.708 15.2883L188.32 17.8156Z"
                          fill="white"
                        />
                      </a>
                      <a
                        href="https://www.linkedin.com/company/bnbyond/"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <circle
                          cx="85"
                          cy="15.3867"
                          r="15"
                          fill="#AAC4C9"
                          className="hover:fill-blue-500"
                        />
                        <path
                          d="M91.9965 21.3867L92 21.3861V16.2516C92 13.7398 91.4592 11.8049 88.5227 11.8049C87.1111 11.8049 86.1637 12.5796 85.777 13.314H85.7362V12.0394H82.9519V21.3861H85.8511V16.758C85.8511 15.5394 86.0821 14.3611 87.5912 14.3611C89.0781 14.3611 89.1002 15.7517 89.1002 16.8361V21.3867H91.9965ZM78.231 12.04H81.1337V21.3867H78.231V12.04ZM79.6812 7.38672C78.7531 7.38672 78 8.1398 78 9.06789C78 9.99597 78.7531 10.7648 79.6812 10.7648C80.6092 10.7648 81.3623 9.99597 81.3623 9.06789C81.362 8.62211 81.1848 8.19468 80.8696 7.87946C80.5544 7.56425 80.1269 7.38703 79.6812 7.38672Z"
                          fill="white"
                        />
                      </a>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex w-full p-2 md:w-2/12 sm:w-1/2 md:justify-start">
            <div>
              {/* <Link to="#"> */}
              <p
                className={`text-black font-Avenir text-base font-medium leading-[186%]`}
              >
                Explore BnByond
              </p>
              {/* </Link> */}
              {/* <Link to="/propertylisting">
                <p className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}>
                  List your property
                </p>
              </Link>
              <Link to="">
                <p className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}>
                  Book with Confidence
                </p>
              </Link> */}
              {/* <Link to="/trust-safety">
                <p className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}>
                  Trust & Safety
                </p>
              </Link> */}
              <Link to="/partner-resource">
                <p
                  className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}
                >
                  Partner resources
                </p>
              </Link>
              <Link to="/how-it-works">
                <p
                  className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}
                >
                  How it Works
                </p>
              </Link>
              {/* <Link to="/vactaion-rental">
                <p className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}>
                  Vacation rental guides
                </p>
              </Link> */}
            </div>
          </div>
          <div className="flex w-full p-2 md:w-2/12 sm:w-1/2 md:justify-start">
            <div>
              {/* <Link to="#"> */}
              <p
                className={`text-black font-Avenir text-base font-medium leading-[186%]`}
              >
                Company
              </p>
              {/* </Link> */}
              <Link to="/aboutus">
                <p
                  className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}
                >
                  About
                </p>
              </Link>
              <Link to="/careers">
                <p
                  className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}
                >
                  Careers
                </p>
              </Link>
              {/* <Link to="/affiliats">
                <p className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}>
                  Affiliates
                </p>
              </Link> */}
              {/* <Link to="/media-center">
                <p className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}>
                  Media Center
                </p>
              </Link> */}
              {/* <Link to="/advertising">
                <p className={`text-black font-Lato text-sm font-normal leading-[198%] hover:text-blue-500`}>
                  Advertising
                </p>
              </Link> */}
            </div>
          </div>

          <div className="w-full p-2 md:w-4/12 sm:w-1/2 md:justify-start">
            <p
              className={` text-black font-Avenir text-base font-medium leading-[186%]`}
            >
              Get the BnByond mobile app
            </p>

            <div className="flex my-2 space-x-2 text-black">
              <img  
                src={require("assets/img/google.png")}
                className="cursor-pointer w-24"
                alt="Google Play"
                title="This feature is coming soon!"
              />
              <img
                src={require("assets/img/apple.png")}
                className="cursor-pointer w-24"
                alt="App Store"
                title="This feature is coming soon!"
              />
            </div>
            <div className="lg:w-[300px] w-[260px] flex items-center justify-center relative z-30">
              <PhoneInput
                country={"us"}
                value={phone}
                onChange={(phone) => setPhone(phone)}
                inputStyle={{ borderRadius: "12px", width: "200px" }}
                dropdownStyle={{ top: "-215px", zIndex: 50 }}
              />
              <button
               title="This feature is coming soon!"
               className=" bg-[#E8AD21] flex justify-normal items-center m-1 lg:p-3 md:p-1 p-3 lg:w-[80px] w-[60px] lg:h-[40px] h-[30px] rounded-full">
                <div>
                  <p className="lg:text-base text-[15px] font-semibold text-center text-white uppercase font-Lato">
                    Send
                  </p>
                </div>
              </button>
            </div>
            <div className="flex justify-between mt-3">
              <p
                className={`text-black font-Lato text-sm text-[12px] leading-[198%] flex items-center`}
              >
                <span>
                  Avaliable for iOS and Android Messaging rates may apply.
                </span>{" "}
                <span>
                  <img
                    src={require("assets/img/sign.png")}
                    className=""
                    alt=""
                  />
                </span>
              </p>
            </div>
          </div>
        </div>

        <div className="absolute bottom-0 left-0 flex opacity-30">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="269"
            viewBox="0 0 32 269"
            fill="none"
          >
            <path
              d="M23.3889 192.237V184.93H14.1007L13.4012 179.038C13.4012 179.038 15.0784 177.841 15.4409 176.645C15.8033 175.448 15.0784 165.384 15.0784 165.384L16.5197 162.392L16.4017 157.478L17.7165 156.163C17.7165 154.848 15.3229 132.917 15.3229 132.917L15.9213 130.878C15.9213 130.878 18.6774 131.24 19.5202 129.319C20.3631 127.405 22.2764 117.451 22.2764 117.451C22.2764 117.451 25.2685 116.49 25.9933 115.656C26.7098 114.813 27.6706 111.821 27.6706 111.821L28.6315 111.105L28.9939 109.427C28.9939 109.427 30.4352 108.829 30.4352 107.75C30.4352 106.671 29.4743 105.112 29.4743 105.112L30.3172 103.553L29.4743 101.875L18.3318 96.2451L15.8117 100.442C15.8117 100.442 14.6149 99.5996 13.8984 98.6472C13.182 97.6863 11.1423 94.8122 9.58304 89.536C8.02377 84.2597 6.1105 83.1809 6.1105 83.1809C6.1105 83.1809 3.47238 81.0232 3.11838 80.3068C2.75595 79.5903 1.44111 74.0781 1.44111 74.0781C1.44111 74.0781 2.15753 73.3617 2.03953 72.5188C1.92153 71.676 0.724686 67.0066 0.724686 67.0066C0.724686 67.0066 1.68554 67.0066 1.80354 66.2902C1.92154 65.5738 0.960686 63.6521 0.960686 63.6521L1.6771 61.9748L11.9852 61.2584L1.92154 59.8171L1.32311 58.3758L11.3867 54.4228L0.598259 56.3361L-0.362591 55.0213L5.26765 47.3513L-3.00071 53.8244L-5.63883 52.9816L-7.5521 43.6344L-8.03253 52.9816C-9.22937 52.9816 -11.8675 53.698 -11.8675 53.698L-19.293 48.5482L-13.6628 55.0213L-15.34 56.5805L-22.1671 56.943V53.7064L-24.8052 46.6349L-25.7661 39.083C-25.7661 39.083 -25.4037 30.9326 -25.4037 30.4522C-25.4037 29.9718 -27.1989 25.3024 -27.1989 25.3024L-26.8365 21.8298C-25.7577 21.5938 -22.4031 19.0737 -22.4031 19.0737V14.2779L-25.0412 12.8366L-23.8444 10.3165L-24.0804 6.59952L-25.0412 7.79636C-25.0412 7.79636 -24.5608 6.35509 -24.5608 4.67782C-24.5608 3.00055 -25.4037 0 -25.4037 0L-26.8449 1.79527C-27.3254 2.39369 -33.554 4.31539 -33.9165 4.78739C-34.2789 5.26781 -34.9953 8.62236 -34.7593 9.22078C-34.5233 9.81921 -33.7985 11.7409 -33.7985 11.7409C-35.9562 12.1033 -37.9959 13.6542 -37.9959 13.6542V18.568L-35.7202 20.7257L-34.7593 22.167L-34.1609 23.7262L-34.6413 24.9231L-34.1609 26.3644L-34.5233 27.3252L-33.8069 28.5221L-34.0429 29.9633C-34.0429 29.9633 -33.4445 32.6014 -33.3265 33.6803C-33.2085 34.7591 -32.2476 39.3105 -32.2476 39.3105C-32.2476 39.3105 -31.5312 42.9095 -31.6492 47.0985C-31.7672 51.2959 -29.9719 61.4775 -29.9719 61.4775C-29.9719 61.4775 -32.492 65.6749 -32.964 67.8326C-33.4445 69.9903 -34.4053 76.4634 -34.1609 76.8174C-33.9249 77.1798 -32.0032 79.2111 -32.0032 79.2111C-32.0032 79.2111 -32.2392 81.2508 -32.1212 81.8492C-32.0032 82.4476 -29.8455 84.2429 -29.8455 84.2429C-29.8455 84.2429 -29.7275 93.3541 -29.4831 94.6689C-29.2471 95.9838 -27.5698 98.5039 -27.4434 99.7008C-27.3254 100.898 -31.0423 112.883 -30.7979 114.796C-30.5619 116.709 -28.4042 119.23 -27.8058 120.544C-27.2074 121.859 -29.7191 130.49 -29.7191 131.805C-29.7191 133.12 -26.9629 139.475 -26.9629 140.79C-26.9629 142.105 -29.1206 156.13 -29.1206 156.492C-29.1206 156.854 -27.3254 158.287 -27.2074 159.012C-27.0894 159.729 -29.1206 173.636 -29.1206 176.265C-29.1206 177.378 -29.5926 181.027 -30.1321 184.913H-37.8441V192.22H-46.0029V200.084H-40.3811V268.22H25.9259V200.084H31.5477V192.22H23.3974L23.3889 192.237Z"
              fill="#58C0D0"
            />
          </svg>
        </div>

        <div className="absolute bottom-0 right-0 flex opacity-30 z-10 overflow-hidden ">
          {[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1].map((_, index) => {
            return (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="126"
                height="61"
                viewBox="0 0 126 61"
                fill="none"
                key={index}
              >
                <path
                  d="M121.489 26.1691V42.443H116.626V48.8276H109.67V52.6081H106.2V37.929H104.053V36.0585H96.3053V37.929H93.1351V30.683H89.1183V28.2365L84.9046 27.8034V24.4462L79.6523 25.0713V27.2619L73.5188 26.6269V30.683H69.502V36.8904H64.5252V52.6081H58.3721V9.37324H49.0832V4.9774L46.8582 5.2383V2.79671L41.6058 3.41695V5.86346L39.3857 6.12436V9.37324H34.7389V50.3487H27.8916V10.5005H9.71259V6.8972H5.06569V3.51048L2.84563 3.23482V0.684933L-2.40675 0.0351562V2.58504L-4.62682 2.30938V6.8972H-13.9206V50.3782H-20.0738V33.9861H-25.0505V27.5031H-29.0673V23.2746L-35.2008 23.9342V21.6502L-40.4532 21.0004V24.5003L-44.662 24.9532V27.5031H-48.6788V35.0641H-51.8538V33.1148H-59.6019V35.0641H-61.7481V50.3782H-65.2186V46.4352H-72.1692V39.775H-77.0327V22.7971L-88.8517 21.025V39.775H-93.0901V33.9861H-97.9437V31.4903L-103.196 30.8406V33.9861H-104.909V39.5633H-108.749V35.0641H-120.036V60.9641H125.885V25.534L121.489 26.1691Z"
                  fill="#58C0D0"
                />
              </svg>
            );
          })}
        </div>
      </div>
      <div className="flex justify-center items-center relative w-full px-4">
        <div className="w-full lg:w-[80%] flex flex-col md:flex-row justify-center lg:justify-between items-center relative">
          <div className="absolute bottom-2 md:left-0 text-center lg:text-left">
            <p
              className="text-sm leading-6 font-normal"
              style={{ zIndex: "20" }}
            >
              © 2025 All Rights Reserved. BnByond
            </p>
          </div>
          <div className="absolute bottom-10 md:bottom-2 md:right-0 flex gap-2 lg:gap-5">
            <Link
              to="/terms"
              className="hover:text-blue-500"
              style={{ zIndex: "20" }}
            >
              <p className="text-sm leading-6 font-normal">
                Terms & Conditions
              </p>
            </Link>
            <Link
              to="/privacy"
              className="hover:text-blue-500"
              style={{ zIndex: "20" }}
            >
              <p className="text-sm leading-6 font-normal">Privacy Policy</p>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}

export default Footerbn;
