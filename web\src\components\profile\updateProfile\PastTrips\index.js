
import { Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import userServices from "services/httpService/userAuth/userServices";
import { localStorageData } from "services/auth/localStorageData";

const MyTrip = () => {
  const [history, setHistory] = useState([]);

  const getReservation = async () => {
    try {
      const userId = localStorageData("_id");
      const apiUrl = `/reservation/reservations/${userId}`;
      let res = await userServices.getReservation(apiUrl);
      const currentDate = new Date();
      const historyFilteredData =
        res.data &&
        res.data.filter((item) => {
          if (item.offerState && item.offerState.checkIn) {
            const checkInDate = new Date(item.offerState.checkIn);
            return checkInDate < currentDate;
          }
          return false;
        });

      setHistory(historyFilteredData);

    } catch (error) {
      console.error("Error fetching reservation data", error);
    }
  };

  useEffect(() => {
    getReservation();
  }, []);

  return (
    <>
      <section>
        <Container maxWidth="xl">
          <Container maxWidth="xl">
            <div className="my-4">
              <h1 className="text-xl font-medium ml-4">My Trips</h1>
            </div>
            <div className=" p-4">
              <PastTripCard
                history={history}
              />
            </div>
          </Container>
        </Container>
      </section>
    </>
  );
};

export default MyTrip;
