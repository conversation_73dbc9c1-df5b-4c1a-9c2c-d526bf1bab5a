import React, { useState, useEffect } from "react";
import Container from "@mui/material/Container";
import Rating from 'react-rating';
import { FaStar, FaStarHalfAlt, FaRegStar } from 'react-icons/fa';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import kitchen from "assets/img/kitchen.png";
import Footerbn from "pages/Footer/Footerbn";
import icon from "assets/img/icon.png"
import 'react-calendar/dist/Calendar.css';

const getImageSrc = (photo) => {
    if (typeof photo === 'string') return photo;
    if (photo?.file) return URL.createObjectURL(photo.file);
    return "/api/placeholder/400/320";
  };


function PreviewDetail() {

    const [state, setState] = useState(null);

    useEffect(() => {
        const storedState = localStorage.getItem('previewState');
        if (storedState) {
            setState(JSON.parse(storedState));
        }
    }, []);

    const hasPhotos = state.photos && state.photos.length > 0;
    
    if (!state) return <p>Loading...</p>;

    return (

        <main className="relative w-full h-full min-h-screen  ">

            {state == "" ? (
                <div className="flex justify-center items-center min-h-screen">
                    <img src={icon} className="w-28 h-28 animate-spin "></img>
                </div>
            ) : (
                <>
                    <section className="">

                        <Container style={{ width: '100%', padding: '0px', marginTop: '50px' }}  >
                            <div className="flex items-center w-[90%]  ml-2">
                                <i className="cursor-pointer fas fa-angle-left"></i>
                                <h4 className="ml-1 md:ml-2 text-2xl">{state.address}</h4>
                            </div>
                            <div className="flex items-center justify-between px-3 pb-5 ml-1 md:ml-3 mr-2 md:mr-4">
                                <div className="flex items-center">
                                    <Rating
                                        initialRating={3.5}
                                        readonly
                                        emptySymbol={<FaRegStar color="#d3d3d3" />}
                                        halfSymbol={<FaStarHalfAlt color="#ffd700" />}
                                        fullSymbol={<FaStar color="#ffd700" />}
                                        fractions={2}
                                    />
                                    <span className="ml-2 text-sm">(3.5)</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <span className={`fa-regular ${"text-color-red fa-solid"} fa-heart w-[17.14px] h-[15.7px] cursor-pointer`}></span>
                                    <button className="underline">
                                        Save
                                    </button>
                                </div>
                            </div>
                            <Container style={{ width: '100%' }} >

                                <div className="">
                                    <div className="md:flex md:w-[100%] mx-auto h-[450px] gap-4 overflow-hidden rounded-lg">
                                        <div className="md:w-1/2 w-full">
                                        {hasPhotos && (
                                                <img
                                                    src={getImageSrc(state.photos[0])}
                                                    className="h-full w-full object-cover"
                                                    alt="Property view 1"
                                                />
                                            )}
                                        </div>
                                        <div className="flex flex-col w-full gap-4 ">
                                            <div className="flex w-full gap-4">
                                                <div className="w-full">
                                                {hasPhotos && (
                                                        <img
                                                            src={getImageSrc(state.photos[1])}
                                                            className="h-[223px] w-full object-cover"
                                                            alt="Property view 2"
                                                        />
                                                    )}
                                                </div>
                                                <div className="w-full">
                                                {hasPhotos && (
                                                        <img
                                                            src={getImageSrc(state.photos[2])}
                                                            className="h-[223px] w-full object-cover"
                                                            alt="Property view 3"
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex w-full gap-4">
                                                <div className="w-full">
                                                {hasPhotos && (
                                                        <img
                                                            src={getImageSrc(state.photos[3])}
                                                            className="h-[223px] w-full object-cover"
                                                            alt="Property view 4"
                                                        />
                                                    )}
                                                </div>
                                                <div className="w-full">
                                                     {hasPhotos && (
                                                        <img
                                                            src={getImageSrc(state.photos[4])}
                                                            className="h-[223px] w-full object-cover"
                                                            alt="Property view 5"
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </Container>
                        </Container>
                    </section>

                    <section>
                        <Container className="w-[90%]">
                            <div className="pb-2 mb-2 border-b md:flex detailpage-wrapper border-b-color-grey">
                                <div className="w-full dp-item-wrapper ">


                                    <div className="md:flex justify-between pt-5">

                                        <div className="w-full lg:w-[65%] md:w-[55%]">
                                            <div className="">
                                                <h3 className="text-2xl">{state.title}</h3>
                                                {state.spaceType && (
                                                    <div className="lg:px-2 py-2 border-b flex flex-wrap  justify-between border-b-color-green  ">
                                                        <div className="flex items-center md:text-center lg:text-start lg:pr-5 md:pr-2 pr-2 md:py-0 py-5 ">
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                width="32"
                                                                height="32"
                                                                viewBox="0 0 35 35"
                                                                fill="none"
                                                            >
                                                                <path
                                                                    d="M17.0857 8.24895C19.2167 8.24895 20.9484 6.5151 20.9484 4.3863C20.9484 2.25751 19.2167 0.521484 17.0857 0.521484C14.9548 0.521484 13.2231 2.25534 13.2231 4.38413C13.2231 6.51293 14.9569 8.24678 17.0857 8.24678V8.24895ZM17.0857 2.06654C18.3639 2.06654 19.4033 3.10599 19.4033 4.38413C19.4033 5.66228 18.3639 6.70172 17.0857 6.70172C15.8076 6.70172 14.7681 5.66228 14.7681 4.38413C14.7681 3.10599 15.8076 2.06654 17.0857 2.06654ZM20.1758 9.79401H13.9934C11.4371 9.79401 9.35608 11.8729 9.35608 14.4314V22.1588C9.35608 23.437 10.3955 24.4764 11.6737 24.4764H12.4462V31.8176C12.4462 33.3084 13.6592 34.5215 15.1501 34.5215C15.9074 34.5215 16.591 34.2068 17.0814 33.7034C17.5892 34.2264 18.2857 34.5215 19.0127 34.5215C20.5035 34.5215 21.7166 33.3084 21.7166 31.8176V24.4764H22.4891C23.7672 24.4764 24.8067 23.437 24.8067 22.1588V14.4314C24.8067 11.8751 22.7278 9.79401 20.1693 9.79401H20.1758ZM23.266 22.1567C23.266 22.582 22.9188 22.9292 22.4934 22.9292H21.7209V15.2017C21.7209 14.7742 21.3759 14.4292 20.9484 14.4292C20.5209 14.4292 20.1758 14.7742 20.1758 15.2017V31.8155C20.1758 32.4534 19.655 32.9743 19.017 32.9743C18.3791 32.9743 17.8582 32.4534 17.8582 31.8155V23.7017C17.8582 23.2742 17.5132 22.9292 17.0857 22.9292C16.6582 22.9292 16.3132 23.2742 16.3132 23.7017V31.8155C16.3132 32.4534 15.7924 32.9743 15.1544 32.9743C14.5164 32.9743 13.9956 32.4534 13.9956 31.8155V15.2017C13.9956 14.7742 13.6506 14.4292 13.2231 14.4292C12.7956 14.4292 12.4505 14.7742 12.4505 15.2017V22.9292H11.678C11.2527 22.9292 10.9055 22.582 10.9055 22.1567V14.4292C10.9055 12.7236 12.2921 11.3391 13.9956 11.3391H20.178C21.8836 11.3391 23.2681 12.7257 23.2681 14.4292V22.1567H23.266ZM6.26813 9.79401C7.97377 9.79401 9.35825 8.40737 9.35825 6.70389C9.35825 5.00042 7.9716 3.61377 6.26813 3.61377C4.56466 3.61377 3.17801 5.00042 3.17801 6.70389C3.17801 8.40737 4.56466 9.79401 6.26813 9.79401ZM6.26813 5.15666C7.12095 5.15666 7.81319 5.8489 7.81319 6.70172C7.81319 7.55454 7.12095 8.24678 6.26813 8.24678C5.41531 8.24678 4.72307 7.55454 4.72307 6.70172C4.72307 5.8489 5.41531 5.15666 6.26813 5.15666ZM10.1308 26.0844V31.4292C10.1308 32.7073 9.09134 33.7468 7.81319 33.7468C7.21643 33.7468 6.67827 33.5146 6.26813 33.1435C5.858 33.5146 5.31766 33.7468 4.72307 33.7468C3.44492 33.7468 2.40548 32.7073 2.40548 31.4292V24.4743C1.12733 24.4743 0.0878906 23.4348 0.0878906 22.1567V15.9743C0.0878906 13.418 2.16678 11.3369 4.72524 11.3369H7.81536C8.24286 11.3369 8.58789 11.6819 8.58789 12.1094C8.58789 12.5369 8.24286 12.882 7.81536 12.882H4.72524C3.0196 12.882 1.63512 14.2686 1.63512 15.9721V22.1545C1.63512 22.5798 1.98232 22.927 2.40765 22.927V19.0644C2.40765 18.6369 2.75268 18.2918 3.18018 18.2918C3.60768 18.2918 3.95271 18.6369 3.95271 19.0644V31.427C3.95271 31.8523 4.29991 32.1996 4.72524 32.1996C5.15057 32.1996 5.49777 31.8523 5.49777 31.427V28.3369C5.49777 27.9094 5.8428 27.5644 6.2703 27.5644C6.6978 27.5644 7.04283 27.9094 7.04283 28.3369V31.427C7.04283 31.8523 7.39003 32.1996 7.81536 32.1996C8.24069 32.1996 8.58789 31.8523 8.58789 31.427V26.0822C8.58789 25.6547 8.93293 25.3097 9.36042 25.3097C9.78792 25.3097 10.1329 25.6547 10.1329 26.0822L10.1308 26.0844ZM27.9033 9.79401C29.609 9.79401 30.9934 8.40737 30.9934 6.70389C30.9934 5.00042 29.6068 3.61377 27.9033 3.61377C26.1998 3.61377 24.8132 5.00042 24.8132 6.70389C24.8132 8.40737 26.1998 9.79401 27.9033 9.79401ZM27.9033 5.15666C28.7561 5.15666 29.4484 5.8489 29.4484 6.70172C29.4484 7.55454 28.7561 8.24678 27.9033 8.24678C27.0505 8.24678 26.3582 7.55454 26.3582 6.70172C26.3582 5.8489 27.0505 5.15666 27.9033 5.15666ZM34.0857 15.9743V22.1567C34.0857 23.4348 33.0463 24.4743 31.7681 24.4743V31.4292C31.7681 32.7073 30.7287 33.7468 29.4505 33.7468C28.8538 33.7468 28.3156 33.5146 27.9055 33.1435C27.4953 33.5146 26.955 33.7468 26.3604 33.7468C25.0823 33.7468 24.0428 32.7073 24.0428 31.4292V26.0844C24.0428 25.6569 24.3879 25.3119 24.8154 25.3119C25.2429 25.3119 25.5879 25.6569 25.5879 26.0844V31.4292C25.5879 31.8545 25.9351 32.2017 26.3604 32.2017C26.7857 32.2017 27.1329 31.8545 27.1329 31.4292V28.3391C27.1329 27.9116 27.478 27.5665 27.9055 27.5665C28.333 27.5665 28.678 27.9116 28.678 28.3391V31.4292C28.678 31.8545 29.0252 32.2017 29.4505 32.2017C29.8759 32.2017 30.2231 31.8545 30.2231 31.4292V19.0665C30.2231 18.639 30.5681 18.294 30.9956 18.294C31.4231 18.294 31.7681 18.639 31.7681 19.0665V22.9292C32.1935 22.9292 32.5407 22.582 32.5407 22.1567V15.9743C32.5407 14.2686 31.154 12.8841 29.4505 12.8841H26.3604C25.9329 12.8841 25.5879 12.5391 25.5879 12.1116C25.5879 11.6841 25.9329 11.3391 26.3604 11.3391H29.4505C32.0068 11.3391 34.0879 13.418 34.0879 15.9764L34.0857 15.9743Z"
                                                                    fill="#58C0D0"
                                                                />
                                                            </svg>
                                                            <span className="ml-2 text-sm">
                                                                {state.spaceTypeDetail.guests} guests
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center md:text-center lg:text-start lg:pr-5 md:pr-2 pr-2 md:py-0 py-5 ">
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                width="34"
                                                                height="32"
                                                                viewBox="0 0 34 32"
                                                                fill="none"
                                                            >
                                                                <path
                                                                    d="M30.9905 15.7259V5.45531C30.9905 2.89441 28.9066 0.810547 26.3457 0.810547H6.69484C4.13395 0.810547 2.05008 2.89441 2.05008 5.45531V15.8592C0.785087 17.1956 0.0878906 18.9415 0.0878906 20.7801V28.598C0.0878906 30.087 1.29881 31.2979 2.78784 31.2979C4.27686 31.2979 5.48779 30.087 5.48779 28.598V27.8873H27.688V28.598C27.688 30.087 28.8989 31.2979 30.3879 31.2979C31.877 31.2979 33.0879 30.087 33.0879 28.598V20.7801C33.0879 18.872 32.3443 17.0778 30.9963 15.724L30.9905 15.7259ZM3.52366 5.45531C3.52366 4.6094 3.85391 3.81371 4.45261 3.21501C5.05131 2.61631 5.84701 2.28605 6.69291 2.28605H26.3457C28.0936 2.28605 29.5169 3.70749 29.5169 5.45724V14.5884C29.179 14.3914 28.8255 14.2234 28.4624 14.0843V12.0198C28.4624 10.0769 26.8826 8.49709 24.9398 8.49709H19.3699C18.2826 8.49709 17.2513 9.00695 16.585 9.86638C15.9187 9.00695 14.8874 8.49709 13.8001 8.49709H8.23022C6.28734 8.49709 4.70754 10.0769 4.70754 12.0198V14.0843C4.29618 14.2408 3.89833 14.4358 3.52173 14.6676V5.45531H3.52366ZM26.9869 12.0178V13.6942C26.6335 13.6401 26.2743 13.615 25.9189 13.615H17.3247V12.0178C17.3247 10.888 18.244 9.97067 19.3718 9.97067H24.9417C26.0696 9.97067 26.9889 10.89 26.9889 12.0178H26.9869ZM15.8492 12.0178V13.615H7.25492C6.89956 13.615 6.54034 13.6421 6.18691 13.6942V12.0178C6.18691 10.888 7.10621 9.97067 8.23408 9.97067H13.8039C14.9318 9.97067 15.8511 10.89 15.8511 12.0178H15.8492ZM4.01228 28.598C4.01228 29.2739 3.46379 29.8224 2.78784 29.8224C2.11189 29.8224 1.5634 29.2739 1.5634 28.598V27.8873H4.01228V28.598ZM31.6105 28.598C31.6105 29.2739 31.062 29.8224 30.386 29.8224C29.7101 29.8224 29.1616 29.2739 29.1616 28.598V27.8873H31.6105V28.598ZM31.6105 26.4137H1.56147V24.0595H31.6085V26.4137H31.6105ZM31.6105 22.5859H1.56147V20.782C1.56147 17.6437 4.11464 15.0905 7.25299 15.0905H25.9189C29.0573 15.0905 31.6105 17.6437 31.6105 20.782V22.5859Z"
                                                                    fill="#58C0D0"
                                                                />
                                                            </svg>
                                                            <span className="ml-2 text-sm">
                                                                {state.spaceTypeDetail.bedrooms} bedrooms
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center md:text-center lg:text-start lg:pr-5 md:pr-2 pr-2 md:py-0 py-5 ">
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                width="33"
                                                                height="30"
                                                                viewBox="0 0 33 30"
                                                                fill="none"
                                                            >
                                                                <path
                                                                    d="M19.2488 13.4673C19.4618 13.4673 19.6477 13.3763 19.7717 13.2407C19.8976 13.1051 19.9673 12.9289 19.9693 12.7468V12.1677C19.9693 11.9663 19.8995 11.7803 19.7678 11.6486C19.6361 11.5169 19.4502 11.4472 19.2488 11.4472C19.0473 11.4472 18.8614 11.5169 18.7297 11.6486C18.598 11.7803 18.5283 11.9663 18.5283 12.1677V12.7468C18.5283 12.9482 18.598 13.1342 18.7297 13.2659C18.8614 13.3976 19.0473 13.4673 19.2488 13.4673ZM21.356 10.2115C21.5574 10.2115 21.7434 10.1418 21.8751 10.0101C22.0068 9.87838 22.0765 9.69244 22.0765 9.49101V9.22761C22.0765 9.02618 22.0068 8.84024 21.8751 8.70854C21.7434 8.57684 21.5574 8.50711 21.356 8.50711C21.1546 8.50711 20.9687 8.57684 20.837 8.70854C20.7052 8.84024 20.6355 9.02618 20.6355 9.22761V9.49101C20.6355 9.69244 20.7052 9.87838 20.837 10.0101C20.9687 10.1418 21.1546 10.2115 21.356 10.2115ZM19.2488 10.9572C19.4502 10.9572 19.6361 10.8875 19.7678 10.7558C19.8995 10.624 19.9693 10.4381 19.9693 10.2367V9.97328C19.9693 9.77185 19.8995 9.58592 19.7678 9.45421C19.6361 9.32251 19.4502 9.25279 19.2488 9.25279C19.0473 9.25279 18.8614 9.32251 18.7297 9.45421C18.598 9.58592 18.5283 9.77185 18.5283 9.97328V10.2367C18.5283 10.4381 18.598 10.624 18.7297 10.7558C18.8614 10.8875 19.0473 10.9572 19.2488 10.9572ZM20.6355 11.914C20.6355 12.1154 20.7052 12.3013 20.837 12.433C20.9687 12.5647 21.1546 12.6345 21.356 12.6345C21.5691 12.6345 21.755 12.5434 21.879 12.4079C22.0049 12.2723 22.0746 12.096 22.0765 11.914V11.3349C22.0765 11.1334 22.0068 10.9475 21.8751 10.8158C21.7434 10.6841 21.5574 10.6144 21.356 10.6144C21.1546 10.6144 20.9687 10.6841 20.837 10.8158C20.7052 10.9475 20.6355 11.1334 20.6355 11.3349V11.914ZM32.0879 17.6566C32.0879 18.1544 31.8961 18.5998 31.5766 18.9194C31.257 19.239 30.8115 19.4307 30.3138 19.4307H29.8451L28.4467 24.9758C28.038 26.5078 26.6997 27.6564 25.1096 27.8346L25.6441 28.9037L25.648 28.9114C25.6867 29.0063 25.7022 29.1051 25.7022 29.2019C25.7022 29.3337 25.6712 29.4615 25.6112 29.5719C25.5608 29.6648 25.4892 29.7481 25.3962 29.8082C25.373 29.845 25.3439 29.8798 25.3032 29.9069C25.2432 29.9476 25.1773 29.9612 25.1309 29.9651C25.0979 29.967 25.0708 29.967 25.0456 29.967C24.9081 29.967 24.7745 29.9302 24.6622 29.8547C24.5498 29.7791 24.4627 29.6707 24.3987 29.5428L23.5562 27.8578H9.17347L8.29997 29.6048L8.29416 29.6145C8.22831 29.7133 8.14115 29.8024 8.03269 29.8663C7.92423 29.9302 7.7964 29.9651 7.65889 29.9651C7.63177 29.9651 7.58141 29.965 7.52137 29.9554C7.47295 29.9457 7.41097 29.9302 7.34706 29.8837C7.11077 29.7752 6.96744 29.5409 6.96938 29.293C6.96938 29.1768 7.00037 29.0567 7.06429 28.9463L7.60853 27.8559H7.50007C5.72401 27.8559 4.17263 26.6357 3.7291 24.9177L2.34428 19.4249H1.86201C1.36425 19.4249 0.918783 19.2332 0.599209 18.9136C0.279635 18.6018 0.0878906 18.1544 0.0878906 17.6566C0.0878906 17.1588 0.279635 16.7134 0.599209 16.3938C0.895541 16.0975 1.30421 15.9115 1.75936 15.8883C2.06925 14.6623 3.16548 13.7752 4.49607 13.7752C4.94929 13.7752 5.38701 13.905 5.78405 14.0851C6.26826 13.2678 7.17468 12.7236 8.18376 12.7216C9.06308 12.7216 9.82812 13.1497 10.3801 13.7791C11.6681 13.8159 12.7217 14.6875 13.0277 15.8825H28.5396V5.01117C28.5396 3.0608 26.9941 1.51716 25.0456 1.51716C24.1082 1.5191 23.4265 1.87547 22.9403 2.43133C22.4891 2.94846 22.216 3.64959 22.1211 4.39526C23.3219 4.72065 24.1876 5.8072 24.1857 7.12036C24.1857 7.32178 24.116 7.50772 23.9843 7.63942C23.8526 7.77112 23.6666 7.84085 23.4652 7.84085H19.2507C19.0493 7.84085 18.8633 7.77112 18.7316 7.63942C18.5999 7.50772 18.5302 7.32178 18.5302 7.12036C18.5302 5.77815 19.4308 4.6761 20.6723 4.37589C20.7924 3.13246 21.2631 2.07302 22.0049 1.31573C22.7738 0.529386 23.8313 0.0761719 25.0456 0.0761719C26.415 0.0761719 27.6507 0.62429 28.5416 1.51522C29.4325 2.40616 29.9806 3.64184 29.9806 5.01117V15.8825H30.3138C30.8115 15.8825 31.257 16.0742 31.5766 16.3938C31.8961 16.7134 32.0879 17.1588 32.0879 17.6566ZM28.3305 19.4307H3.85112L5.15846 24.5613C5.45286 25.6382 6.43289 26.4207 7.55236 26.4207H24.728C25.8475 26.4207 26.8275 25.6382 27.1219 24.5633L28.3285 19.4327L28.3305 19.4307ZM3.3243 15.8825H5.7918C5.72789 15.8457 5.67172 15.7973 5.62523 15.7372C5.28048 15.3964 4.91249 15.2162 4.49801 15.2162C3.9925 15.2162 3.56253 15.4758 3.32624 15.8825H3.3243ZM10.1864 15.2162C10.1496 15.222 10.1109 15.2298 10.0741 15.2298C9.94626 15.2298 9.82037 15.1949 9.70803 15.131C9.5957 15.0671 9.49692 14.9722 9.43301 14.8463C9.31873 14.6178 9.15023 14.4512 8.94493 14.3369C8.73963 14.2226 8.49559 14.1645 8.23606 14.1645C7.88937 14.1645 7.58141 14.2904 7.33738 14.5054C7.09334 14.7204 6.91709 15.0245 6.84543 15.3789V15.3847L6.84349 15.3886C6.8125 15.5126 6.76408 15.6307 6.6808 15.7353C6.63238 15.7953 6.56846 15.8476 6.4968 15.8864H11.517C11.2807 15.4796 10.8488 15.2201 10.3452 15.2201H10.1864V15.2162ZM1.52888 17.6566C1.52888 17.7728 1.56568 17.8484 1.61797 17.9026C1.67027 17.9549 1.74774 17.9897 1.86395 17.9917H30.3157C30.4319 17.9917 30.5075 17.9549 30.5617 17.9026C30.614 17.8503 30.6488 17.7728 30.6508 17.6566C30.6508 17.5404 30.614 17.4649 30.5617 17.4106C30.5094 17.3583 30.4319 17.3235 30.3157 17.3215H1.86201C1.7458 17.3215 1.67027 17.3583 1.61603 17.4106C1.56374 17.4629 1.52888 17.5404 1.52694 17.6566H1.52888ZM21.4102 5.73166C20.9047 5.73166 20.436 5.99507 20.1881 6.39792H22.582C22.3457 5.99119 21.9138 5.73166 21.4102 5.73166ZM22.7447 12.5512V12.7449C22.7447 12.9463 22.8144 13.1322 22.9461 13.2639C23.0778 13.3956 23.2638 13.4654 23.4652 13.4654C23.6783 13.4654 23.8642 13.3743 23.9881 13.2387C24.114 13.1032 24.1838 12.9269 24.1857 12.7449V12.1657C24.1857 11.9643 24.116 11.7784 23.9843 11.6467C23.8526 11.515 23.6666 11.4453 23.4652 11.4453C23.2638 11.4453 23.0778 11.515 22.9461 11.6467C22.8144 11.7784 22.7447 11.9643 22.7447 12.1657V12.5512ZM23.4652 10.9552C23.6666 10.9552 23.8526 10.8855 23.9843 10.7538C24.116 10.6221 24.1857 10.4362 24.1857 10.2347V9.97134C24.1857 9.76991 24.116 9.58398 23.9843 9.45228C23.8526 9.32057 23.6666 9.25085 23.4652 9.25085C23.2638 9.25085 23.0778 9.32057 22.9461 9.45228C22.8144 9.58398 22.7447 9.76991 22.7447 9.97134V10.2347C22.7447 10.4362 22.8144 10.6221 22.9461 10.7538C23.0778 10.8855 23.2638 10.9552 23.4652 10.9552ZM20.6375 13.967C20.6375 14.1684 20.7072 14.3543 20.8389 14.4861C20.9706 14.6178 21.1565 14.6875 21.358 14.6875C21.5594 14.6875 21.7453 14.6178 21.877 14.4861C22.0087 14.3543 22.0784 14.1684 22.0784 13.967V13.7036C22.0784 13.5022 22.0087 13.3162 21.877 13.1845C21.7453 13.0528 21.5594 12.9831 21.358 12.9831C21.1565 12.9831 20.9706 13.0528 20.8389 13.1845C20.7072 13.3162 20.6375 13.5022 20.6375 13.7036V13.967Z"
                                                                    fill="#58C0D0"
                                                                />
                                                            </svg>
                                                            <span className="ml-2 text-sm">
                                                                {state.spaceTypeDetail.bathrooms} bathrooms
                                                            </span>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="mt-2 ">
                                                <div className="flex items-center">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="28"
                                                        height="28"
                                                        viewBox="0 0 28 28"
                                                        fill="none"
                                                    >
                                                        <path
                                                            d="M18.7257 10.4108C18.8674 10.5525 18.9453 10.7422 18.9453 10.9458C18.9453 11.1494 18.8674 11.3391 18.7257 11.4808L12.5713 17.6352C12.2859 17.9206 11.7888 17.9206 11.5033 17.6352L8.42115 14.553C8.27942 14.4112 8.20157 14.2216 8.20157 14.018C8.20157 13.8144 8.27942 13.6247 8.42115 13.483C8.56687 13.3393 8.76051 13.2654 8.95614 13.2654C9.15177 13.2654 9.3454 13.3373 9.49113 13.483L12.0403 16.0322L17.6597 10.4128C17.9492 10.1233 18.4382 10.1233 18.7277 10.4128L18.7257 10.4108ZM17.6916 25.0112L15.6774 26.746C15.1145 27.239 14.3699 27.5125 13.5794 27.5125C12.7889 27.5125 12.0483 27.245 11.4794 26.7579L9.46717 25.0272C9.23362 24.8216 8.68266 24.622 8.35527 24.622H6.16342C4.39076 24.622 2.94949 23.1807 2.94949 21.408V19.2301C2.94949 18.9088 2.74787 18.3658 2.54625 18.1322L0.827494 16.106C-0.158644 14.9383 -0.158644 13.1097 0.827494 11.9439L2.54625 9.91973C2.75186 9.68617 2.95148 9.1412 2.95148 8.81981V6.62795C2.95148 4.8553 4.39276 3.41402 6.16541 3.41402H8.36925C8.67268 3.41402 9.23162 3.2124 9.48115 3.00879L11.4933 1.27606C12.6671 0.269959 14.5057 0.269959 15.6794 1.27606L17.6916 3.00679C17.9252 3.2124 18.4762 3.41202 18.8035 3.41202H20.9694C22.7421 3.41202 24.1834 4.8533 24.1834 6.62595V8.79186C24.1834 9.11126 24.391 9.66222 24.6006 9.90376L26.3333 11.916C27.3394 13.0897 27.3394 14.9283 26.3333 16.1021L24.6026 18.1143C24.391 18.3558 24.1834 18.9068 24.1834 19.2262V21.3921C24.1834 23.1647 22.7421 24.606 20.9694 24.606H18.8035C18.5001 24.606 17.9412 24.8076 17.6916 25.0112ZM16.7155 23.8754C17.2545 23.4222 18.1368 23.0948 18.8175 23.0948H20.9834C21.9216 23.0948 22.6862 22.3303 22.6862 21.3921V19.2262C22.6862 18.5335 23.0136 17.6491 23.4667 17.1261L25.1995 15.1119C25.7145 14.5091 25.7145 13.493 25.1995 12.8901L23.4667 10.8779C23.0156 10.3549 22.6862 9.47258 22.6862 8.77789V6.62396C22.6862 5.68573 21.9216 4.92117 20.9834 4.92117H18.8175C18.1248 4.92117 17.2405 4.59379 16.7175 4.14065L14.7033 2.40792C14.1004 1.89489 13.0843 1.89489 12.4815 2.40792L10.4713 4.15063C9.95026 4.5878 9.04797 4.91918 8.37124 4.91918H6.16741C5.22918 4.91918 4.46462 5.68373 4.46462 6.62196V8.79985C4.46462 9.47457 4.13525 10.3649 3.69608 10.8739L1.97732 12.9001C1.46629 13.499 1.46629 14.5091 1.97732 15.1079L3.69608 17.1321C4.14123 17.6611 4.46462 18.5355 4.46462 19.2082V21.3861C4.46462 22.3243 5.22918 23.0889 6.16741 23.0889H8.37124C9.06394 23.0889 9.94827 23.4162 10.4713 23.8694L12.4855 25.6021C12.7869 25.8596 13.1901 25.9874 13.5954 25.9874C14.0006 25.9874 14.4058 25.8596 14.7073 25.6021L16.7195 23.8714L16.7155 23.8754Z"
                                                            fill="black"
                                                        />
                                                    </svg>
                                                    <h3 className="ml-2 text-xl font-semibold">
                                                        Vetted by our experts
                                                    </h3>
                                                </div>
                                                <p className="text-sm text-[#4B4B4B] mt-1">
                                                    Found in the small and traditional village of
                                                    Piskopiano, this two-storey Hersonissos home offers
                                                    plenty of Cycladic style. We love the terrace, and the
                                                    inclusion of a hot tub is a nice touch – though it can
                                                    be seen from nearby restaurant terraces, it's a great
                                                    spot for catching the sun. With plenty of parking space,
                                                    the home can be turned into a base for exploring all of
                                                    Crete, while nearby you'll find the lively downtown area
                                                    of Hersonissos just a twenty-minute walk away.
                                                </p>

                                                <h3 className="mt-2 text-xl font-semibold">
                                                    About this place
                                                </h3>
                                                <p className="text-sm text-[#4B4B4B] mt-1" style={{ whiteSpace: 'pre-wrap' }}>
                                                    {state.description}
                                                </p>
                                                <h3 className="mt-2 font-semibold text-md">The space</h3>
                                                <p className="text-sm text-[#4B4B4B] mt-1" style={{ whiteSpace: 'pre-wrap' }}>
                                                    {state.listing}
                                                </p>

                                            </div>
                                        </div>


                                    </div>

                                </div>

                            </div>
                            <div>
                                <h3 className="mt-2 text-xl font-semibold">
                                    What this place offers
                                </h3>
                                <div className="mb-10">
                                    <div className="grid grid-cols-2 gap-2 ">
                                        {state.amenities.map((item, index) => (
                                            <div className="flex mx-2 my-3" key={index}>
                                                <img src={item.img} alt={item.text} className="w-6 h-6 mr-2" />
                                                <span className="text-black">{item.label}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </Container>

                    </section>

                </>
            )
            }

            <Footerbn />
        </main >
    );
}

export default PreviewDetail;